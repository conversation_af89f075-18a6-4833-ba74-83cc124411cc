# 完整的构建和部署脚本
Write-Host "开始构建项目..." -ForegroundColor Green

# 1. 构建项目
npm run build

if ($LASTEXITCODE -eq 0) {
    Write-Host "构建成功！" -ForegroundColor Green
    
    # 2. 更新静态资源
    Write-Host "正在更新静态资源..." -ForegroundColor Yellow
    
    if (Test-Path "dist\pc\static") {
        Remove-Item -Path "dist\pc\static" -Recurse -Force
    }

    if (Test-Path "dist\mobile\static") {
        Remove-Item -Path "dist\mobile\static" -Recurse -Force
    }

    Copy-Item -Path "dist\static" -Destination "dist\pc\static" -Recurse
    Copy-Item -Path "dist\static" -Destination "dist\mobile\static" -Recurse
    
    Write-Host "静态资源更新完成！" -ForegroundColor Green
    
    # 3. 启动服务器
    Write-Host "正在启动服务器..." -ForegroundColor Cyan
    serve -s dist
    
} else {
    Write-Host "构建失败！请检查错误信息。" -ForegroundColor Red
}
