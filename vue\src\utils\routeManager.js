/**
 * 路由管理工具
 * 用于处理PC端和移动端之间的智能跳转
 */

// PC端和移动端路由映射关系
const routeMapping = {
  // PC端到移动端的映射
  pcToMobile: {
    '/pc/login': '/mobile/login',
    '/pc/home': '/mobile/home',
    '/pc/detail': '/mobile/detail',
    '/pc/cultiver': '/mobile/home', // PC的cultiver对应移动端的home
    '/data/history': '/mobile/home', // PC的数据页面对应移动端首页
    '/data/report': '/mobile/home',
    '/data/tousi': '/mobile/home',
    '/device/manage': '/mobile/detail', // PC的设备管理对应移动端设备列表
    '/device/camera': '/mobile/detail',
    '/user/add': '/mobile/home',
    '/register': '/mobile/register'
  },
  
  // 移动端到PC端的映射
  mobileToPc: {
    '/mobile/login': '/pc/login',
    '/mobile/home': '/pc/home',
    '/mobile/detail': '/pc/detail',
    '/mobile/register': '/pc/register',
    '/mobile/list': '/pc/home',
    '/mobile/device-detail': '/pc/detail',
    '/mobile/section-title': '/pc/home'
  }
}

// 默认跳转页面
const defaultRoutes = {
  pc: '/pc/home',
  mobile: '/mobile/home'
}

/**
 * 根据当前路由和目标设备类型获取对应的路由
 * @param {string} currentPath 当前路由路径
 * @param {boolean} isMobile 是否为移动设备
 * @param {Object} query 路由查询参数
 * @returns {Object} 包含path和query的路由对象
 */
export function getTargetRoute(currentPath, isMobile, query = {}) {
  let targetPath
  
  if (isMobile) {
    // 需要跳转到移动端
    if (currentPath.startsWith('/mobile/')) {
      // 已经是移动端路由，不需要跳转
      return null
    }
    
    // 查找对应的移动端路由
    targetPath = routeMapping.pcToMobile[currentPath]
    
    if (!targetPath) {
      // 没找到对应路由，使用默认移动端路由
      targetPath = defaultRoutes.mobile
    }
  } else {
    // 需要跳转到PC端
    if (currentPath.startsWith('/pc/') || 
        currentPath.startsWith('/data/') || 
        currentPath.startsWith('/device/') || 
        currentPath.startsWith('/user/')) {
      // 已经是PC端路由，不需要跳转
      return null
    }
    
    // 查找对应的PC端路由
    targetPath = routeMapping.mobileToPc[currentPath]
    
    if (!targetPath) {
      // 没找到对应路由，使用默认PC端路由
      targetPath = defaultRoutes.pc
    }
  }
  
  return {
    path: targetPath,
    query: query
  }
}

/**
 * 检查是否需要进行设备切换跳转
 * @param {string} currentPath 当前路由路径
 * @param {boolean} isMobile 是否为移动设备
 * @returns {boolean} 是否需要跳转
 */
export function shouldRedirect(currentPath, isMobile) {
  if (isMobile) {
    // 移动设备访问PC页面需要跳转
    return currentPath.startsWith('/pc/') || 
           currentPath.startsWith('/data/') || 
           currentPath.startsWith('/device/') || 
           currentPath.startsWith('/user/')
  } else {
    // PC设备访问移动端页面需要跳转
    return currentPath.startsWith('/mobile/')
  }
}

/**
 * 执行设备切换跳转
 * @param {Object} router Vue Router实例
 * @param {string} currentPath 当前路由路径
 * @param {boolean} isMobile 是否为移动设备
 * @param {Object} query 路由查询参数
 * @returns {Promise} 路由跳转Promise
 */
export function performDeviceRedirect(router, currentPath, isMobile, query = {}) {
  const targetRoute = getTargetRoute(currentPath, isMobile, query)
  
  if (!targetRoute) {
    return Promise.resolve()
  }
  
  console.log(`设备切换: ${currentPath} -> ${targetRoute.path}`)
  
  return router.replace({
    path: targetRoute.path,
    query: targetRoute.query
  }).catch(error => {
    // 如果跳转失败，尝试跳转到默认页面
    console.warn('路由跳转失败，尝试跳转到默认页面:', error)
    const defaultPath = isMobile ? defaultRoutes.mobile : defaultRoutes.pc
    return router.replace({
      path: defaultPath,
      query: targetRoute.query
    })
  })
}

/**
 * 获取当前设备类型对应的登录页面
 * @param {boolean} isMobile 是否为移动设备
 * @returns {string} 登录页面路径
 */
export function getLoginRoute(isMobile) {
  return isMobile ? '/mobile/login' : '/pc/login'
}

/**
 * 获取当前设备类型对应的首页
 * @param {boolean} isMobile 是否为移动设备
 * @returns {string} 首页路径
 */
export function getHomeRoute(isMobile) {
  return isMobile ? '/mobile/home' : '/pc/home'
}

/**
 * 添加新的路由映射
 * @param {string} pcRoute PC端路由
 * @param {string} mobileRoute 移动端路由
 */
export function addRouteMapping(pcRoute, mobileRoute) {
  routeMapping.pcToMobile[pcRoute] = mobileRoute
  routeMapping.mobileToPc[mobileRoute] = pcRoute
}

/**
 * 移除路由映射
 * @param {string} pcRoute PC端路由
 */
export function removeRouteMapping(pcRoute) {
  const mobileRoute = routeMapping.pcToMobile[pcRoute]
  if (mobileRoute) {
    delete routeMapping.pcToMobile[pcRoute]
    delete routeMapping.mobileToPc[mobileRoute]
  }
} 