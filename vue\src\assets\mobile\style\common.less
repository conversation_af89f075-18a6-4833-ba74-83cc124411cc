@import url('./variable.less');

@font-face{
  font-family: 'font1'; 
  src: url('../font/BarlowCondensed-Medium.otf');
}

@font-face{
  font-family: 'font2'; 
  src: url('../font/MFBanHei_Noncommercial-Regular.otf');
}

@font-face{
  font-family: 'font3'; 
  src: url('../font/HYQiHei-50S.otf');
}

@font-face{
  font-family: 'font4'; 
  src: url('../font/HYQiHei-70S.otf');
}
@font-face {
  font-family: 'LCD';
  src: url('../font/LCDBQ-Italic.otf');
}

.font1 {
  font-family: font1;
}

.font2 {
  font-family: font2;
}

.font3 {
  font-family: font3;
}

.font4 {
  font-family: font4;
}



html {
  font-family: font1, sans-serif, Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, <PERSON><PERSON>, sans-serif;
  font-size: 16px;
  color: white;
}

.w-60 {
  width: 60%;
}

.h-30 {
  height: 30%;
}
.h-35 {
  height: 35%;
}

.h-40 {
  height: 40%;
}

.h-60 {
  height: 60%;
}

.h-70 {
  height: 70%;
}

.color-white {
  color: #fff;
}

.color-black {
  color: #333;
}

.color-gray {
  color: #ccc;
}

.bg-cover {
  background-color: @colorCover;
}

.bg-deep {
  background-color: @colorDeep;
}

.shadow-common {
  box-shadow: 0 0.4rem 0.4rem rgba(238, 238, 238, 0.5);
}

.w-35 {
  width: 35%;
}

.py-2 {
  padding-top: 0.8rem!important;
  padding-bottom: 0.8rem!important;
}

.pt-2 {
  padding-top: 0.8rem!important;
}

.pb-2 {
  padding-bottom: 0.8rem!important;
}

.px-2 {
  padding-left: 0.8rem!important;
  padding-right: 0.8rem!important;
}

.pl-2 {
  padding-left: 0.8rem!important;
}

.pr-2 {
  padding-right: 0.8rem!important;
}

.flex-item {
  display: flex;
  .flex-label {
    flex: 0 0 auto;
  }
  .flex-content {
    flex: 1 1 auto;
  }
}

.flex-vertical {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  .flex-header, .flex-footer {
    flex: 0 0 auto;
  }
  .flex-content {
    flex: 1;
    overflow: auto;
  }
}

.text-max {
  font-size: 1.875rem; // 30
}

.text-lg {
  font-size: 1.625rem; // 26 
}

.text-plus {
  font-size: 1.5rem; // 24
}

.text-md {
  font-size: 1.375rem; // 22 
}

.text-main {
  font-size: 1.25rem; // 20
}

.text-title {
  font-size: 1.125rem; // 18
}

.text-sub {
  font-size: 1rem; // 16
}

.text-item {
  font-size: 0.9375rem; // 15
}

.text-alert {
  font-size: 0.875rem; // 14
}

.text-mini {
  font-size: 0.8125rem; // 13
}

.text-min {
  font-size: 0.75rem; // 12
}

.text-zero {
  font-size: 0;
}

.ellipsis-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-nowrap {
  white-space: nowrap;
}

.round-8 {
  border-radius: 0.5rem;
  overflow: hidden;
}

.border-box {
  box-sizing: border-box;
}

.cursor-pointer {
  cursor: pointer;
}

.no-scroll {
	&::-webkit-scrollbar {
		width: 0px;
	}
}

.top-0 {
  top: 0;
}

.round-sm {
  border-radius: 0.5rem;
}

.round-md {
  border-radius: 0.8rem;
}

.round-lg {
  border-radius: 1rem;
}

.common-box {
  width: 25%;
  height: calc(100% - 6rem);
  padding: 0.6rem 0 0.8rem;
  box-sizing: border-box;
  margin-top: 6rem;
}

.border-common {
  border: 1px solid #8cc8ff;
  box-sizing: border-box;
}

.grad-bg {
  background-size: 6rem 2.5rem;
  background-color: rgba(0, 69, 149, 0.7);
  background-repeat: repeat;
  background-image: url('~@/assets/img/grad.png');
}

.linear-bg__primary {
  // background: linear-gradient(to right, #5765F2 0%, #4644e3 100%)!important;
  background: linear-gradient(to right, #4cb2ff 0%, #5765F2 100%)!important;
}

.tag {
  height: 1.6rem;
  line-height: 1.6rem;
  padding: 0 1rem;
  background-color: rgba(43, 59, 204, 0.24);
  font-size: 0.9rem;
  border-radius: 0.2rem;
  box-shadow: 0 0 0.3rem rgb(106, 143, 245) inset;
  &.tag-success {
    background-color: rgba(75, 204, 43, 0.24);
    box-shadow: 0 0 0.3rem rgb(92, 243, 130) inset;
  }
}

.button-item {
  padding: 0.6rem 1.5rem;
  box-shadow: 0 0 1rem #1273db inset;
  border-radius: 0.2rem;
  font-size: 1.5rem;
}

// 覆盖elemen组件样式

.el-button {
  &.el-button--warning {
    background-color: @colorWarning;
  }
  &.el-button--dange {
    background-color: @colorDanger;
  }
}

.el-form-item__label {
  &::before {
    color: @colorDanger!important;
  }
}

.el-table {
  &.custom {
    background-color: transparent!important;
    &::before {
      width: 0!important;
    }
    th, tr, td {
      background: transparent!important;
      border-color: rgba(173, 195, 224, 0.1)!important;
      color: #adc3e0!important;
      font-size: 1rem;
    }
    th {
      color: @colorWhite!important;
    }
    .el-table__cell {
      padding: 1.2rem 0;
    }
  }
}

.el-pagination {
  &.custom {
    .el-pagination__total, .el-pagination__jump {
      color: @colorWhite!important;
    }
    .el-input__inner {
      background: @colorCommon;
      border: none;
      color: @colorWhite;
    }
    button {
      background: rgba(255, 255, 255, 0.1)!important;
    }
  }
}

