<template>
  <div class="home">
    <van-nav-bar title="上海海洋大学智慧养殖平台" class="home-nav">
    </van-nav-bar>
    <div class="content">
      <div class="card">
        <company-introduction />
      </div>
      <div class="card">
        <base-list :baselist="baseList" />
      </div>
    </div>
  </div>
</template>

<script>
import CompanyIntroduction from '@/components/mobile/CompanyIntroduction.vue'
import BaseList from '@/components/mobile/BaseList.vue'
import { homelist } from '@/request/api';

export default {
  name: 'Home',
  components: {
    CompanyIntroduction,
    BaseList
  },

  data() {
    return {
      baseList: [],
      wetherinfo: {
        'wendu': {
          img: 'top-info-wd.png',
          value: '0'
        },
        'shidu': {
          img: 'top-info-xy.png',
          value: '0'
        },
        'fengxiang': {
          img: 'top-info-ql.png',
          value: '0'
        },
        'fengsu': {
          img: 'top-info-fs.png',
          value: '0'
        },
        'guangzhao': {
          img: 'top-info-yg.png',
          value: '0'
        },
        'jiangyuliang': {
          img: 'top-info-sd.png',
          value: '0'
        },
        'qiya': {
          img: 'top-info-qy.png',
          value: '0'
        }
      },
      alertlist: {
        tem: 0,
        ph: 0,
        o2: 0
      },
    }
  },

  methods: {
    startPolling() {
      this.intervalId = setInterval(() => {
        this.getlist();
      }, 60000); // 每60秒（1分钟）调用一次getlist
    },
    getlist() {
      homelist({}).then(res => {
        this.baseList = res.data.baselist;
        console.log("object", this.baseList);
        // this.updateMapMarkers();
        let tianqi = res.data.tianqi;
        Object.keys(tianqi).forEach(key => {
          if (Object.prototype.hasOwnProperty.call(this.wetherinfo, key)) {
            this.wetherinfo[key].value = parseFloat(parseFloat(tianqi[key]).toFixed(1));
          }
        });
        if (res.data.alertlist.length > 0) {
          const firstAlert = res.data.alertlist[0];
          const describe = firstAlert.describe;
          // 使用正则表达式提取 TEM, PH, O2 的值
          const regex = /TEM:(\d+\.\d+) PH:(\d+\.\d+) O2:(\d+\.\d+)/;
          const match = describe.match(regex);
          if (match) {
            this.alertlist.tem = parseFloat(parseFloat(match[1]).toFixed(1));
            this.alertlist.ph = parseFloat(parseFloat(match[2]).toFixed(1));
            this.alertlist.o2 = parseFloat(parseFloat(match[3]).toFixed(1));
          } else {
            console.error("无法解析 describe 字符串");
          }
        } else {
          console.error("alertlist 为空");
        }
      });
    },
  },

  mounted() {
    this.getlist();
    this.startPolling(); // 开始定时请求数据
  },

  beforeDestroy() {
    // 清除定时器
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

 }
</script>

<style lang="less" scoped>
.home {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background-color: #001528;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.home-nav {
  flex-shrink: 0;
  background-color: #001528 !important;

  :deep(.van-nav-bar__content) {
    background-color: #001528 !important;
  }

  :deep(.van-nav-bar__title) {
    color: #C3E4FF !important;
  }

  :deep(.van-button) {
    color: #C3E4FF !important;
    border-color: #C3E4FF !important;
    background-color: transparent !important;
  }

  :deep(.van-icon) {
    color: #C3E4FF !important;
  }
}

.content {
  flex: 1;
  overflow: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  .card {
    height: 350px;
  }
}
</style>