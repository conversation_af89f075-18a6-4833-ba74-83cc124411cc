module.exports = {
  devServer: {
    historyApiFallback: {
      rewrites: [
        { from: /^\/mobile\/.*$/, to: '/index.html' },
        { from: /^\/pc\/.*$/, to: '/index.html' },
        { from: /./, to: '/index.html' }
      ]
    },
    host: '0.0.0.0',
    disableHostCheck: true
  },
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: false,
  productionSourceMap: false
}
