module.exports = {
  devServer: {
    historyApiFallback: {
      rewrites: [
        { from: /^\/mobile\/.*$/, to: '/index.html' },
        { from: /^\/pc\/.*$/, to: '/index.html' },
        { from: /./, to: '/index.html' }
      ]
    },
    host: '0.0.0.0',
    disableHostCheck: true
  },
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: false,
  productionSourceMap: false,
  configureWebpack: {
    output: {
      filename: process.env.NODE_ENV === 'production' ? 'static/js/[name].[contenthash:8].js' : '[name].js',
      chunkFilename: process.env.NODE_ENV === 'production' ? 'static/js/[name].[contenthash:8].js' : '[name].js'
    }
  },
  chainWebpack: config => {
    // 确保静态资源使用正确的路径
    config.module
      .rule('images')
      .test(/\.(png|jpe?g|gif|svg)(\?.*)?$/)
      .use('url-loader')
      .loader('url-loader')
      .options({
        limit: 4096,
        fallback: {
          loader: 'file-loader',
          options: {
            name: 'static/img/[name].[hash:8].[ext]'
          }
        }
      })
  }
}
