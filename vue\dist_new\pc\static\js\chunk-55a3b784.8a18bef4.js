(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-55a3b784"],{"435c":function(A,t,a){"use strict";a("e74b")},ca73:function(A,t){A.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAYAAAAD+CAYAAAAzmNK6AAAAAXNSR0IArs4c6QAAC2JJREFUeF7t1z+I3gcdx/Hfk9ypkJhaKopdqiamg6BgbBzkqHFRsHouER0cXFwVdFbirKBrFwdB0C6mrWgR06TFoWDdIiQlQpaIg7b3/8/z3D1yR1sTKwo+R+Dp+3XjDb/n93l9vncfntF7r706HfwcCDxz/Njo239bes9fcBAgQKAgMDIA/6p5NBlPT/3hufUHrj69MRrvGsbCX4CMBKICL794+WED8B/Kn66tvrrzq5//dvL0L25Gb0NsAgTexgIfffSRb75lANYvXrj0Ns4sGgECBAgMw2AAnAEBAgSiAgYgWrzYBAgQMABugAABAlEBAxAtXmwCBAgYADdAgACBqIABiBYvNgECBAyAGyBAgEBUwABEixebAAECBsANECBAICpgAKLFi02AAAED4AYIECAQFTAA0eLFJkCAgAFwAwQIEIgKGIBo8WITIEDAALgBAgQIRAUMQLR4sQkQIGAA3AABAgSiAgYgWrzYBAgQMABugAABAlEBAxAtXmwCBAgYADdAgACBqIABiBYvNgECBAyAGyBAgEBUwABEixebAAECBsANECBAICpgAKLFi02AAAED4AYIECAQFTAA0eLFJkCAgAFwAwQIEIgKGIBo8WITIEDAALgBAgQIRAUMQLR4sQkQIGAA3AABAgSiAgYgWrzYBAgQMABugAABAlEBAxAtXmwCBAgYADdAgACBqIABiBYvNgECBAyAGyBAgEBUwABEixebAAECBsANECBAICpw9wA8OwzDFw4c1i9euBT1EJsAAQIZgTcH4P3XXvvQ/mR8a7qwMDIAmf4FJUAgLPDmABwYnP7Bz9ZWHn/ipAEIX4ToBAhkBO4ZgE989uJfb3/nh+/a+MbyTzICghIgQCAqcM8AnFtavnPj7Keenzzzy1eiHmITIEAgI/CWAbh+4/aTmfSCEiBAICxgAMLli06AQFvAALT7l54AgbCAAQiXLzoBAm0BA9DuX3oCBMICBiBcvugECLQFDEC7f+kJEAgLGIBw+aITINAWMADt/qUnQCAsYADC5YtOgEBbwAC0+5eeAIGwgAEIly86AQJtAQPQ7l96AgTCAgYgXL7oBAi0BQxAu3/pCRAICxiAcPmiEyDQFjAA7f6lJ0AgLGAAwuWLToBAW8AAtPuXngCBsIABCJcvOgECbQED0O5fegIEwgIGIFy+6AQItAUMQLt/6QkQCAsYgHD5ohMg0BYwAO3+pSdAICxgAMLli06AQFvAALT7l54AgbCAAQiXLzoBAm0BA9DuX3oCBMICBiBcvugECLQFDEC7f+kJEAgLGIBw+aITINAWMADt/qUnQCAsYADC5YtOgEBbwAC0+5eeAIGwgAEIly86AQJtAQPQ7l96AgTCAgYgXL7oBAi0BQxAu3/pCRAICxiAcPmiEyDQFjAA7f6lJ0AgLGAAwuWLToBAW8AAtPuXngCBsIABCJcvOgECbQED0O5fegIEwgIGIFy+6AQItAUMQLt/6QkQCAsYgHD5ohMg0BYwAO3+pSdAICxgAMLli06AQFvAALT7l54AgbCAAQiXLzoBAm0BA9DuX3oCBMICBiBcvugECLQFDEC7f+kJEAgLGIBw+aITINAWMADt/qUnQCAsYADC5YtOgEBbwAC0+5eeAIGwgAEIly86AQJtAQPQ7l96AgTCAgYgXL7oBAi0BQxAu3/pCRAICxiAcPmiEyDQFjAA7f6lJ0AgLGAAwuWLToBAW8AAtPuXngCBsIABCJcvOgECbQED0O5fegIEwgIGIFy+6AQItAUMQLt/6QkQCAsYgHD5ohMg0BYwAO3+pSdAICxgAMLli06AQFvAALT7l54AgbCAAQiXLzoBAm0BA9DuX3oCBMICBiBcvugECLQFDEC7f+kJEAgLGIBw+aITINAWMADt/qUnQCAsYADC5YtOgEBbwAC0+5eeAIGwgAEIly86AQJtAQPQ7l96AgTCAgYgXL7oBAi0BQxAu3/pCRAICxiAcPmiEyDQFjAA7f6lJ0AgLGAAwuWLToBAW8AAtPuXngCBsIABCJcvOgECbQED0O5fegIEwgIGIFy+6AQItAUMQLt/6QkQCAsYgHD5ohMg0BYwAO3+pSdAICxgAMLli06AQFvAALT7l54AgbCAAQiXLzoBAm0BA9DuX3oCBMICBiBcvugECLQFDEC7f+kJEAgLGIBw+aITINAWMADt/qUnQCAsYADC5YtOgEBbwAC0+5eeAIGwgAEIly86AQJtAQPQ7l96AgTCAgYgXL7oBAi0BQxAu3/pCRAICxiAcPmiEyDQFjAA7f6lJ0AgLGAAwuWLToBAW8AAtPuXngCBsIABCJcvOgECbQED0O5fegIEwgIGIFy+6AQItAUMQLt/6QkQCAsYgHD5ohMg0BYwAO3+pSdAICxgAMLli06AQFvAALT7l54AgbCAAQiXLzoBAm0BA9DuX3oCBMICBiBcvugECLQFDEC7f+kJEAgLGIBw+aITINAWMADt/qUnQCAsYADC5YtOgEBbwAC0+5eeAIGwgAEIly86AQJtAQPQ7l96AgTCAgYgXL7oBAi0BQxAu3/pCRAICxiAcPmiEyDQFjAA7f6lJ0AgLGAAwuWLToBAW8AAtPuXngCBsIABCJcvOgECbQED0O5fegIEwgIGIFy+6AQItAUMQLt/6QkQCAsYgHD5ohMg0BYwAO3+pSdAICxgAMLli06AQFvAALT7l54AgbCAAQiXLzoBAm0BA9DuX3oCBMICBiBcvugECLQFDEC7f+kJEAgLGIBw+aITINAWMADt/qUnQCAsYADC5YtOgEBbwAC0+5eeAIGowMIXv/KRR2++dOHlFy8/PDowOLe0fOf6jdtPRj3EJkCAQEbgxE8vf+uRH313+09XnvqAAcjULigBAgSG4eRTz3//gWvPrt363tdPGQAXQYAAgZDAwQCMJpPpsYXF0wYgVLyoBAgQOBiA1xV+bQDcAwECBEICdw3AYABCxYtKgAABA+AGCBAgEBUwANHixSZAgIABcAMECBCIChiAaPFiEyBAwAC4AQIECEQFDEC0eLEJECBgANwAAQIEogIGIFq82AQIEDAAboAAAQJRAQMQLV5sAgQIGAA3QIAAgaiAAYgWLzYBAgQMgBsgQIBAVMAARIsXmwABAgbADRAgQCAqYACixYtNgAABA+AGCBAgEBUwANHixSZAgIABcAMECBA4IoG7/6GuX7xw6Y3HHv5+b28y7E0m46vPXVk4v/TYdLy7O9rZ2h6/8Ls/Hv/4Y2emmxtb082N7b2XXrg1+uCZh4b1te3p2sr23it//sexE+9e3N9YG09XV8bD1ubkiF53MABHJek5BAjkBf7vAfjYJ89Mtze3pxsbW4cD8OGzDw2rK/cOwNrK7nR1bTzsbO4dFbQBOCpJzyFAIC/wPwZgb9ibjMdXf3Nl8fzj5/fHu9ujna2dw28A/z4Ar38D2F9f3dm/ef3vh98ADgZgfW3iG0D+zAAQIEBgdgHfAGY39AQCBAjMpYABmMvavDQBAgRmFzAAsxt6AgECBOZSwADMZW1emgABArMLGIDZDT2BAAECcylgAOayNi9NgACB2QUMwOyGnkCAAIG5FDAAc1mblyZAgMDsAgZgdkNPIECAwFwKGIC5rM1LEyBAYHYBAzC7oScQIEBgLgUMwFzW5qUJECAwu4ABmN3QEwgQIDCXAgZgLmvz0gQIEJhdwADMbugJBAgQmEsBAzCXtXlpAgQIzC5gAGY39AQCBAjMpYABmMvavDQBAgRmFzAAsxt6AgECcyKwsPy1s+/88lc/Pzp56sE5eeX79pqjg086t7R85759og8iQIDAfRaYLr5jtPKZL51c/fTnTkwXFg//7/kZBhCugACBjMD7fv/a6f3j0x8Po+GJTOj/EvSfWMCIH0iXPm0AAAAASUVORK5CYII="},d882:function(A,t){A.exports="data:image/png;base64,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"},e5c9:function(A,t,a){"use strict";a.r(t);var e=function(){var A=this,t=A.$createElement,a=A._self._c||t;return a("div",{staticClass:"device-detail"},[a("van-nav-bar",{staticClass:"device-nav",attrs:{title:A.deviceName,"left-arrow":""},on:{"click-left":A.onClickLeft}}),a("div",{staticClass:"equip-box",style:{backgroundImage:"url("+A.backgroundImage+")"}},[a("div",{staticClass:"text"},[A.miaoshuFormatted&&0!==A.miaoshuFormatted.length?A.miaoshuFormatted&&A.miaoshuFormatted.length>=3?a("div",{staticClass:"info-table"},[a("div",{staticClass:"info-row full-width"},[a("span",{style:{color:A.labelcolor}},[A._v(A._s(A.miaoshuFormatted[0].label))]),a("span",{staticClass:"info-value"},[A._v("："+A._s(A.miaoshuFormatted[0].value))])]),a("div",{staticClass:"info-row full-width"},[a("span",{style:{color:A.labelcolor}},[A._v(A._s(A.miaoshuFormatted[1].label))]),a("span",{staticClass:"info-value"},[A._v("："+A._s(A.miaoshuFormatted[1].value))])]),a("div",{staticClass:"info-row full-width"},[a("span",{style:{color:A.labelcolor}},[A._v(A._s(A.miaoshuFormatted[2].label))]),a("span",{staticClass:"info-value"},[A._v("："+A._s(A.miaoshuFormatted[2].value))])]),A.miaoshuFormatted.length>4?a("div",{staticClass:"info-row"},[a("span",{style:{color:A.labelcolor}},[A._v(A._s(A.miaoshuFormatted[3].label))]),a("span",{staticClass:"info-value"},[A._v("："+A._s(A.miaoshuFormatted[3].value))]),a("span",{staticClass:"info-gap"}),a("span",{style:{color:A.labelcolor}},[A._v(A._s(A.miaoshuFormatted[4].label))]),a("span",{staticClass:"info-value"},[A._v("："+A._s(A.miaoshuFormatted[4].value))])]):A._e(),A.miaoshuFormatted.length>6?a("div",{staticClass:"info-row"},[a("span",{style:{color:A.labelcolor}},[A._v(A._s(A.miaoshuFormatted[5].label))]),a("span",{staticClass:"info-value"},[A._v("："+A._s(A.miaoshuFormatted[5].value))]),a("span",{staticClass:"info-gap"}),a("span",{style:{color:A.labelcolor}},[A._v(A._s(A.miaoshuFormatted[6].label))]),a("span",{staticClass:"info-value"},[A._v("："+A._s(A.miaoshuFormatted[6].value))])]):A._e(),A.miaoshuFormatted.length>8?a("div",{staticClass:"info-row"},[a("span",{style:{color:A.labelcolor}},[A._v(A._s(A.miaoshuFormatted[7].label))]),a("span",{staticClass:"info-value"},[A._v("："+A._s(A.miaoshuFormatted[7].value))]),a("span",{staticClass:"info-gap"}),a("span",{style:{color:A.labelcolor}},[A._v(A._s(A.miaoshuFormatted[8].label))]),a("span",{staticClass:"info-value"},[A._v("："+A._s(A.miaoshuFormatted[8].value))])]):A._e(),A.miaoshuFormatted.length>10?a("div",{staticClass:"info-row"},[a("span",{style:{color:A.labelcolor}},[A._v(A._s(A.miaoshuFormatted[9].label))]),a("span",{staticClass:"info-value"},[A._v("："+A._s(A.miaoshuFormatted[9].value))]),a("span",{staticClass:"info-gap"}),a("span",{style:{color:A.labelcolor}},[A._v(A._s(A.miaoshuFormatted[10].label))]),a("span",{staticClass:"info-value"},[A._v("："+A._s(A.miaoshuFormatted[10].value))])]):A._e()]):A._e():a("div",{staticClass:"loading-text"},[A._v(" 正在加载设备信息... ")])])]),A._l(A.chartTitles,(function(t,e){return a("div",{key:e,staticClass:"right-item2",class:1==A.selectid?"shebei1":"shebei2"},[a("span",[A._v(A._s(t))]),A.lineData&&A.lineData.length>0&&A.lineData[e]?a("chartline",{attrs:{Data:A.lineData[e]}}):A._e()],1)}))],2)},i=[],s=a("5530"),I=(a("b0c0"),a("b64b"),a("7db0"),a("d3b7"),a("fd03")),g=a("2f62"),l=a("3876"),C={components:{chartline:l["a"]},data:function(){return{box:0,miaoshu:{EQUIPMENT_TYPE:"",EQUIPMENT_NAME:"",ACQUISITION_TIME:"",INTRODUCE:"",VOLTAGE:"",state:"",ORP:"",CON:"",TEM:"",PH:"",O2:"",SALT:"",LIGHT:"",WIND_SPEED:"",WIND_DIRECTION:"",RADIATION:"",HUMIDITY:"",ATM:"",RAINFALL:""},selectid:1,lineData:[{name:"温度",data:[],xAxisData:[],data1:[],data2:[]},{name:"溶氧值",data:[],xAxisData:[],data1:[],data2:[]},{name:"PH",data:[],xAxisData:[],data1:[],data2:[]},{name:"雨量",data:[],xAxisData:[],data1:[],data2:[]}]}},computed:Object(s["a"])(Object(s["a"])({},Object(g["c"])("device",["deviceInfo","baseId","chartData"])),{},{deviceId:function(){return this.deviceInfo.id},deviceName:function(){return this.deviceInfo.name},deviceType:function(){return this.deviceInfo.type},fullDeviceData:function(){return this.deviceInfo.fullDeviceData},backgroundImage:function(){return"水质检测"===this.miaoshu.EQUIPMENT_TYPE?a("ca73"):a("d882")},labelcolor:function(){return"#A6FFB0"},miaoshuFormatted:function(){if("水质检测"==this.miaoshu.EQUIPMENT_TYPE){var A=[{label:"设备名称",value:this.miaoshu.EQUIPMENT_NAME},{label:"采集时间",value:this.miaoshu.ACQUISITION_TIME},{label:"设备描述",value:this.miaoshu.INTRODUCE},{label:"电压",value:this.miaoshu.VOLTAGE},{label:"状态",value:this.miaoshu.state},{label:"ORP",value:this.miaoshu.ORP},{label:"电导",value:this.miaoshu.CON},{label:"温度",value:this.miaoshu.TEM},{label:"PH",value:this.miaoshu.PH},{label:"含氧量",value:this.miaoshu.O2},{label:"盐度",value:this.miaoshu.SALT}];return A}if("气象检测"==this.miaoshu.EQUIPMENT_TYPE){var t=[{label:"设备名称",value:this.miaoshu.EQUIPMENT_NAME},{label:"采集时间",value:this.miaoshu.ACQUISITION_TIME},{label:"设备描述",value:this.miaoshu.INTRODUCE},{label:"电压",value:this.miaoshu.VOLTAGE},{label:"状态",value:this.miaoshu.state},{label:"光照",value:this.miaoshu.LIGHT},{label:"风速",value:this.miaoshu.WIND_SPEED},{label:"风向",value:this.miaoshu.WIND_DIRECTION},{label:"辐射",value:this.miaoshu.RADIATION},{label:"温度",value:this.miaoshu.TEM},{label:"湿度",value:this.miaoshu.HUMIDITY}];return t}return[]},chartTitles:function(){return 1==this.selectid?["水温","PH","含氧量"]:2==this.selectid?["温度","湿度","气压"]:["","",""]}}),created:function(){"water"===this.deviceType?this.selectid=1:"meteorological"===this.deviceType&&(this.selectid=2),!this.deviceInfo.id&&this.$route.query.id&&this.updateDeviceInfo({id:this.$route.query.id,name:this.$route.query.name||"未知设备",type:this.$route.query.type||"water",itemData:{},fullDeviceData:{}}),this.fullDeviceData&&Object.keys(this.fullDeviceData).length>0?this.miaoshu={EQUIPMENT_TYPE:"water"===this.deviceType?"水质检测":"气象检测",EQUIPMENT_NAME:this.fullDeviceData.EQUIPMENT_NAME||this.deviceName||"未知设备",ACQUISITION_TIME:this.fullDeviceData.ACQUISITION_TIME||"暂无数据",INTRODUCE:this.fullDeviceData.INTRODUCE||"暂无描述",VOLTAGE:this.fullDeviceData.VOLTAGE||"0V",state:this.fullDeviceData.STATE||"未知",ORP:this.fullDeviceData.ORP||"0",CON:this.fullDeviceData.CON||"0",TEM:this.fullDeviceData.TEM||"0°C",PH:this.fullDeviceData.PH||"0",O2:this.fullDeviceData.O2||"0",SALT:this.fullDeviceData.SALT||"0",LIGHT:this.fullDeviceData.LIGHT||"0",WIND_SPEED:this.fullDeviceData.WIND_SPEED||"0",WIND_DIRECTION:this.fullDeviceData.WIND_DIRECTION||"0",RADIATION:this.fullDeviceData.RADIATION||"0",HUMIDITY:this.fullDeviceData.HUMIDITY||"0",ATM:this.fullDeviceData.ATM||"0",RAINFALL:this.fullDeviceData.RAINFALL||"0"}:this.miaoshu={EQUIPMENT_TYPE:"water"===this.deviceType?"水质检测":"气象检测",EQUIPMENT_NAME:this.deviceName||"未知设备",ACQUISITION_TIME:"暂无数据",INTRODUCE:"暂无描述",VOLTAGE:"0V",state:"未知",ORP:"0",CON:"0",TEM:"0°C",PH:"0",O2:"0",SALT:"0",LIGHT:"0",WIND_SPEED:"0",WIND_DIRECTION:"0",RADIATION:"0",HUMIDITY:"0",ATM:"0",RAINFALL:"0"},this.fetchDeviceData()},methods:Object(s["a"])(Object(s["a"])({},Object(g["b"])("device",["updateDeviceInfo","updateBaseId","updateChartData"])),{},{onClickLeft:function(){this.$router.back()},getshebeidata:function(A){var t=this;Object(I["y"])({base_id:this.baseId,equipmentID:A.ID}).then((function(A){if(A.data&&A.data.shuju){var a=A.data.shuju,e=[];e.push({name:"温度",id:1,data:a.templist[0].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[0].y[1].data,data2:a.templist[0].y[2].data,max:a.templist[0].max,min:a.templist[0].min}),1==t.selectid?e.push({name:"溶氧值",id:2,data:a.templist[1].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[1].y[1].data,data2:a.templist[1].y[2].data,max:a.templist[1].max,min:a.templist[1].min},{name:"PH",id:3,data:a.templist[2].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[2].y[1].data,data2:a.templist[2].y[2].data,max:a.templist[2].max,min:a.templist[2].min}):2==t.selectid&&e.push({name:"湿度",id:2,data:a.templist[3].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[3].y[1].data,data2:a.templist[3].y[2].data,max:a.templist[3].max,min:a.templist[3].min},{name:"气压",id:3,data:a.templist[4].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[4].y[1].data,data2:a.templist[4].y[2].data,max:a.templist[4].max,min:a.templist[4].min}),t.lineData=e,t.updateChartData(e)}})).catch((function(A){console.error("获取设备图表数据失败:",A)}))},fetchDeviceData:function(){var A=this;this.baseId&&this.deviceId?Object(I["z"])({base_id:this.baseId}).then((function(t){if(t&&t.data&&t.data.shebeilist){var a=t.data.shebeilist.find((function(t){return t.ID==A.deviceId||t.EQUIPMENT_NAME==A.deviceName}));a?(console.log("找到目标设备:",a),A.getshebeidata(a)):console.warn("未找到目标设备，deviceId:",A.deviceId,"deviceName:",A.deviceName)}})).catch((function(A){console.error("获取设备列表失败:",A)})):console.warn("缺少必要的参数，baseId:",this.baseId,"deviceId:",this.deviceId)}})},B=C,o=(a("435c"),a("2877")),E=Object(o["a"])(B,e,i,!1,null,"aee69210",null);t["default"]=E.exports},e74b:function(A,t,a){}}]);