# 更新静态资源脚本
Write-Host "正在更新静态资源..." -ForegroundColor Green

# 删除旧的静态资源目录
if (Test-Path "dist\pc\static") {
    Remove-Item -Path "dist\pc\static" -Recurse -Force
    Write-Host "已删除旧的PC静态资源" -ForegroundColor Yellow
}

if (Test-Path "dist\mobile\static") {
    Remove-Item -Path "dist\mobile\static" -Recurse -Force
    Write-Host "已删除旧的移动端静态资源" -ForegroundColor Yellow
}

# 复制新的静态资源
Copy-Item -Path "dist\static" -Destination "dist\pc\static" -Recurse
Copy-Item -Path "dist\static" -Destination "dist\mobile\static" -Recurse

Write-Host "静态资源更新完成！" -ForegroundColor Green
Write-Host "现在可以重新启动服务器了" -ForegroundColor Cyan
