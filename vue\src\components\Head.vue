<template>
  <header class="border-box border-box">
    <div class="w-100 h-100 d-flex align-items-center justify-content-center">
      <slot></slot>
    </div>
    <div class="flex-label right-content d-flex align-items-center pb-1">
      <div class="text-lg mr-5">{{ time }}</div>
      <div class="d-flex align-items-center text-md" @click="exitLogion()">
        <svg t="1636380494993"
             class="icon"
             viewBox="0 0 1024 1024"
             version="1.1"
             xmlns="http://www.w3.org/2000/svg"
             p-id="10082"
             width="2.1rem"
             height="2.1rem">
          <path d="M502.784 428.544a31.744 31.744 0 0 0 32.256-31.232V208.384a32.256 32.256 0 0 0-65.024 0v188.416a31.744 31.744 0 0 0 32.768 31.744z"
                fill="#ffffff"
                p-id="10083"></path>
          <path d="M693.248 256a33.28 33.28 0 0 0-45.056 7.68 30.72 30.72 0 0 0 8.192 43.52 259.584 259.584 0 0 1 116.736 215.04 266.24 266.24 0 0 1-269.824 261.632 266.24 266.24 0 0 1-270.336-261.632A259.584 259.584 0 0 1 349.696 307.2 30.72 30.72 0 0 0 358.4 264.192a32.768 32.768 0 0 0-27.648-13.312 33.28 33.28 0 0 0-17.92 5.12 321.536 321.536 0 0 0-144.384 266.24 330.24 330.24 0 0 0 334.848 324.096 330.24 330.24 0 0 0 334.848-324.096A321.536 321.536 0 0 0 693.248 256z"
                fill="#ffffff"
                p-id="10084"></path>
        </svg>
        退出登录
      </div>
    </div>
    <div class="bottom-box">
      <Item>
        <div class="d-flex align-items-center mb-3 text-md">
          <div class="d-flex justify-content-center align-items-center w-25">
            <img :src="wenduImg" alt="temperature" class="mr-1" style="width:2rem;height:2rem">
            <div class="mx-1" >{{ livetianqi.temperature || '温度' }}℃</div>
          </div>
          <div class="d-flex justify-content-center align-items-center w-25">
            <img :src="shiduImg" alt="shidu" class="mr-1" style="width:2rem;height:2rem">
            <div class="mx-1" >{{ tianqi.shidu || '湿度' }}</div>
          </div>
          <div class="d-flex justify-content-center align-items-center w-25">
            <img :src="fengxiangImg" alt="fengxiang" class="mr-1" style="width:2rem;height:2rem">
            <div class="mx-1" >{{ tianqi.fengxiang || '风向' }}</div>
          </div>
          <div class="d-flex justify-content-center align-items-center w-25">
            <img :src="fengsuImg" alt="fengsu" class="mr-1" style="width:2rem;height:2rem">
            <div class="mx-1" >{{ tianqi.fengsu || '风力' }}</div>
          </div>
        </div>
        <div class="d-flex align-items-center text-md">
          <div class="d-flex justify-content-center align-items-center w-25">
            <img :src="guangzhaoImg" alt="guangzhao" class="mr-1" style="width:2rem;height:2rem">
            <div class="mx-1" >{{ tianqi.guangzhao || '光照' }}</div>
          </div>
          <div class="d-flex justify-content-center align-items-center w-25">
            <img :src="jiangylImg" alt="jiangyl" class="mr-1" style="width:2rem;height:2rem">
            <div class="mx-1" >{{ tianqi.jiangyuliang || '降雨量' }}</div>
          </div>
          <div class="d-flex justify-content-center align-items-center w-25">
            <img :src="daqiyImg" alt="daqiya" class="mr-1" style="width:2rem;height:2rem">
            <div class="mx-1">{{ tianqi.qiya || '大气压' }}</div>
          </div>
        </div>
      </Item>
    </div>
  </header>
</template>

<script>

export default {
  props: {
    tianqi: {
      type: Object,
      required: true
    },


  },

  data () {
    return {
      time: '--',
      timer: '',
      // 天气图标路径
      wenduImg: require('@/assets/img/wendu.png'),
      shiduImg: require('@/assets/img/shidu.png'),
      fengxiangImg: require('@/assets/img/fengxiang.png'),
      fengsuImg: require('@/assets/img/fengsu.png'),
      guangzhaoImg: require('@/assets/img/guangzhao .png'),
      jiangylImg: require('@/assets/img/jiangyl.png'),
      daqiyImg: require('@/assets/img/daqiy.png'),
    }
  },

  created () {
    this.setTime();

  },
  methods: {
    setTime () {
      this.time = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
      if (!this.timer) this.timer = setInterval(this.setTime, 1000);
    },
    exitLogion(){
       this.$router.push({
        path: '/login',
       })
    }
  }
}
</script>

<style lang="less" scoped>
header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 7rem;
  background: url("~@/assets/img/head.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .right-content {
    position: absolute;
    height: 7rem;
    right: 0;
    top: -0.2rem;
    padding-right: 5rem;
    line-height: 2rem;
  }
  .left-content {
    position: absolute;
    left: 1rem;
    top: 1.5rem;
    line-height: 2rem;
  }
  .bottom-box {
    position: absolute;
    top: 6.6rem;
    left: 50%;
    transform: translate(-50%, 0);
    width: calc(50% - 5rem);
  }
}
</style>
