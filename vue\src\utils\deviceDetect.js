/**
 * 设备检测工具
 * 用于检测当前设备是否为移动设备
 */

// 设备变化回调函数列表
let deviceChangeCallbacks = []

/**
 * 检测是否为移动设备
 * @returns {boolean} 如果是移动设备返回true，否则返回false
 */
export function _isMobile() {
  const userAgent = navigator.userAgent.toLowerCase();
  const mobileKeywords = [
    'android', 'iphone', 'ipad', 'ipod', 'blackberry', 
    'windows phone', 'mobile', 'webos', 'opera mini'
  ];
  
  // 检查用户代理字符串是否包含移动设备关键词
  const isMobileUA = mobileKeywords.some(keyword => userAgent.includes(keyword));
  
  // 检查屏幕宽度（小于768px认为是移动设备）
  const isMobileWidth = window.innerWidth <= 768;
  
  // 检查触摸支持
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  
  return isMobileUA || (isMobileWidth && isTouchDevice);
}

/**
 * 检测设备类型
 * @returns {string} 返回设备类型：'mobile', 'tablet', 'desktop'
 */
export function getDeviceType() {
  const userAgent = navigator.userAgent.toLowerCase();
  const width = window.innerWidth;
  
  // 移动设备检测
  if (_isMobile()) {
    // 平板检测
    if (width >= 768 && width <= 1024) {
      return 'tablet';
    }
    return 'mobile';
  }
  
  return 'desktop';
}

/**
 * 检测是否为iOS设备
 * @returns {boolean}
 */
export function isIOS() {
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
}

/**
 * 检测是否为Android设备
 * @returns {boolean}
 */
export function isAndroid() {
  return /Android/.test(navigator.userAgent);
}

/**
 * 检测是否为微信浏览器
 * @returns {boolean}
 */
export function isWeChat() {
  return /MicroMessenger/i.test(navigator.userAgent);
}

/**
 * 设备状态管理
 */
let currentDevice = getDeviceType()
let isListening = false

/**
 * 监听设备变化
 * @param {Function} callback 设备变化时的回调函数
 */
export function onDeviceChange(callback) {
  if (typeof callback === 'function') {
    deviceChangeCallbacks.push(callback)
  }
  
  // 如果还没有开始监听，启动监听
  if (!isListening) {
    startDeviceChangeListener()
  }
}

/**
 * 移除设备变化监听
 * @param {Function} callback 要移除的回调函数
 */
export function offDeviceChange(callback) {
  const index = deviceChangeCallbacks.indexOf(callback)
  if (index > -1) {
    deviceChangeCallbacks.splice(index, 1)
  }
}

/**
 * 开始监听设备变化
 */
function startDeviceChangeListener() {
  if (isListening) return
  
  isListening = true
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleDeviceChange)
  
  // 监听屏幕方向变化
  if (window.screen && window.screen.orientation) {
    window.screen.orientation.addEventListener('change', handleDeviceChange)
  } else {
    // 兼容旧版本浏览器
    window.addEventListener('orientationchange', handleDeviceChange)
  }
}

/**
 * 处理设备变化
 */
function handleDeviceChange() {
  // 延迟执行，确保窗口大小已经更新
  setTimeout(() => {
    const newDevice = getDeviceType()
    const newIsMobile = _isMobile()
    
    if (newDevice !== currentDevice) {
      const oldDevice = currentDevice
      currentDevice = newDevice
      
      // 触发所有回调函数
      deviceChangeCallbacks.forEach(callback => {
        try {
          callback({
            oldDevice,
            newDevice,
            isMobile: newIsMobile,
            width: window.innerWidth,
            height: window.innerHeight
          })
        } catch (error) {
          console.error('设备变化回调执行错误:', error)
        }
      })
    }
  }, 100)
}

/**
 * 停止监听设备变化
 */
export function stopDeviceChangeListener() {
  if (!isListening) return
  
  isListening = false
  deviceChangeCallbacks = []
  
  window.removeEventListener('resize', handleDeviceChange)
  
  if (window.screen && window.screen.orientation) {
    window.screen.orientation.removeEventListener('change', handleDeviceChange)
  } else {
    window.removeEventListener('orientationchange', handleDeviceChange)
  }
}

/**
 * 获取设备信息
 * @returns {Object} 设备信息对象
 */
export function getDeviceInfo() {
  return {
    type: getDeviceType(),
    isMobile: _isMobile(),
    isIOS: isIOS(),
    isAndroid: isAndroid(),
    isWeChat: isWeChat(),
    width: window.innerWidth,
    height: window.innerHeight,
    userAgent: navigator.userAgent
  }
}
