<template>
  <header class="flex-header flex-item justify-content-between align-items-center bg-deep px-4">
    <div class="d-flex align-items-center">
      <svg t="1639791098228" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18796" width="1.4rem" height="1.4rem"><path d="M830.135922 0H193.864078C86.990291 0 0 86.990291 0 193.864078v636.271844c0 106.873786 86.990291 193.864078 193.864078 193.864078h263.45631c19.883495 0 37.281553-17.398058 37.281554-37.281553s-17.398058-37.281553-37.281554-37.281554H193.864078C126.757282 949.436893 74.563107 894.757282 74.563107 830.135922V193.864078C74.563107 129.242718 129.242718 74.563107 193.864078 74.563107h636.271844c67.106796 0 119.300971 54.679612 119.300971 119.300971v464.776699c-12.427184-4.970874-22.368932-7.456311-34.796116-7.456311h-82.019418c-94.446602 0-171.495146 77.048544-171.495145 171.495146v106.873786c0 52.194175 42.252427 94.446602 94.446602 94.446602h74.563106c106.873786 0 193.864078-86.990291 193.864078-193.864078V193.864078c0-106.873786-86.990291-193.864078-193.864078-193.864078z m119.300971 830.135922c0 67.106796-54.679612 119.300971-119.300971 119.300971h-74.563106c-9.941748 0-19.883495-7.456311-19.883496-19.883495v-106.873786c0-52.194175 42.252427-96.932039 96.932039-96.932039h82.019418c19.883495 0 34.796117 14.912621 34.796116 34.796116v69.592233z" fill="#ffffff" p-id="18797"></path><path d="M211.262136 295.76699h631.300971c19.883495 0 37.281553-17.398058 37.281553-37.281553s-17.398058-37.281553-37.281553-37.281554H211.262136c-19.883495-2.485437-37.281553 14.912621-37.281553 34.796117s17.398058 39.76699 37.281553 39.76699zM211.262136 531.883495h631.300971c19.883495 0 37.281553-17.398058 37.281553-37.281553s-17.398058-37.281553-37.281553-37.281554H211.262136c-19.883495 0-37.281553 17.398058-37.281553 37.281554s17.398058 37.281553 37.281553 37.281553zM584.07767 695.92233h-372.815534c-19.883495 0-37.281553 17.398058-37.281553 37.281553S191.378641 770.485437 211.262136 770.485437h372.815534c19.883495 0 37.281553-17.398058 37.281553-37.281554s-17.398058-37.281553-37.281553-37.281553z" fill="#ffffff" p-id="18798"></path></svg>
      <div class="ml-2">{{ $route.meta.pathName }}</div>
    </div>
    <div class="d-flex align-items-center">
      <div class="mr-5 color-gray cursor-pointer" @click="onModify">修改密码</div>
      <div class="mr-4">用户名</div>
      <div class="logout-btn cursor-pointer d-flex align-items-center" @click="onLogout">
        <svg t="1636380494993" class="icon mr-1" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10082" width="1.6rem" height="1.6rem"><path d="M502.784 428.544a31.744 31.744 0 0 0 32.256-31.232V208.384a32.256 32.256 0 0 0-65.024 0v188.416a31.744 31.744 0 0 0 32.768 31.744z" fill="#ffffff" p-id="10083"></path><path d="M693.248 256a33.28 33.28 0 0 0-45.056 7.68 30.72 30.72 0 0 0 8.192 43.52 259.584 259.584 0 0 1 116.736 215.04 266.24 266.24 0 0 1-269.824 261.632 266.24 266.24 0 0 1-270.336-261.632A259.584 259.584 0 0 1 349.696 307.2 30.72 30.72 0 0 0 358.4 264.192a32.768 32.768 0 0 0-27.648-13.312 33.28 33.28 0 0 0-17.92 5.12 321.536 321.536 0 0 0-144.384 266.24 330.24 330.24 0 0 0 334.848 324.096 330.24 330.24 0 0 0 334.848-324.096A321.536 321.536 0 0 0 693.248 256z" fill="#ffffff" p-id="10084"></path></svg>
        <span>退出登录</span>
      </div>
    </div>

    <!-- 修改密码对话框 -->
    <el-dialog title="修改密码" :visible.sync="dialogVisible" width="40%">
      <div class="d-flex justify-content-center">
        <el-form ref="passwordForm" :model="form" :rules="rules" label-width="auto" class="w-60">
          <el-form-item label="旧密码" prop="oldPwd">
            <el-input v-model="form.oldPwd" type="password" show-password></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newPwd">
            <el-input v-model="form.newPwd" type="password" show-password></el-input>
          </el-form-item>
          <el-form-item label="确认新密码" prop="checkNewPwd">
            <el-input v-model="form.checkNewPwd" type="password" show-password></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelModify">取 消</el-button>
        <el-button type="primary" @click="confirmModify">确 定</el-button>
      </div>
    </el-dialog>
  </header>
</template>

<script>
import { getLoginRoute } from '@/utils/routeManager'
import { _isMobile } from '@/utils/deviceDetect'

export default {
  data() {
    return {
      dialogVisible: false,
      form: {
        oldPwd: '',
        newPwd: '',
        checkNewPwd: ''
      },
      rules: {
        oldPwd: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
        newPwd: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ],
        checkNewPwd: [
          { required: true, message: '请确认新密码', trigger: 'blur' },
          { validator: this.validateConfirmPassword, trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
    // 修改密码
    onModify() {
      this.dialogVisible = true;
    },
    
    // 取消修改密码
    cancelModify() {
      this.dialogVisible = false;
      this.resetForm();
    },
    
    // 确认修改密码
    confirmModify() {
      this.$refs.passwordForm.validate((valid) => {
        if (valid) {
          // TODO: 调用修改密码API
          console.log('修改密码:', this.form);
          this.$message({
            message: '密码修改成功',
            type: 'success'
          });
          this.dialogVisible = false;
          this.resetForm();
        }
      });
    },
    
    // 重置表单
    resetForm() {
      this.form = {
        oldPwd: '',
        newPwd: '',
        checkNewPwd: ''
      };
      if (this.$refs.passwordForm) {
        this.$refs.passwordForm.resetFields();
      }
    },
    
    // 验证确认密码
    validateConfirmPassword(rule, value, callback) {
      if (value !== this.form.newPwd) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    },
    
    // 退出登录
    onLogout() {
      this.$confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除本地存储
        localStorage.removeItem('token');
        localStorage.removeItem('userInfo');
        
        // 跳转到登录页面
        const loginPath = getLoginRoute(_isMobile());
        this.$router.push(loginPath);
        
        this.$message({
          message: '已退出登录',
          type: 'success'
        });
      }).catch(() => {
        // 取消退出
      });
    }
  }
}
</script>

<style lang="less" scoped>
header {
  height: 60px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.cursor-pointer {
  cursor: pointer;
  transition: opacity 0.3s ease;
  
  &:hover {
    opacity: 0.8;
  }
}

.logout-btn {
  &:hover {
    color: #f56c6c;
  }
}

.color-gray {
  color: #b8c4ce;
  
  &:hover {
    color: #ffffff;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  header {
    padding: 0 10px;
    
    .d-flex {
      gap: 5px;
    }
    
    .mr-4, .mr-5 {
      margin-right: 8px !important;
    }
  }
}
</style>