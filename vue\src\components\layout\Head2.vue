<template>
  <div class="header-box d-flex flex-row position-relative">
    <!-- 左侧 -->
    <div class="header-left position-absolute d-flex flex-row">
      <div class="other">
        {{ currentTime }}
      </div>
      <div class="button-box " v-if="isShow">
        <div  :class="['cursor-pointer', { 'active-route': isActiveCultiver }]" @click="goToMonitor('/cultiver')">养殖日志</div>
        <div class="cursor-pointer">智能控制</div>
      </div>
    </div>
    <div class=" w-100 h-100 d-flex justify-content-center">
      <div class="header-center ">
        {{ base_name || '渔业数智化养殖管控平台' }}
      </div>
    </div>

    <div class="header-right position-absolute d-flex flex-row " style="justify-content: flex-end;">

      <div class="button-box" v-if="isShow">
        <div class="cursor-pointer" @click="$router.push('/home')">监控大屏</div>
        <div class="cursor-pointer" @click="goToMonitor('/data/history/')">数据中心</div>
      </div>
      <div class="other d-flex flex-row-reverse align-items-start cursor-pointer" @click="exitLogion()">
        <div>
          退出登录
        </div>
        <img :src="loginImg" alt="">
      </div>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      base_name: "",
      timer: '',
      currentTime: new Date().toLocaleString(),
      // 登录图标路径
      loginImg: require('@/assets/ui/login.png'),
    }
  },
  mounted() {
    this.timer = setInterval(() => {
      this.currentTime = this.formatDate(new Date());
    }, 1000);
  },
  computed: {
    // Base_name() {
    //   console.log("base_name",this.$route.query.base_name);
    //   return this.$route.query.base_name || '未知基地';
    // },
    isActiveCultiver() {
      return this.$route.path === '/cultiver'
    }
  },
  watch: {
    // 监听路由参数变化
    '$route.query.base_name': {
      immediate: true, // 立即执行一次
      handler(newVal) {
        this.base_name = newVal || '渔业数智化养殖管控平台';
      }
    }
  },
  methods: {
    goToMonitor(url) {
      if (this.$route.path !== url) {
        this.$router.push({
          path: url,
          query: {
            base_id: this.$route.query.base_id,
            base_name: this.$route.query.base_name
          }
        }).catch(err => {
          if (err.name !== 'NavigationDuplicated') {
            throw err;
          }
        });
      }
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    exitLogion() {
      this.$router.push({
        path: '/login',
      })
    }
  },
  beforeDestroy() {
    clearInterval(this.timer);
  }
}
</script>
<style lang="less" scoped>
.header-center {
  width: 40rem;
  height: 100%;
  font-family: SourceHanSansCN;
  font-weight: 800;
  font-size: 38px;
  color: #FFFFFF;
  line-height: 5rem;
  text-shadow: 0px 0px 20px rgba(85, 150, 223, 0.85);
  display: flex;
  // align-items: center;
  justify-content: center;
  letter-spacing: 0.6rem;
}

.header-box {
  width: 100%;
  height: 100%;
  background-image: url("~@/assets/ui/head.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.header-left {
  left: 0;
  width: 38rem;
  height: calc(100% - 3.6rem);
  padding: 2.3rem 0 1.2rem 2rem;
}

.other {
  width: 40%;
  height: 100%;
  font-size: 20px;
  font-family: 'LCD', sans-serif;
  color: #AAB9D4;

  img {
    width: 15px;
    margin-right: 10px;
    margin-top: 6px;
    height: 17px;
  }
}

.button-box {
  display: flex;
  width: 50%;
  height: 100%;
  font-size: 16px;
  color: #C3E4FF;
  line-height: 2rem;

  div {
    width: 50%;
    height: 100%;
    text-align: center;
    background-image: url("~@/assets/ui/button-bg.png");
    background-size: 80% 100%;
    background-position: center;
    background-repeat: no-repeat;
    &.active-route {
      filter: hue-rotate(190deg); // 可以调整数值来改变色相
      // 或者使用其他方式改变颜色
      // background-image: url("~@/assets/ui/button-bg-active.png");
    }
  }
}

.header-right {
  right: 0;
  width: 38rem;
  height: calc(100% - 3.6rem);
  padding: 2.3rem 2rem 1.2rem 0;
}
</style>
