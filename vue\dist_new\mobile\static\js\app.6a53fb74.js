(function(t){function e(e){for(var i,n,r=e[0],l=e[1],c=e[2],d=0,u=[];d<r.length;d++)n=r[d],Object.prototype.hasOwnProperty.call(s,n)&&s[n]&&u.push(s[n][0]),s[n]=0;for(i in l)Object.prototype.hasOwnProperty.call(l,i)&&(t[i]=l[i]);m&&m(e);while(u.length)u.shift()();return o.push.apply(o,c||[]),a()}function a(){for(var t,e=0;e<o.length;e++){for(var a=o[e],i=!0,n=1;n<a.length;n++){var r=a[n];0!==s[r]&&(i=!1)}i&&(o.splice(e--,1),t=l(l.s=a[0]))}return t}var i={},n={app:0},s={app:0},o=[];function r(t){return l.p+"static/js/"+({}[t]||t)+"."+{"chunk-55a3b784":"8a18bef4"}[t]+".js"}function l(e){if(i[e])return i[e].exports;var a=i[e]={i:e,l:!1,exports:{}};return t[e].call(a.exports,a,a.exports,l),a.l=!0,a.exports}l.e=function(t){var e=[],a={"chunk-55a3b784":1};n[t]?e.push(n[t]):0!==n[t]&&a[t]&&e.push(n[t]=new Promise((function(e,a){for(var i="static/css/"+({}[t]||t)+"."+{"chunk-55a3b784":"e375c2f8"}[t]+".css",s=l.p+i,o=document.getElementsByTagName("link"),r=0;r<o.length;r++){var c=o[r],d=c.getAttribute("data-href")||c.getAttribute("href");if("stylesheet"===c.rel&&(d===i||d===s))return e()}var u=document.getElementsByTagName("style");for(r=0;r<u.length;r++){c=u[r],d=c.getAttribute("data-href");if(d===i||d===s)return e()}var m=document.createElement("link");m.rel="stylesheet",m.type="text/css",m.onload=e,m.onerror=function(e){var i=e&&e.target&&e.target.src||s,o=new Error("Loading CSS chunk "+t+" failed.\n("+i+")");o.code="CSS_CHUNK_LOAD_FAILED",o.request=i,delete n[t],m.parentNode.removeChild(m),a(o)},m.href=s;var f=document.getElementsByTagName("head")[0];f.appendChild(m)})).then((function(){n[t]=0})));var i=s[t];if(0!==i)if(i)e.push(i[2]);else{var o=new Promise((function(e,a){i=s[t]=[e,a]}));e.push(i[2]=o);var c,d=document.createElement("script");d.charset="utf-8",d.timeout=120,l.nc&&d.setAttribute("nonce",l.nc),d.src=r(t);var u=new Error;c=function(e){d.onerror=d.onload=null,clearTimeout(m);var a=s[t];if(0!==a){if(a){var i=e&&("load"===e.type?"missing":e.type),n=e&&e.target&&e.target.src;u.message="Loading chunk "+t+" failed.\n("+i+": "+n+")",u.name="ChunkLoadError",u.type=i,u.request=n,a[1](u)}s[t]=void 0}};var m=setTimeout((function(){c({type:"timeout",target:d})}),12e4);d.onerror=d.onload=c,document.head.appendChild(d)}return Promise.all(e)},l.m=t,l.c=i,l.d=function(t,e,a){l.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:a})},l.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},l.t=function(t,e){if(1&e&&(t=l(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(l.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)l.d(a,i,function(e){return t[e]}.bind(null,i));return a},l.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return l.d(e,"a",e),e},l.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},l.p="",l.oe=function(t){throw console.error(t),t};var c=window["webpackJsonp"]=window["webpackJsonp"]||[],d=c.push.bind(c);c.push=e,c=c.slice();for(var u=0;u<c.length;u++)e(c[u]);var m=d;o.push([0,"chunk-vendors"]),a()})({0:function(t,e,a){t.exports=a("56d7")},"01d7":function(t,e,a){},"0369":function(t,e,a){t.exports=a.p+"static/img/map-icon1.a23abc1b.png"},"05eb":function(t,e,a){t.exports=a.p+"static/img/jiangyl.738319d1.png"},"0814":function(t,e,a){"use strict";a("e34b")},"09d8":function(t,e,a){t.exports=a.p+"static/img/device-icon-ph.016ea6f2.png"},"0bb7":function(t,e,a){"use strict";a("6d04")},"0f5a":function(t,e,a){"use strict";a("4765")},"101a":function(t,e,a){"use strict";a("1e24")},1661:function(t,e,a){t.exports=a.p+"static/img/wendu.3da24f6b.png"},"17e8":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"d",(function(){return u})),a.d(e,"c",(function(){return m})),a.d(e,"b",(function(){return p}));a("d3b7"),a("caad"),a("2532"),a("ac1f"),a("00b4"),a("a434"),a("159b");var i=[];function n(){var t=navigator.userAgent.toLowerCase(),e=["android","iphone","ipad","ipod","blackberry","windows phone","mobile","webos","opera mini"],a=e.some((function(e){return t.includes(e)})),i=window.innerWidth<=768,n="ontouchstart"in window||navigator.maxTouchPoints>0;return a||i&&n}function s(){navigator.userAgent.toLowerCase();var t=window.innerWidth;return n()?t>=768&&t<=1024?"tablet":"mobile":"desktop"}function o(){return/iPad|iPhone|iPod/.test(navigator.userAgent)}function r(){return/Android/.test(navigator.userAgent)}function l(){return/MicroMessenger/i.test(navigator.userAgent)}var c=s(),d=!1;function u(t){"function"===typeof t&&i.push(t),d||f()}function m(t){var e=i.indexOf(t);e>-1&&i.splice(e,1)}function f(){d||(d=!0,window.addEventListener("resize",h),window.screen&&window.screen.orientation?window.screen.orientation.addEventListener("change",h):window.addEventListener("orientationchange",h))}function h(){setTimeout((function(){var t=s(),e=n();if(t!==c){var a=c;c=t,i.forEach((function(i){try{i({oldDevice:a,newDevice:t,isMobile:e,width:window.innerWidth,height:window.innerHeight})}catch(n){console.error("设备变化回调执行错误:",n)}}))}}),100)}function p(){return{type:s(),isMobile:n(),isIOS:o(),isAndroid:r(),isWeChat:l(),width:window.innerWidth,height:window.innerHeight,userAgent:navigator.userAgent}}},"1aa5":function(t,e,a){t.exports=a.p+"static/img/device-icon-o2.10969ada.png"},"1afe":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAbCAYAAABvCO8sAAAAAXNSR0IArs4c6QAABNJJREFUSEulln9QVFUUx7/n7S9XQHZjyRXEAHWaUiSJyl80IIRTjuOPwAqVRixF/SeHGtNRx0wmnWnqnybEJCZNmtZSZ8K1iRALx7B0NIMZHSTA5JeLCynyw2Xfae4DlrfLxg+9f9133jnfzz3n3nveI4xiXGTWxRO5RuHq18Xh4KAu9J4CEE3DiZQxayNv9zYAMGkkbdSUUGp8GGhNiytFAkpE7LBAkZmp2dUNQJLYHRkdZqx/GKCIudHU8yNA4QrweiNbWO75EKDnwXxZq3Ntn2YNui3e1dRwsDwe+umTyDEW2LVbPekA3gOj0d3ryp4RFdjsybCqvvtvBqJUgg3s7n02JjqwZSyQAd/Kuq4kEJ1R682YYphCRLKS4Z+1XewrzMC2Z6KM+4T94kXW6UM6F4NoIWSKZoKlz59FFWpZlkpiow12Idiv9zmAjWpNNyMyLtpYrwAv3egcCiTeFD81IO9S9f3NINoKIGKEbOuIaW/cdGPB77UdVo1bavL4Ey7cvWlckJREvQrwwvX7vsB6nSQ/55KljwCsG0tZGTg+58mAVyuudcwiohwidhq6AnbExtJ9zx6eq7qbQCR9IbIg4C9Jwnq3zHsAWjoWmMq3av7TgTP9xXpdi/Kr7eaEWaa2s5X3bGCIU/YooyQxJijVV2DIPfz58r+rQPT1o5A8scyrU2YHH1VreQFt59k4wXC3HYB+MIhukcTHwagDk4bBMSBkjmZBBO4K4eDg+PjBtugFLK5oywJRwaAYn9KN63ltUaxV2fCBUfyHcyZk6RyA4JHADF6x5AXziQE/L+DJ8202hmfvnMvnmUOEY3F5u9klIZcA57L5ph3CdvKcM4El+nVEIKFwxVxzll+grfzOFQJilZfMB9JftCiX11buPEPgJMVOlJ2+4LF8MT1WfqeJQRUSuXMhSyFM2AVgns8irqYnhPRpqpu33V5taB9vFq1MKRMDm1YlWvLEvOhsaweAgP6AY28kWlYWlrWZDHBnZyRZ9hWVtS4Dc1vGwtBfis62iq9LmApak5FomTYEWFhWO04jK71zQp8wvb8m2bJfzA+XOsQeju9fyKE3k0PfFvOvSh3fEmgywPPA/FZmyuMFh0sdosNY1cDM5NChQOHw5U+OSoBn9AlT6brU0JQ+e8sHAIlygUieu/Yla0Wf/bbSoRi0NKjdcqrD5BB76lNSqspKDfU0Aa9Dc9DeIk7TsoHVMfOcDYutF8Rzvr1lqqzhexsXWZXP1kF7i7iraTpjp2ltUlR3/unmLcSUAyDcZw+Prn9l4uohJRWGz4qb3yHGp6qATmbKdrn1P2xZbhb3E3knmiJlLbIA2gnQabdG+7r+wYPJTBqDTHIcCIe8gbR685KJnsvvlWG+zRncre9RhH2G+OrXglkLoumqd10AjKpnUWK1Jo/TcOCGJWGdfjMUxo+/b9xOQK4f6NhNjJyctLBP1IF+/2n2H2uoBuA5WWMnKRFXtqaHz/aN9Qvc+01DhET8m58DMFr2P3pZins3I6x1VEDhlHukbpKbtKUgfmq0lH6/y7IOKbtXRjj9xQ37mygCdh25uRPANp/D4UeL74l17lnzhNIs/m+MCBSBuwtuhrk0nAbwywCJvTWJZgugDZBqQCjW0YPvdmdOU+7ocOM/P4G8Kb7jXAAAAAAASUVORK5CYII="},"1be6":function(t,e,a){},"1e24":function(t,e,a){},"201b":function(t){t.exports=JSON.parse('{"color":["rgba(79,191,227,1)","rgba(249,170,114,1)","rgba(179,229,251,1)","rgba(250,210,95,1)","rgba(249,170,114,1)","rgba(79,191,227,1)","#cda819","#32a487"],"backgroundColor":"rgba(0,0,0,0)","textStyle":{},"title":{"textStyle":{"color":"#bddbf1"},"subtextStyle":{"color":"#bddbf1"}},"line":{"itemStyle":{"borderWidth":1},"lineStyle":{"width":2},"symbolSize":4,"symbol":"emptyCircle","smooth":false},"radar":{"itemStyle":{"borderWidth":1},"lineStyle":{"width":2},"symbolSize":4,"symbol":"emptyCircle","smooth":false},"bar":{"itemStyle":{"barBorderWidth":"0","barBorderColor":"#cccccc"}},"pie":{"itemStyle":{"borderWidth":"0","borderColor":"#cccccc"}},"scatter":{"itemStyle":{"borderWidth":"0","borderColor":"#cccccc"}},"boxplot":{"itemStyle":{"borderWidth":"0","borderColor":"#cccccc"}},"parallel":{"itemStyle":{"borderWidth":"0","borderColor":"#cccccc"}},"sankey":{"itemStyle":{"borderWidth":"0","borderColor":"#cccccc"}},"funnel":{"itemStyle":{"borderWidth":"0","borderColor":"#cccccc"}},"gauge":{"itemStyle":{"borderWidth":"0","borderColor":"#cccccc"}},"candlestick":{"itemStyle":{"color":"#c12e34","color0":"#2b821d","borderColor":"#c12e34","borderColor0":"#2b821d","borderWidth":1}},"graph":{"itemStyle":{"borderWidth":"0","borderColor":"#cccccc"},"lineStyle":{"width":1,"color":"#aaa"},"symbolSize":4,"symbol":"emptyCircle","smooth":false,"color":["rgba(79,191,227,1)","rgba(249,170,114,1)","rgba(179,229,251,1)","rgba(250,210,95,1)","rgba(249,170,114,1)","rgba(79,191,227,1)","#cda819","#32a487"],"label":{"color":"#000000"}},"map":{"itemStyle":{"areaColor":"#ddd","borderColor":"#eee","borderWidth":0.5},"label":{"color":"#c12e34"},"emphasis":{"itemStyle":{"areaColor":"#e6b600","borderColor":"#ddd","borderWidth":1},"label":{"color":"#c12e34"}}},"geo":{"itemStyle":{"areaColor":"#ddd","borderColor":"#eee","borderWidth":0.5},"label":{"color":"#c12e34"},"emphasis":{"itemStyle":{"areaColor":"#e6b600","borderColor":"#ddd","borderWidth":1},"label":{"color":"#c12e34"}}},"categoryAxis":{"axisLine":{"show":true,"lineStyle":{"color":"#2c5199"}},"axisTick":{"show":false,"lineStyle":{"color":"#333"}},"axisLabel":{"show":true,"color":"#bddbf1"},"splitLine":{"show":false,"lineStyle":{"color":["#ccc"]}},"splitArea":{"show":false,"areaStyle":{"color":["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},"valueAxis":{"axisLine":{"show":false,"lineStyle":{"color":"#333"}},"axisTick":{"show":false,"lineStyle":{"color":"#333"}},"axisLabel":{"show":true,"color":"#bddbf1"},"splitLine":{"show":true,"lineStyle":{"color":["rgba(44,81,153,0.46)"]}},"splitArea":{"show":false,"areaStyle":{"color":["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},"logAxis":{"axisLine":{"show":false,"lineStyle":{"color":"#333"}},"axisTick":{"show":false,"lineStyle":{"color":"#333"}},"axisLabel":{"show":false,"color":"#333"},"splitLine":{"show":false,"lineStyle":{"color":["#ccc"]}},"splitArea":{"show":false,"areaStyle":{"color":["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},"timeAxis":{"axisLine":{"show":false,"lineStyle":{"color":"#333"}},"axisTick":{"show":false,"lineStyle":{"color":"#333"}},"axisLabel":{"show":true,"color":"#333"},"splitLine":{"show":false,"lineStyle":{"color":["#ccc"]}},"splitArea":{"show":false,"areaStyle":{"color":["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},"toolbox":{"iconStyle":{"borderColor":"#bddbf1"},"emphasis":{"iconStyle":{"borderColor":"#bddbf1"}}},"legend":{"textStyle":{"color":"#bddbf1"}},"tooltip":{"axisPointer":{"lineStyle":{"color":"#bddbf1","width":""},"crossStyle":{"color":"#bddbf1","width":""}}},"timeline":{"lineStyle":{"color":"#005eaa","width":1},"itemStyle":{"color":"#005eaa","borderWidth":1},"controlStyle":{"color":"#005eaa","borderColor":"#005eaa","borderWidth":0.5},"checkpointStyle":{"color":"#005eaa","borderColor":"#316bc2"},"label":{"color":"#005eaa"},"emphasis":{"itemStyle":{"color":"#005eaa"},"controlStyle":{"color":"#005eaa","borderColor":"#005eaa","borderWidth":0.5},"label":{"color":"#005eaa"}}},"visualMap":{"color":["#bddbf1","#bddbf1"]},"dataZoom":{"backgroundColor":"rgba(47,69,84,0)","dataBackgroundColor":"rgba(47,69,84,0.3)","fillerColor":"rgba(167,183,204,0.4)","handleColor":"#a7b7cc","handleSize":"100%","textStyle":{"color":"#333333"}},"markPoint":{"label":{"color":"#000000"},"emphasis":{"label":{"color":"#000000"}}}}')},"210b":function(t,e,a){"use strict";a("b052")},"23eb":function(t,e,a){"use strict";a("c021")},"242c":function(t,e,a){"use strict";a("64ae")},2523:function(t,e,a){},2935:function(t,e){t.exports="data:image/png;base64,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"},"2a7e":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAbCAYAAABvCO8sAAAAAXNSR0IArs4c6QAABNJJREFUSEulln9QVFUUx7/n7S9XQHZjyRXEAHWaUiSJyl80IIRTjuOPwAqVRixF/SeHGtNRx0wmnWnqnybEJCZNmtZSZ8K1iRALx7B0NIMZHSTA5JeLCynyw2Xfae4DlrfLxg+9f9133jnfzz3n3nveI4xiXGTWxRO5RuHq18Xh4KAu9J4CEE3DiZQxayNv9zYAMGkkbdSUUGp8GGhNiytFAkpE7LBAkZmp2dUNQJLYHRkdZqx/GKCIudHU8yNA4QrweiNbWO75EKDnwXxZq3Ntn2YNui3e1dRwsDwe+umTyDEW2LVbPekA3gOj0d3ryp4RFdjsybCqvvtvBqJUgg3s7n02JjqwZSyQAd/Kuq4kEJ1R682YYphCRLKS4Z+1XewrzMC2Z6KM+4T94kXW6UM6F4NoIWSKZoKlz59FFWpZlkpiow12Idiv9zmAjWpNNyMyLtpYrwAv3egcCiTeFD81IO9S9f3NINoKIGKEbOuIaW/cdGPB77UdVo1bavL4Ey7cvWlckJREvQrwwvX7vsB6nSQ/55KljwCsG0tZGTg+58mAVyuudcwiohwidhq6AnbExtJ9zx6eq7qbQCR9IbIg4C9Jwnq3zHsAWjoWmMq3av7TgTP9xXpdi/Kr7eaEWaa2s5X3bGCIU/YooyQxJijVV2DIPfz58r+rQPT1o5A8scyrU2YHH1VreQFt59k4wXC3HYB+MIhukcTHwagDk4bBMSBkjmZBBO4K4eDg+PjBtugFLK5oywJRwaAYn9KN63ltUaxV2fCBUfyHcyZk6RyA4JHADF6x5AXziQE/L+DJ8202hmfvnMvnmUOEY3F5u9klIZcA57L5ph3CdvKcM4El+nVEIKFwxVxzll+grfzOFQJilZfMB9JftCiX11buPEPgJMVOlJ2+4LF8MT1WfqeJQRUSuXMhSyFM2AVgns8irqYnhPRpqpu33V5taB9vFq1MKRMDm1YlWvLEvOhsaweAgP6AY28kWlYWlrWZDHBnZyRZ9hWVtS4Dc1vGwtBfis62iq9LmApak5FomTYEWFhWO04jK71zQp8wvb8m2bJfzA+XOsQeju9fyKE3k0PfFvOvSh3fEmgywPPA/FZmyuMFh0sdosNY1cDM5NChQOHw5U+OSoBn9AlT6brU0JQ+e8sHAIlygUieu/Yla0Wf/bbSoRi0NKjdcqrD5BB76lNSqspKDfU0Aa9Dc9DeIk7TsoHVMfOcDYutF8Rzvr1lqqzhexsXWZXP1kF7i7iraTpjp2ltUlR3/unmLcSUAyDcZw+Prn9l4uohJRWGz4qb3yHGp6qATmbKdrn1P2xZbhb3E3knmiJlLbIA2gnQabdG+7r+wYPJTBqDTHIcCIe8gbR685KJnsvvlWG+zRncre9RhH2G+OrXglkLoumqd10AjKpnUWK1Jo/TcOCGJWGdfjMUxo+/b9xOQK4f6NhNjJyctLBP1IF+/2n2H2uoBuA5WWMnKRFXtqaHz/aN9Qvc+01DhET8m58DMFr2P3pZins3I6x1VEDhlHukbpKbtKUgfmq0lH6/y7IOKbtXRjj9xQ37mygCdh25uRPANp/D4UeL74l17lnzhNIs/m+MCBSBuwtuhrk0nAbwywCJvTWJZgugDZBqQCjW0YPvdmdOU+7ocOM/P4G8Kb7jXAAAAAAASUVORK5CYII="},"2b1e":function(t,e,a){},"2b7b":function(t,e,a){"use strict";a("be27")},"2e0d":function(t,e,a){},"2e81":function(t,e,a){},"317e":function(t,e,a){t.exports=a.p+"static/img/bg.a516f844.png"},3302:function(t,e,a){},"378c":function(t,e,a){t.exports=a.p+"static/img/top-info-fs.a5c88f91.png"},3876:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"chartContainer",staticStyle:{width:"100%",height:"100%"}})},n=[],s=(a("b64b"),a("e39c"),a("ac1f"),a("00b4"),a("99af"),a("fb6a"),a("d3b7"),a("159b"),a("313e"));function o(t,e){var a="",i=/^#[\da-f]{6}$/i;return i.test(t)&&(a="rgba(".concat(parseInt("0x"+t.slice(1,3)),",").concat(parseInt("0x"+t.slice(3,5)),",").concat(parseInt("0x"+t.slice(5,7)),",").concat(e,")")),a}function r(t,e,a,i,n,r,l,c,d){return{color:[a,n,l],tooltip:{trigger:"axis",formatter:function(t){var e='<div style="color: #fff;font-size: 14px;line-height: 24px;padding-bottom: 5px;border-bottom: 1px solid rgba(255,255,255,0.3);margin-bottom: 0px;">\n        '.concat(t[0].axisValue,"\n    </div>");return t.forEach((function(t){e+='<div style="color: #fff;font-size: 14px;line-height: 24px;">\n                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'.concat(t.color,';"></span>\n                    ').concat(t.seriesName,': \n                    <span style="color:#fff;font-weight:700;font-size: 18px">').concat(t.value,"</span>")})),e},extraCssText:"background: rgba(28, 46, 81,0.4);  border: none; border-radius: 4px;",axisPointer:{type:"shadow",shadowStyle:{color:" rgba(28, 46, 81,0.4)",shadowColor:"rgba(225,225,225,1)",shadowBlur:5}}},grid:{top:30,left:15,right:5,bottom:10,containLabel:!0},xAxis:[{type:"category",boundaryGap:!1,axisLabel:{formatter:"{value}",textStyle:{color:"#fff"},rotate:45,interval:0},axisLine:{lineStyle:{color:"rgba(44, 81, 153, 1)"}},data:e}],yAxis:[{type:"value",max:c,min:d,axisLabel:{textStyle:{color:"#fff"}},nameTextStyle:{color:"#fff",fontSize:12,lineHeight:40},splitLine:{lineStyle:{type:"dashed",color:"rgba(44, 81, 153, 1)"}},axisLine:{show:!1},axisTick:{show:!1}}],series:[{name:"实际值",type:"line",smooth:!0,symbol:"none",symbolSize:8,zlevel:3,lineStyle:{normal:{color:a,shadowBlur:3,shadowColor:o(a,.5),shadowOffsetY:8}},areaStyle:{normal:{color:new s["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:o(a,.2)},{offset:1,color:o(a,0)}],!1),shadowColor:o(a,.1),shadowBlur:10}},data:t},{name:"上限",type:"line",smooth:!0,symbol:"none",symbolSize:8,zlevel:3,lineStyle:{normal:{color:n,type:"dashed",width:1}},data:i},{name:"下限",type:"line",smooth:!0,symbol:"none",symbolSize:8,zlevel:3,lineStyle:{normal:{color:l,type:"dashed",width:1}},data:r}]}}var l={props:{Data:{type:Object,required:!0}},data:function(){return{chartInstance2:null,resizeObserver:null}},watch:{Data:{handler:function(t){t&&Object.keys(t).length>0&&this.updateChart()},deep:!0,immediate:!0}},mounted:function(){var t=this;this.$refs.chartContainer&&(this.chartInstance2=s["init"](this.$refs.chartContainer,this.$echartsConfig.theme),this.updateChart(),this.resizeObserver=new ResizeObserver((function(){t.chartInstance2&&t.chartInstance2.resize()})),this.resizeObserver.observe(this.$refs.chartContainer))},methods:{updateChart:function(){if(this.Data&&this.Data.data&&this.Data.xAxisData){var t=this.Data,e=t.xAxisData,a="";a="1"==t.id?"#fad25f":"2"==t.id?"#51b7de":"#fad25f";var i=r(t.data,e,a,t.data1,"#eb0e2e",t.data2,"#0bf00b",t.max,t.min);this.chartInstance2&&this.chartInstance2.setOption(i,!0)}else console.warn("数据不完整，无法更新图表")}},beforeDestroy:function(){this.chartInstance2&&this.chartInstance2.dispose(),this.resizeObserver&&this.$refs.chartContainer&&(this.resizeObserver.unobserve(this.$refs.chartContainer),this.resizeObserver.disconnect())}},c=l,d=a("2877"),u=Object(d["a"])(c,i,n,!1,null,null,null);e["a"]=u.exports},3967:function(t,e,a){t.exports=a.p+"static/img/touwei-icon2.460b1aad.png"},"3ce9":function(t,e,a){"use strict";a("ce4c")},"3ecc":function(t,e,a){},"40f4":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAARCAYAAAACCvahAAAAAXNSR0IArs4c6QAAAWtJREFUOE/Fk81LAmEQxp9ZpQ+CCOvUIbplJibRraBAWPMgXmrDP6Bj0KFzQXTr0KX+gQ4JK3TpkK5FhPdC2EW99QEdowgqjX0nXLPU3RU7NaeXmec38zDDS3CIlFYalfhzi0ALYEggyptC2k3GJm+b5dTOps6K4x7JvAEw1FqjdwnmzHI0VGrkbbCa0fMgzDs5AlBQosGwI6zmjDEIvnMB62lh+pXYdLn2bJmsakYYzDXL7kFiUZFDVzb4KFsY6IPnCUCPG9374R1MJPyvNriWSGf0DSbsO8JM28rS1I7rwqwGWWOPwZstDQgHihxc73iqRvH4sjzirVRjTCQJ8uaSsv+x3c3PwpiZ0pqxBqYAE1+sRoOnzeK0ZkQEiwQBD/3VymE8PvtmwWrG8EHiPBiBX4BeAFEESACYADDc1OzeY5pz37B+DkKk44lsRb6uw1md/wbW1f8MW3clXgHD16X9ZzCd2H5Vl7Al+wLTxX/OIGK47gAAAABJRU5ErkJggg=="},4105:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100"},[a("div",{ref:"scrollContainer",staticClass:"scroll-container",on:{mouseover:t.stopScrolling,mouseleave:t.startScrolling}},[a("div",{staticClass:"scroll-content",style:t.scrollStyle},[a("ul",{staticClass:"area-list"},[t._l(t.localBaselist,(function(e,i){return a("li",{key:i,class:["area-item flex-item cursor-pointer",{"odd-item":i%2===0}]},[t._m(0,!0),a("el-tooltip",{attrs:{content:e.content,placement:"top",effect:"dark",enterable:!1}},[a("div",{staticClass:"flex-content1 information-title d-flex align-items-center"},[t._v(t._s(e.content))])]),a("div",{staticClass:"d-flex align-items-center text-clock h-100"},[a("i",{staticClass:"el-icon-time"}),t._v(t._s(e.time)+" ")])],1)})),t.needExtraItem?a("li",{key:"extra-item",staticClass:"area-item flex-item"}):t._e()],2),t.shouldScroll?a("ul",{staticClass:"area-list"},[t._l(t.localBaselist,(function(e,i){return a("li",{key:i,class:["area-item flex-item cursor-pointer",{"odd-item":i%2===0}]},[t._m(1,!0),a("el-tooltip",{attrs:{content:e.content,placement:"top",effect:"dark",enterable:!1}},[a("div",{staticClass:"flex-content1 information-title d-flex align-items-center"},[t._v(t._s(e.content))])]),a("div",{staticClass:"d-flex align-items-center text-clock h-100"},[a("i",{staticClass:"el-icon-time"}),t._v(t._s(e.time)+" ")])],1)})),t.needExtraItem?a("li",{key:"extra-item",staticClass:"area-item flex-item"}):t._e()],2):t._e()])])])},n=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"d-flex h-100 align-items-center justify-content-center"},[i("img",{attrs:{src:a("e864"),alt:""}})])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"d-flex h-100 align-items-center justify-content-center"},[i("img",{attrs:{src:a("e864"),alt:""}})])}],s=a("5530"),o=a("ade3"),r=(a("b0c0"),a("d81d"),a("ac1f"),a("5319"),a("5c96")),l={components:Object(o["a"])({},r["Tooltip"].name,r["Tooltip"]),props:{baselist:{type:Array,required:!0}},data:function(){return{scrollOffset:0,scrollInterval:null,contentHeight:0,localBaselist:[]}},computed:{needExtraItem:function(){return this.baselist.length%2!==0},shouldScroll:function(){return this.baselist.length>5},scrollStyle:function(){return this.shouldScroll?{transform:"translateY(".concat(-this.scrollOffset,"px)")}:{}}},watch:{baselist:{immediate:!0,handler:function(t){this.localBaselist=t.map((function(t){return Object(s["a"])(Object(s["a"])({},t),{},{time:t.time?t.time.replace("T"," "):t.time})})),this.resetScroll()},deep:!0}},mounted:function(){this.resetScroll()},methods:{resetScroll:function(){var t=this;this.stopScrolling(),this.$nextTick((function(){var e=t.$refs.scrollContainer;t.contentHeight=t.shouldScroll?e.children[0].offsetHeight/2:0,t.scrollOffset=0,t.shouldScroll&&t.startScrolling()}))},startScrolling:function(){var t=this;if(this.shouldScroll){var e=.5,a=performance.now(),i=function i(n){var s=n-a;a=n,t.scrollOffset+=e*(s/16.67),t.scrollOffset>=t.contentHeight&&(t.scrollOffset=0),t.scrollInterval=requestAnimationFrame(i)};this.scrollInterval=requestAnimationFrame(i)}},stopScrolling:function(){this.scrollInterval&&(cancelAnimationFrame(this.scrollInterval),this.scrollInterval=null)}},beforeDestroy:function(){this.stopScrolling()}},c=l,d=(a("f227"),a("2877")),u=Object(d["a"])(c,i,n,!1,null,"53860352",null);e["a"]=u.exports},4240:function(t,e,a){t.exports=a.p+"static/img/map-icon2.3f65ce51.png"},4360:function(t,e,a){"use strict";var i=a("2b0e"),n=a("2f62"),s=(a("b0c0"),{deviceId:null,deviceName:"",deviceType:"",fullDeviceData:null,baseId:"1",chartData:null}),o={SET_DEVICE_INFO:function(t,e){t.deviceId=e.id||null,t.deviceName=e.name||"",t.deviceType=e.type||"",t.fullDeviceData=e.itemData||null},SET_BASE_ID:function(t,e){t.baseId=e},SET_CHART_DATA:function(t,e){t.chartData=e}},r={updateDeviceInfo:function(t,e){var a=t.commit;a("SET_DEVICE_INFO",e)},updateBaseId:function(t,e){var a=t.commit;a("SET_BASE_ID",e)},updateChartData:function(t,e){var a=t.commit;a("SET_CHART_DATA",e)}},l={deviceInfo:function(t){return{id:t.deviceId,name:t.deviceName,type:t.deviceType,fullDeviceData:t.fullDeviceData}},baseId:function(t){return t.baseId},chartData:function(t){return t.chartData}},c={namespaced:!0,state:s,mutations:o,actions:r,getters:l};i["default"].use(n["a"]);e["a"]=new n["a"].Store({state:{token:"",base_id:null},mutations:{set_token:function(t,e){t.token=e,sessionStorage.setItem("token",e)},del_token:function(t){t.token="",sessionStorage.removeItem("token")},setBaseId:function(t,e){t.base_id=e,sessionStorage.setItem("base_id",e)},clearBase:function(t){t.base_id=null,sessionStorage.removeItem("base_id")}},actions:{updateBaseId:function(t,e){var a=t.commit;a("setBaseId",e)},initializeStore:function(t){var e=t.commit,a=sessionStorage.getItem("token"),i=sessionStorage.getItem("base_id");a&&e("set_token",a),i&&e("setBaseId",parseInt(i,10))}},getters:{getBaseId:function(t){return t.base_id}},modules:{device:c}})},"46a0":function(t,e,a){},"470a":function(t,e,a){t.exports=a.p+"static/img/jiankong(1).eea8bb80.png"},4765:function(t,e,a){},"4c23":function(t,e,a){t.exports=a.p+"static/img/top-info-wd.a9dd02ce.png"},5141:function(t,e,a){t.exports=a.p+"static/img/top-info-ql.03525532.png"},"53df":function(t,e,a){},"53e4":function(t,e,a){},"56d7":function(t,e,a){"use strict";a.r(e);a("e260"),a("e6cf"),a("cca6"),a("a79d");var i=a("2b0e"),n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100 flex-item",attrs:{id:"app"}},[t.showPCPage?[t.$route.meta.withMenu?[a("div",{staticClass:"left-wrapper flex-label h-100 flex-vertical bg-deep"},[a("div",{staticClass:"logo-box flex-header pr-3 flex-item align-items-center",class:{fold:t.isCollapse}},[a("div",{staticClass:"logo flex-label d-flex justify-content-center"},[a("svg",{staticClass:"icon",attrs:{t:"1639791648958",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"20138",width:"1.6rem",height:"1.6rem"}},[a("path",{attrs:{d:"M844 912H112V112h68v452.34s80.753-52.55 334.716 0 329.284 0 329.284 0V112h68v800h-68zM523.671 516C288.7 447.63 213 516 213 516v-67s129.5-79.04 310.671 0C685.432 519.58 815 449 815 449v67s-101.384 55.27-291.329 0z m0-110C288.7 337.63 213 406 213 406v-67s129.5-79.04 310.671 0C685.432 409.58 815 339 815 339v67s-101.384 55.27-291.329 0z m0-111C288.7 226.63 213 295 213 295v-67s129.5-79.04 310.671 0C685.432 298.58 815 228 815 228v67s-101.384 55.27-291.329 0z",fill:"#ffffff","p-id":"20139"}})])]),a("div",{staticClass:"flex-content overflow-hidden text-nowrap"},[t._v(t._s(t.base_name))])]),a("el-menu",{staticClass:"flex-content el-menu-vertical-demo h-100 border-0",attrs:{collapse:t.isCollapse,"default-active":"2","background-color":"#031E3E","text-color":"#fff",router:"","active-text-color":"#fff"}},[a("el-menu-item",{attrs:{index:"/detail"},on:{click:function(e){return t.navigateToDetail()}}},[a("i",{staticClass:"el-icon-menu"}),a("span",{attrs:{slot:"title"},slot:"title"},[t._v("返回上级")])]),a("el-submenu",{attrs:{index:"2"}},[a("template",{slot:"title"},[a("i",{staticClass:"el-icon-location"}),a("span",[t._v("数据中心")])]),a("el-menu-item",{attrs:{index:"/data/history"},on:{click:function(e){return t.handleMenuClick("/data/history")}}},[t._v("水质历史数据")]),a("el-menu-item",{attrs:{index:"/data/history2"},on:{click:function(e){return t.handleMenuClick("/data/history2")}}},[t._v("气象历史数据")]),a("el-menu-item",{attrs:{index:"/data/report"},on:{click:function(e){return t.handleMenuClick("/data/report")}}},[t._v("具体设备数值表")]),a("el-menu-item",{attrs:{index:"/data/tousi"},on:{click:function(e){return t.handleMenuClick("/data/tousi")}}},[t._v("投饲计划")])],2),a("el-submenu",{attrs:{index:"设备中心"}},[a("template",{slot:"title"},[a("i",{staticClass:"el-icon-document"}),a("span",[t._v("设备中心")])]),a("el-menu-item",{attrs:{index:"/device/manage"},on:{click:function(e){return t.handleMenuClick("/device/manage")}}},[t._v("设备管理")]),a("el-menu-item",{attrs:{index:"/device/camera"},on:{click:function(e){return t.handleMenuClick("/device/camera")}}},[t._v("摄像头管理")])],2),a("el-menu-item",{attrs:{index:"/smartCultiver/homeview"}},[a("i",{staticClass:"el-icon-setting"}),a("span",{attrs:{slot:"title"},slot:"title"},[t._v("智能养殖")])]),a("el-submenu",{attrs:{index:"用户管理"}},[a("template",{slot:"title"},[a("i",{staticClass:"el-icon-user-solid"}),a("span",{attrs:{slot:"title"},slot:"title"},[t._v("用户管理")])]),a("el-menu-item",{attrs:{index:"/user/add"},on:{click:function(e){return t.handleMenuClick("/user/add")}}},[t._v("新增用户")])],2)],1),a("div",{staticClass:"fold-box flex-footer py-1 text-center cursor-pointer",class:{fold:t.isCollapse},on:{click:t.onToggleCollapse}},[t.isCollapse?a("i",{staticClass:"el-icon-s-unfold text-plus"}):a("i",{staticClass:"el-icon-s-fold text-plus"})])],1),a("div",{staticClass:"flex-content flex-vertical"},[a("Nav"),a("div",{staticClass:"flex-content"},[a("router-view")],1)],1)]:[a("router-view")]]:t.showMobilePage?[a("div",{staticClass:"mobile-container"},[a("router-view")],1)]:t._e()],2)},s=[],o=a("1da1"),r=(a("96cf"),a("17e8")),l=a("6069"),c={data:function(){return{isCollapse:!1,isMobile:!1,showPCPage:!0,showMobilePage:!1,deviceChangeHandler:null}},mounted:function(){this.initDeviceDetection()},beforeDestroy:function(){this.cleanupDeviceDetection()},methods:{initDeviceDetection:function(){this.checkDevice(),this.deviceChangeHandler=this.handleDeviceChange.bind(this),Object(r["d"])(this.deviceChangeHandler)},cleanupDeviceDetection:function(){this.deviceChangeHandler&&(Object(r["c"])(this.deviceChangeHandler),this.deviceChangeHandler=null)},checkDevice:function(){var t=Object(r["b"])();this.isMobile=t.isMobile,this.showPCPage=!this.isMobile,this.showMobilePage=this.isMobile},handleDeviceChange:function(t){console.log("设备发生变化:",t);var e=t.isMobile,a=this.$route.path;this.isMobile=e,this.showPCPage=!e,this.showMobilePage=e,Object(l["d"])(a,e)&&this.performAutoRedirect(a,e)},performAutoRedirect:function(t,e){var a=this;return Object(o["a"])(regeneratorRuntime.mark((function i(){var n;return regeneratorRuntime.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,n=a.$route.query,i.next=4,Object(l["c"])(a.$router,t,e,n);case 4:i.next=9;break;case 6:i.prev=6,i.t0=i["catch"](0),console.error("自动跳转失败:",i.t0);case 9:case"end":return i.stop()}}),i,null,[[0,6]])})))()},handleMenuClick:function(t){this.$router.push({path:t,query:{base_id:this.base_id,base_name:this.base_name}})},onToggleCollapse:function(){this.isCollapse=!this.isCollapse},navigateToDetail:function(){this.$route.query.base_name,this.$route.query.base_id;console.log(this.base_name),this.$router.push({path:"/detail",query:{base_id:this.base_id,base_name:this.base_name}}),console.log(this.base_name)}},watch:{"$route.query":{handler:function(t){this.base_id=t.base_id,this.base_name=t.base_name},immediate:!0}}},d=c,u=(a("210b"),a("2877")),m=Object(u["a"])(d,n,s,!1,null,"05b56a28",null),f=m.exports,h=a("a18c"),p=a("4360"),g=(a("2e0d"),a("5c96")),A=a.n(g),b=(a("0fae"),a("b970")),v=(a("157a"),a("2b1e"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("header",{staticClass:"flex-header flex-item justify-content-between align-items-center bg-deep px-4"},[a("div",{staticClass:"d-flex align-items-center"},[a("svg",{staticClass:"icon",attrs:{t:"1639791098228",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"18796",width:"1.4rem",height:"1.4rem"}},[a("path",{attrs:{d:"M830.135922 0H193.864078C86.990291 0 0 86.990291 0 193.864078v636.271844c0 106.873786 86.990291 193.864078 193.864078 193.864078h263.45631c19.883495 0 37.281553-17.398058 37.281554-37.281553s-17.398058-37.281553-37.281554-37.281554H193.864078C126.757282 949.436893 74.563107 894.757282 74.563107 830.135922V193.864078C74.563107 129.242718 129.242718 74.563107 193.864078 74.563107h636.271844c67.106796 0 119.300971 54.679612 119.300971 119.300971v464.776699c-12.427184-4.970874-22.368932-7.456311-34.796116-7.456311h-82.019418c-94.446602 0-171.495146 77.048544-171.495145 171.495146v106.873786c0 52.194175 42.252427 94.446602 94.446602 94.446602h74.563106c106.873786 0 193.864078-86.990291 193.864078-193.864078V193.864078c0-106.873786-86.990291-193.864078-193.864078-193.864078z m119.300971 830.135922c0 67.106796-54.679612 119.300971-119.300971 119.300971h-74.563106c-9.941748 0-19.883495-7.456311-19.883496-19.883495v-106.873786c0-52.194175 42.252427-96.932039 96.932039-96.932039h82.019418c19.883495 0 34.796117 14.912621 34.796116 34.796116v69.592233z",fill:"#ffffff","p-id":"18797"}}),a("path",{attrs:{d:"M211.262136 295.76699h631.300971c19.883495 0 37.281553-17.398058 37.281553-37.281553s-17.398058-37.281553-37.281553-37.281554H211.262136c-19.883495-2.485437-37.281553 14.912621-37.281553 34.796117s17.398058 39.76699 37.281553 39.76699zM211.262136 531.883495h631.300971c19.883495 0 37.281553-17.398058 37.281553-37.281553s-17.398058-37.281553-37.281553-37.281554H211.262136c-19.883495 0-37.281553 17.398058-37.281553 37.281554s17.398058 37.281553 37.281553 37.281553zM584.07767 695.92233h-372.815534c-19.883495 0-37.281553 17.398058-37.281553 37.281553S191.378641 770.485437 211.262136 770.485437h372.815534c19.883495 0 37.281553-17.398058 37.281553-37.281554s-17.398058-37.281553-37.281553-37.281553z",fill:"#ffffff","p-id":"18798"}})]),a("div",{staticClass:"ml-2"},[t._v(t._s(t.$route.meta.pathName))])]),a("div",{staticClass:"d-flex align-items-center"},[a("div",{staticClass:"mr-5 color-gray cursor-pointer",on:{click:t.onModify}},[t._v("修改密码")]),a("div",{staticClass:"mr-4"},[t._v("用户名")]),a("div",{staticClass:"logout-btn cursor-pointer d-flex align-items-center",on:{click:t.onLogout}},[a("svg",{staticClass:"icon mr-1",attrs:{t:"1636380494993",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"10082",width:"1.6rem",height:"1.6rem"}},[a("path",{attrs:{d:"M502.784 428.544a31.744 31.744 0 0 0 32.256-31.232V208.384a32.256 32.256 0 0 0-65.024 0v188.416a31.744 31.744 0 0 0 32.768 31.744z",fill:"#ffffff","p-id":"10083"}}),a("path",{attrs:{d:"M693.248 256a33.28 33.28 0 0 0-45.056 7.68 30.72 30.72 0 0 0 8.192 43.52 259.584 259.584 0 0 1 116.736 215.04 266.24 266.24 0 0 1-269.824 261.632 266.24 266.24 0 0 1-270.336-261.632A259.584 259.584 0 0 1 349.696 307.2 30.72 30.72 0 0 0 358.4 264.192a32.768 32.768 0 0 0-27.648-13.312 33.28 33.28 0 0 0-17.92 5.12 321.536 321.536 0 0 0-144.384 266.24 330.24 330.24 0 0 0 334.848 324.096 330.24 330.24 0 0 0 334.848-324.096A321.536 321.536 0 0 0 693.248 256z",fill:"#ffffff","p-id":"10084"}})]),a("span",[t._v("退出登录")])])]),a("el-dialog",{attrs:{title:"修改密码",visible:t.dialogVisible,width:"40%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("div",{staticClass:"d-flex justify-content-center"},[a("el-form",{ref:"passwordForm",staticClass:"w-60",attrs:{model:t.form,rules:t.rules,"label-width":"auto"}},[a("el-form-item",{attrs:{label:"旧密码",prop:"oldPwd"}},[a("el-input",{attrs:{type:"password","show-password":""},model:{value:t.form.oldPwd,callback:function(e){t.$set(t.form,"oldPwd",e)},expression:"form.oldPwd"}})],1),a("el-form-item",{attrs:{label:"新密码",prop:"newPwd"}},[a("el-input",{attrs:{type:"password","show-password":""},model:{value:t.form.newPwd,callback:function(e){t.$set(t.form,"newPwd",e)},expression:"form.newPwd"}})],1),a("el-form-item",{attrs:{label:"确认新密码",prop:"checkNewPwd"}},[a("el-input",{attrs:{type:"password","show-password":""},model:{value:t.form.checkNewPwd,callback:function(e){t.$set(t.form,"checkNewPwd",e)},expression:"form.checkNewPwd"}})],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:t.cancelModify}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.confirmModify}},[t._v("确 定")])],1)])],1)}),C=[],w=(a("d9e2"),{data:function(){return{dialogVisible:!1,form:{oldPwd:"",newPwd:"",checkNewPwd:""},rules:{oldPwd:[{required:!0,message:"请输入旧密码",trigger:"blur"}],newPwd:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],checkNewPwd:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:this.validateConfirmPassword,trigger:"blur"}]}}},methods:{onModify:function(){this.dialogVisible=!0},cancelModify:function(){this.dialogVisible=!1,this.resetForm()},confirmModify:function(){var t=this;this.$refs.passwordForm.validate((function(e){e&&(console.log("修改密码:",t.form),t.$message({message:"密码修改成功",type:"success"}),t.dialogVisible=!1,t.resetForm())}))},resetForm:function(){this.form={oldPwd:"",newPwd:"",checkNewPwd:""},this.$refs.passwordForm&&this.$refs.passwordForm.resetFields()},validateConfirmPassword:function(t,e,a){e!==this.form.newPwd?a(new Error("两次输入的密码不一致")):a()},onLogout:function(){var t=this;this.$confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){localStorage.removeItem("token"),localStorage.removeItem("userInfo");var e=Object(l["b"])(Object(r["a"])());t.$router.push(e),t.$message({message:"已退出登录",type:"success"})})).catch((function(){}))}}}),x=w,y=(a("e42c"),Object(u["a"])(x,v,C,!1,null,"0a758486",null)),E=y.exports,I=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("header",{staticClass:"border-box border-box"},[a("div",{staticClass:"w-100 h-100 d-flex align-items-center justify-content-center"},[t._t("default")],2),a("div",{staticClass:"flex-label right-content d-flex align-items-center pb-1"},[a("div",{staticClass:"text-lg mr-5"},[t._v(t._s(t.time))]),a("div",{staticClass:"d-flex align-items-center text-md",on:{click:function(e){return t.exitLogion()}}},[a("svg",{staticClass:"icon",attrs:{t:"1636380494993",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"10082",width:"2.1rem",height:"2.1rem"}},[a("path",{attrs:{d:"M502.784 428.544a31.744 31.744 0 0 0 32.256-31.232V208.384a32.256 32.256 0 0 0-65.024 0v188.416a31.744 31.744 0 0 0 32.768 31.744z",fill:"#ffffff","p-id":"10083"}}),a("path",{attrs:{d:"M693.248 256a33.28 33.28 0 0 0-45.056 7.68 30.72 30.72 0 0 0 8.192 43.52 259.584 259.584 0 0 1 116.736 215.04 266.24 266.24 0 0 1-269.824 261.632 266.24 266.24 0 0 1-270.336-261.632A259.584 259.584 0 0 1 349.696 307.2 30.72 30.72 0 0 0 358.4 264.192a32.768 32.768 0 0 0-27.648-13.312 33.28 33.28 0 0 0-17.92 5.12 321.536 321.536 0 0 0-144.384 266.24 330.24 330.24 0 0 0 334.848 324.096 330.24 330.24 0 0 0 334.848-324.096A321.536 321.536 0 0 0 693.248 256z",fill:"#ffffff","p-id":"10084"}})]),t._v(" 退出登录 ")])]),a("div",{staticClass:"bottom-box"},[a("Item",[a("div",{staticClass:"d-flex align-items-center mb-3 text-md"},[a("div",{staticClass:"d-flex justify-content-center align-items-center w-25"},[a("img",{staticClass:"mr-1",staticStyle:{width:"2rem",height:"2rem"},attrs:{src:t.wenduImg,alt:"temperature"}}),a("div",{staticClass:"mx-1"},[t._v(t._s(t.livetianqi.temperature||"温度")+"℃")])]),a("div",{staticClass:"d-flex justify-content-center align-items-center w-25"},[a("img",{staticClass:"mr-1",staticStyle:{width:"2rem",height:"2rem"},attrs:{src:t.shiduImg,alt:"shidu"}}),a("div",{staticClass:"mx-1"},[t._v(t._s(t.tianqi.shidu||"湿度"))])]),a("div",{staticClass:"d-flex justify-content-center align-items-center w-25"},[a("img",{staticClass:"mr-1",staticStyle:{width:"2rem",height:"2rem"},attrs:{src:t.fengxiangImg,alt:"fengxiang"}}),a("div",{staticClass:"mx-1"},[t._v(t._s(t.tianqi.fengxiang||"风向"))])]),a("div",{staticClass:"d-flex justify-content-center align-items-center w-25"},[a("img",{staticClass:"mr-1",staticStyle:{width:"2rem",height:"2rem"},attrs:{src:t.fengsuImg,alt:"fengsu"}}),a("div",{staticClass:"mx-1"},[t._v(t._s(t.tianqi.fengsu||"风力"))])])]),a("div",{staticClass:"d-flex align-items-center text-md"},[a("div",{staticClass:"d-flex justify-content-center align-items-center w-25"},[a("img",{staticClass:"mr-1",staticStyle:{width:"2rem",height:"2rem"},attrs:{src:t.guangzhaoImg,alt:"guangzhao"}}),a("div",{staticClass:"mx-1"},[t._v(t._s(t.tianqi.guangzhao||"光照"))])]),a("div",{staticClass:"d-flex justify-content-center align-items-center w-25"},[a("img",{staticClass:"mr-1",staticStyle:{width:"2rem",height:"2rem"},attrs:{src:t.jiangylImg,alt:"jiangyl"}}),a("div",{staticClass:"mx-1"},[t._v(t._s(t.tianqi.jiangyuliang||"降雨量"))])]),a("div",{staticClass:"d-flex justify-content-center align-items-center w-25"},[a("img",{staticClass:"mr-1",staticStyle:{width:"2rem",height:"2rem"},attrs:{src:t.daqiyImg,alt:"daqiya"}}),a("div",{staticClass:"mx-1"},[t._v(t._s(t.tianqi.qiya||"大气压"))])])])])],1)])},D=[],S={props:{tianqi:{type:Object,required:!0}},data:function(){return{time:"--",timer:"",wenduImg:a("1661"),shiduImg:a("d797"),fengxiangImg:a("788e"),fengsuImg:a("66f6"),guangzhaoImg:a("84e1"),jiangylImg:a("05eb"),daqiyImg:a("c460")}},created:function(){this.setTime()},methods:{setTime:function(){this.time=this.$dayjs().format("YYYY-MM-DD HH:mm:ss"),this.timer||(this.timer=setInterval(this.setTime,1e3))},exitLogion:function(){this.$router.push({path:"/login"})}}},_=S,k=(a("f2bf"),Object(u["a"])(_,I,D,!1,null,"1ed9acb4",null)),N=k.exports,B=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"chart-box flex-vertical w-100 h-100 pb-3 border-box grad-bg flex-vertical"},[[a("div",{staticClass:"border-item tl"}),a("div",{staticClass:"border-item tr"}),a("div",{staticClass:"border-item br"}),a("div",{staticClass:"border-item bl"})],t.title?a("div",{staticClass:"flex-header title-box flex-header text-left px-4 flex-item align-items-center"},[a("div",{staticClass:"line"}),a("div",{staticClass:"line"}),a("div",{staticClass:"line"}),a("div",{staticClass:"text-main font-weight-bold title"},[t._v(t._s(t.title))])]):t._e(),a("div",{staticClass:"flex-content px-4 pt-3 no-scroll"},[t._t("default")],2)],2)},M=[],T={props:{title:String}},L=T,O=(a("23eb"),Object(u["a"])(L,B,M,!1,null,"fc9d0bc4",null)),P=O.exports,Q=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("section",{staticClass:"grad-bg border-common px-3 py-1"},[a("div",{staticClass:"text-main color-gray"},[t._v(t._s(t.title))]),a("div",{staticClass:"color-white text-center",style:{fontSize:(t.size||1.875)+"rem"}},[t._v(t._s(t.content))])])},U=[],F=(a("a9e3"),{props:{title:String,content:[String,Number],size:[String,Number]},data:function(){return{}}}),z=F,j=(a("d962"),Object(u["a"])(z,Q,U,!1,null,"1026843d",null)),R=j.exports,Y={install:function(t){t.component("Nav",E),t.component("Head",N),t.component("Item",P),t.component("Des",R)}},q=a("5a0c"),W=a.n(q),H=a("862d"),G=a.n(H),X=a("ee98"),J=a.n(X),K=a("bc3a"),V=a.n(K),Z=a("313e"),$=a("201b");Z["registerTheme"]("customed",$);var tt={theme:"customed"},et={install:function(t){t.prototype.$echarts=Z,t.prototype.$echartsConfig=tt}},at=a("41d1");i["default"].prototype.$axios=V.a,i["default"].config.productionTip=!1,i["default"].use(J.a),i["default"].use(A.a),i["default"].use(b["a"]),i["default"].use(Y),i["default"].use(et),i["default"].use(at["a"]),i["default"].prototype.$dayjs=W.a,i["default"].prototype.$AMapLoader=G.a,i["default"].prototype.$echarts=Z,new i["default"]({router:h["a"],store:p["a"],created:function(){this.$store.dispatch("initializeStore")},render:function(t){return t(f)}}).$mount("#app")},"58fb":function(t,e,a){"use strict";a("7fdc")},5941:function(t,e,a){"use strict";a("53e4")},"59c1":function(t,e,a){},"59e0":function(t,e){t.exports="data:image/png;base64,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"},"5a4d":function(t,e,a){"use strict";a("daf1")},"5a8e":function(t,e,a){},"5ea1":function(t,e,a){t.exports=a.p+"static/img/touwei-icon1.37b4886b.png"},"5f68":function(t,e,a){t.exports=a.p+"static/media/2.3effb154.mp4"},6069:function(t,e,a){"use strict";a.d(e,"d",(function(){return o})),a.d(e,"c",(function(){return r})),a.d(e,"b",(function(){return l})),a.d(e,"a",(function(){return c}));a("2ca0"),a("d3b7"),a("99af"),a("ac1f"),a("5319");var i={pcToMobile:{"/pc/login":"/mobile/login","/pc/home":"/mobile/home","/pc/detail":"/mobile/detail","/pc/cultiver":"/mobile/home","/data/history":"/mobile/home","/data/report":"/mobile/home","/data/tousi":"/mobile/home","/device/manage":"/mobile/detail","/device/camera":"/mobile/detail","/user/add":"/mobile/home","/register":"/mobile/register"},mobileToPc:{"/mobile/login":"/pc/login","/mobile/home":"/pc/home","/mobile/detail":"/pc/detail","/mobile/register":"/pc/register","/mobile/list":"/pc/home","/mobile/device-detail":"/pc/detail","/mobile/section-title":"/pc/home"}},n={pc:"/pc/home",mobile:"/mobile/home"};function s(t,e){var a,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e){if(t.startsWith("/mobile/"))return null;a=i.pcToMobile[t],a||(a=n.mobile)}else{if(t.startsWith("/pc/")||t.startsWith("/data/")||t.startsWith("/device/")||t.startsWith("/user/"))return null;a=i.mobileToPc[t],a||(a=n.pc)}return{path:a,query:s}}function o(t,e){return e?t.startsWith("/pc/")||t.startsWith("/data/")||t.startsWith("/device/")||t.startsWith("/user/"):t.startsWith("/mobile/")}function r(t,e,a){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=s(e,a,i);return o?(console.log("设备切换: ".concat(e," -> ").concat(o.path)),t.replace({path:o.path,query:o.query}).catch((function(e){console.warn("路由跳转失败，尝试跳转到默认页面:",e);var i=a?n.mobile:n.pc;return t.replace({path:i,query:o.query})}))):Promise.resolve()}function l(t){return t?"/mobile/login":"/pc/login"}function c(t){return t?"/mobile/home":"/pc/home"}},6099:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"Device-List"},[i("van-nav-bar",{staticClass:"device-nav",attrs:{title:t.title,"left-arrow":""},on:{"click-left":t.onClickLeft}}),"devices"===t.activeTab?i("div",{staticClass:"devices-container"},[i("div",{staticClass:"devices-section"},["devices"===t.activeTab?i("el-tabs",{staticClass:"h-100 w-100 mobile-tabs",attrs:{type:"border-card"}},[i("el-tab-pane",{attrs:{label:"水质设备"}},[i("ul",{staticClass:"area-list"},t._l(t.shebeilist.water,(function(e){return i("li",{key:e.ID||t.index,staticClass:"area-item cursor-pointer",on:{click:function(a){return t.emitLinkShebei("water",e)}}},[i("div",{staticClass:"item-left"},[i("img",{attrs:{src:a("fbe7"),alt:""}})]),i("div",{staticClass:"item-middle"},[t._v(t._s(e.BACILITIES_NAME||"未知")+" -> "+t._s(e.EQUIPMENT_NAME||"未知设备")+" ")]),i("div",{staticClass:"item-right"},[t._v("运行良好")])])})),0)]),i("el-tab-pane",{attrs:{label:"气象设备"}},[i("ul",{staticClass:"area-list"},t._l(t.shebeilist.meteorological,(function(e){return i("li",{key:e.ID||t.index,staticClass:"area-item cursor-pointer",on:{click:function(a){return t.emitLinkShebei("meteorological",e)}}},[i("div",{staticClass:"item-left"},[i("img",{attrs:{src:a("fbe7"),alt:""}})]),i("div",{staticClass:"item-middle"},[t._v(t._s(e.BACILITIES_NAME||"未知")+" -> "+t._s(e.EQUIPMENT_NAME||"未知设备")+" ")]),i("div",{staticClass:"item-right"},[t._v("运行良好")])])})),0)]),i("el-tab-pane",{attrs:{label:"监控设备"}},[i("ul",{staticClass:"area-list"},t._l(t.shebeilist.monitoring,(function(e,n){return i("li",{key:e.ID||n,staticClass:"area-item cursor-pointer",on:{click:function(a){return t.emitLinkShebei("monitoring",e,n)}}},[i("div",{staticClass:"item-left"},[i("img",{attrs:{src:a("fbe7"),alt:""}})]),i("div",{staticClass:"item-middle"},[t._v(t._s(e.camera_name||"未命名摄像头"))]),i("div",{staticClass:"item-right",style:{color:1==e.state?"#43C8FF":"#F69E29"}},[t._v(" "+t._s(1==e.state?"运行良好":"运行异常")+" ")])])})),0)])],1):t._e()],1),i("div",{staticClass:"separator-section"},[i("div",{staticClass:"separator-left"},[i("div",{staticClass:"water-param"},[i("img",{staticClass:"param-icon",attrs:{src:a("9980"),alt:"氨氮"}}),i("div",{staticClass:"param-info"},[i("div",{staticClass:"param-name"},[t._v("氨氮(NH₃)")]),i("div",{staticClass:"param-value"},[t._v(t._s(t.waterParams.nh3.value)),i("span",{staticClass:"param-unit"},[t._v("mg/L")])])])])]),i("div",{staticClass:"separator-right"},[i("div",{staticClass:"water-param"},[i("img",{staticClass:"param-icon",attrs:{src:a("2a7e"),alt:"亚硝酸盐"}}),i("div",{staticClass:"param-info"},[i("div",{staticClass:"param-name"},[t._v("亚硝酸盐(NO₂⁻)")]),i("div",{staticClass:"param-value"},[t._v(t._s(t.waterParams.no2.value)),i("span",{staticClass:"param-unit"},[t._v("mg/L")])])])])])]),i("div",{staticClass:"alarm-section"},[i("section-title",{staticClass:"white-title",attrs:{title:"报警信息"}}),i("div",{staticClass:"w-100 h-100 position-relative alarm-list-container"},[i("div",{ref:"scrollContainer",staticClass:"scroll-container"},[i("div",{staticClass:"scroll-content",style:t.scrollStyle},[i("ul",{staticClass:"area-list"},[t._l(t.alertYuanList,(function(e,a){return i("li",{key:e.ID,class:["area-item flex-item cursor-pointer",{"odd-item":a%2===0}]},[i("div",{staticClass:"d-flex align-items-center justify-content-center area-item-img"},[i("img",{attrs:{src:t.getImagePath(e.type),alt:""}})]),i("div",{staticClass:"area-item-content"},[i("div",{staticClass:"flex-content information-title d-flex align-items-center"},[t._v(" "+t._s(e.message)+" ")]),i("div",{staticClass:"info-row"},[i("div",{staticClass:"info-left"},[i("span",[t._v("当前值为")]),i("span",{staticClass:"value-text",class:1==e.type?"error1":"error2"},[t._v(t._s(e.value))])]),i("div",{staticClass:"info-right"},[i("span",[t._v(t._s(e.time))])])])])])})),t.needExtraItem?i("li",{key:"extra-item",staticClass:"area-item flex-item"}):t._e()],2),t.shouldScroll?i("ul",{staticClass:"area-list"},[t._l(t.alertYuanList,(function(e,a){return i("li",{key:e.ID,class:["area-item flex-item cursor-pointer",{"odd-item":a%2===0}]},[i("div",{staticClass:"d-flex align-items-center justify-content-center area-item-img"},[i("img",{attrs:{src:t.getImagePath(e.type),alt:""}})]),i("div",{staticClass:"area-item-content"},[i("div",{staticClass:"flex-content information-title d-flex align-items-center"},[t._v(" "+t._s(e.message)+" ")]),i("div",{staticClass:"info-row"},[i("div",{staticClass:"info-left"},[i("span",[t._v("当前值为")]),i("span",{staticClass:"value-text",class:1==e.type?"error1":"error2"},[t._v(t._s(e.value))])]),i("div",{staticClass:"info-right"},[i("span",[t._v(t._s(e.time))])])])])])})),t.needExtraItem?i("li",{key:"extra-item",staticClass:"area-item flex-item"}):t._e()],2):t._e()])])])],1)]):t._e(),"logs"===t.activeTab?i("div",{staticClass:"breeding-logs"},[i("div",{staticClass:"log-header"},[i("h3",{staticClass:"log-title"},[t._v("日志记录")]),i("van-button",{staticClass:"add-log-btn",attrs:{type:"primary",size:"small",icon:"plus"},on:{click:t.showAddLogForm}},[t._v(" 添加日志 ")])],1),[i("LogList",{attrs:{baselist:t.loglist}})]],2):t._e(),i("el-dialog",{attrs:{visible:t.showAddLogDialog,title:"添加养殖日志",width:t.dialogWidth,"custom-class":"log-dialog"},on:{"update:visible":function(e){t.showAddLogDialog=e}}},[i("div",{staticClass:"h-100 w-100 log-form-container"},[i("el-form",{staticClass:"log-form",attrs:{model:t.addForm,"label-position":"top"}},[i("el-form-item",{attrs:{label:"时间"}},[i("el-date-picker",{staticClass:"form-control",attrs:{"value-format":"yyyy-MM-dd%20HH:mm",placeholder:"选择日期时间"},on:{change:t.check_form},model:{value:t.addForm.time,callback:function(e){t.$set(t.addForm,"time",e)},expression:"addForm.time"}})],1),i("el-form-item",{attrs:{label:"设施名称"}},[i("el-select",{staticClass:"form-control",attrs:{placeholder:"请选择设施"},on:{change:t.check_form},model:{value:t.addForm.sheshiid,callback:function(e){t.$set(t.addForm,"sheshiid",e)},expression:"addForm.sheshiid"}},t._l(t.facilitiesList,(function(t){return i("el-option",{key:t.value,attrs:{label:t.text,value:t.value}})})),1)],1),i("div",{staticClass:"form-row"},[i("el-form-item",{staticClass:"half-width",attrs:{label:"姓名"}},[i("el-input",{on:{change:t.check_form},model:{value:t.addForm.name,callback:function(e){t.$set(t.addForm,"name",e)},expression:"addForm.name"}})],1),i("el-form-item",{staticClass:"half-width",attrs:{label:"动作"}},[i("el-select",{attrs:{placeholder:"请选择动作"},on:{change:t.check_form},model:{value:t.addForm.action,callback:function(e){t.$set(t.addForm,"action",e)},expression:"addForm.action"}},t._l(t.actionOptions,(function(t){return i("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1)],1)],1),i("div",{staticClass:"form-row"},[i("el-form-item",{staticClass:"half-width",attrs:{label:"种类"}},[i("el-input",{on:{change:t.check_form},model:{value:t.addForm.zhonglei,callback:function(e){t.$set(t.addForm,"zhonglei",e)},expression:"addForm.zhonglei"}})],1),i("el-form-item",{staticClass:"half-width",attrs:{label:"数量"}},[i("el-input",{staticClass:"no-number-controls",on:{input:t.validateNum},model:{value:t.addForm.num,callback:function(e){t.$set(t.addForm,"num",e)},expression:"addForm.num"}}),t.errMsgNum?i("div",{staticClass:"num-error"},[t._v(t._s(t.errMsgNum))]):t._e()],1)],1),i("el-form-item",{attrs:{label:"备注"}},[i("el-input",{attrs:{type:"textarea",rows:2},on:{change:t.check_form},model:{value:t.addForm.remark,callback:function(e){t.$set(t.addForm,"remark",e)},expression:"addForm.remark"}})],1),t.errMsg?i("div",{staticClass:"form-error"},[t._v(t._s(t.errMsg))]):t._e(),i("el-form-item",[i("el-button",{staticClass:"submit-btn",attrs:{type:"primary",loading:t.addLoading,disabled:t.isdisabled},on:{click:t.addLog}},[t._v("提交")])],1)],1)],1)]),i("van-tabbar",{staticClass:"bottom-nav",model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[i("van-tabbar-item",{staticClass:"nav-item",attrs:{name:"devices",icon:"apps-o"}},[t._v("设备列表")]),i("van-tabbar-item",{staticClass:"nav-item",attrs:{name:"logs",icon:"notes-o"}},[t._v("养殖日志")])],1)],1)},n=[],s=a("53ca"),o=a("5530"),r=(a("d81d"),a("99af"),a("d3b7"),a("159b"),a("e9c4"),a("4d90"),a("b0c0"),a("caad"),a("ac1f"),a("00b4"),a("fd03")),l=a("2f62"),c=a("4105"),d=a("a5ca"),u={components:{LogList:c["a"],SectionTitle:d["a"]},data:function(){return{title:"设备列表",activeTab:"devices",shebeilist:{water:[],meteorological:[],monitoring:[]},loglist:[],showAddLogDialog:!1,showDatePicker:!1,showActionPicker:!1,showFacilityPicker:!1,addLoading:!1,addForm:{time:"",name:"",action:"",num:"",remark:"",zhonglei:"",sheshiid:"",base_id:""},actionOptions:[{name:"投饲料",value:"投饲料"},{name:"投药物",value:"投药物"},{name:"捕捞水产品",value:"捕捞水产品"},{name:"投放水产品",value:"投放水产品"}],addFromName:{sheshiid:"设施号",time:"时间",name:"操作人姓名",action:"动作",zhonglei:"种类",num:"数量",remark:"备注"},errMsg:"",errMsgNum:"",isdisabled:!0,dialogWidth:"90%",shelist:[],rizhishuru:0,alertYuanList:[],scrollOffset:0,scrollInterval:null,contentHeight:0,containerHeight:0,isPaused:!1,waterParams:{nh3:{value:.25,unit:"mg/L"},no2:{value:.15,unit:"mg/L"}}}},computed:Object(o["a"])(Object(o["a"])({},Object(l["d"])({base_id:function(t){return t.base_id}})),{},{showShebeilist:function(){var t=this.shebeilist;return t.water&&t.water.length||t.meteorological&&t.meteorological.length||t.monitoring&&t.monitoring.length?t:[]},facilitiesList:function(){return this.shelist.map((function(t){return{text:t.BACILITIES_NAME||"未知",value:t.ID||""}}))},needExtraItem:function(){return this.alertYuanList.length%2!==0&&this.alertYuanList.length>3},shouldScroll:function(){return this.contentHeight>this.containerHeight},scrollStyle:function(){return this.shouldScroll?{transform:"translateY(".concat(-this.scrollOffset,"px)")}:{}}}),watch:{activeTab:function(t){this.title="devices"===t?"设备列表":"养殖日志","logs"===t&&this.getLogList()},alertYuanList:{handler:function(){this.resetScroll()},deep:!0}},methods:Object(o["a"])(Object(o["a"])(Object(o["a"])({},Object(l["b"])("device",["updateDeviceInfo","updateBaseId"])),Object(l["b"])(["updateBaseId"])),{},{onClickLeft:function(){this.$router.back()},addAlarm:function(t,e,a,i){Date.now(),Math.floor(1e3*Math.random());this.alertYuanList.push({message:t,value:e,type:a,time:i}),console.log("已添加报警: ".concat(t,", 值: ").concat(e,", 类型: ").concat(a))},getlist:function(){var t=this;Object(r["z"])({base_id:this.base_id}).then((function(e){if(console.log("数据接口:",e),t.shebeilist.water=[],t.shebeilist.meteorological=[],t.alertYuanList=[],e.data.alertlist=[],e&&e.data&&e.data.shebeilist&&Array.isArray(e.data.shebeilist)&&(e.data.shebeilist.forEach((function(e){try{e.UANDL&&"string"===typeof e.UANDL&&(e.UANDL=JSON.parse(e.UANDL)),"水质检测"==e.EQUIPMENT_TYPE?t.shebeilist.water.push(e):"气象检测"==e.EQUIPMENT_TYPE&&t.shebeilist.meteorological.push(e)}catch(a){console.error("解析设备数据出错:",a,e)}})),e.data.alertlist&&Array.isArray(e.data.alertlist)&&(t.alertYuanList=e.data.alertlist),0===t.alertYuanList.length))for(var a=0;a<e.data.shebeilist.length;a++){var i=e.data.shebeilist[a],n=i.Online_Voltage||0;if("水质检测"===i.EQUIPMENT_TYPE){var o=i.O2,r=i.TEM,l=i.PH,c=i.UANDL,d=i.ACQUISITION_TIME,u=c;if("string"===typeof c)try{u=JSON.parse(c)}catch(C){console.error("解析 UANDL 失败:",C);continue}else if("object"!==Object(s["a"])(c)||null===c)return void console.error("UANDL 不是有效的对象或 JSON 字符串");if(o<u.o2.th[0]||o>u.o2.th[1]){var m="".concat(i.EQUIPMENT_NAME,"的溶氧值异常"),f=o;t.addAlarm(m,f,1,d)}if(r<u.te.th[0]||r>u.te.th[1]){var h="".concat(i.EQUIPMENT_NAME,"的温度异常"),p=r;t.addAlarm(h,p,1,d)}if(l<u.ph.th[0]||l>u.ph.th[1]){var g="".concat(i.EQUIPMENT_NAME,"的PH值异常"),A=l;t.addAlarm(g,A,1,d)}if(0==n||n<10){var b="".concat(i.EQUIPMENT_NAME,"的电压值异常"),v=n;t.addAlarm(b,v,2,d)}}}t.shelist=e.data.sheshilist||[],console.log("报警信息列表:",JSON.stringify(t.alertYuanList)),console.log("设施名称:",t.shelist),t.$nextTick((function(){t.resetScroll()}))})).catch((function(e){console.error("获取设备列表失败:",e),0===t.alertYuanList.length&&(t.alertYuanList=[{ID:1,message:"测试设备1的溶氧值异常",value:"3.2",type:1,time:t.getCurrentTime()},{ID:2,message:"测试设备2的电压值异常",value:"8.5",type:2,time:t.getCurrentTime()}],t.resetScroll())}))},getCurrentTime:function(){var t=new Date,e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0"),n=String(t.getHours()).padStart(2,"0"),s=String(t.getMinutes()).padStart(2,"0"),o=String(t.getSeconds()).padStart(2,"0");return"".concat(e,"-").concat(a,"-").concat(i," ").concat(n,":").concat(s,":").concat(o)},GetCamera:function(){var t=this;Object(r["t"])({base_id:this.base_id}).then((function(e){t.shebeilist.monitoring=[],e&&e.item_list&&Array.isArray(e.item_list)&&(t.shebeilist.monitoring=e.item_list)})).catch((function(t){console.error("获取监控设备失败:",t)}))},emitLinkShebei:function(t,e,a){if(e)try{this.updateDeviceInfo({id:e.ID||e.id||"",name:"monitoring"===t?e.camera_name||"未命名摄像头":e.EQUIPMENT_NAME||"未命名设备",type:t,itemData:e});var i="monitoring"===t?"/mobile/section-title":"/mobile/device-detail";this.$router.push(i)}catch(n){console.error("设备跳转出错:",n)}else console.warn("没有设备数据，无法跳转")},getLogList:function(){var t=this;Object(r["n"])({base_id:this.base_id}).then((function(e){e&&e.data&&e.data.loglist&&(t.loglist=e.data.loglist)})).catch((function(t){console.error("获取日志列表失败:",t)}))},showAddLogForm:function(){this.showAddLogDialog=!0,this.addForm={time:"",name:"",action:"",num:"",remark:"",zhonglei:"",sheshiid:"",base_id:this.base_id},this.isdisabled=!0,this.errMsg=""},addLog:function(){var t=this;this.addLoading=!1,""!=this.addForm.sheshiid?""!=this.addForm.time?""!=this.addForm.name?""!=this.addForm.action?""!=this.addForm.zhonglei?""!=this.addForm.num?(this.addLoading=!0,this.addForm.base_id=this.$route.query.base_id||this.base_id,console.log("发送到后端的数据:",JSON.stringify(this.addForm)),Object(r["k"])(this.addForm).then((function(e){t.addLoading=!1,t.loglist=e.data.loglist,alert("提交成功"),t.showAddLogDialog=!1})).catch((function(e){t.addLoading=!1,alert("提交失败: "+e),console.error("添加日志失败:",e)}))):this.$message.warning("请填写数量"):this.$message.warning("请填写种类"):this.$message.warning("请选择动作"):this.$message.warning("请填写操作人姓名"):this.$message.warning("请选择时间"):this.$message.warning("请选择设施")},check_form:function(){this.errMsg="";var t=["sheshiid","time","name","action","zhonglei","num"];for(var e in this.addFromName)if(t.includes(e)&&!this.addForm[e])return this.isdisabled=!0,void(this.errMsg=this.addFromName[e]+"不能为空");this.isdisabled=!1,this.errMsg=""},validateNum:function(t){return this.errMsgNum="",t&&!/^\d+$/.test(t)?(this.errMsgNum="数量必须为数字",this.addForm.num="",this.isdisabled=!0,!1):(this.check_form(),!0)},getImagePath:function(t){return a(1==t?"2935":"f2f5")},resetScroll:function(){var t=this;this.stopScrolling(),this.$nextTick((function(){var e=t.$refs.scrollContainer;if(e){var a;t.containerHeight=e.clientHeight;var i=(null===(a=e.querySelector(".area-item"))||void 0===a?void 0:a.offsetHeight)||50;t.contentHeight=i*(t.alertYuanList.length+(t.needExtraItem?1:0)),t.scrollOffset=0,t.shouldScroll&&t.startScrolling()}}))},startScrolling:function(){var t=this;if(this.shouldScroll&&!this.isPaused){var e=.3,a=performance.now(),i=function i(n){if(t.shouldScroll&&!t.isPaused){var s=n-a;a=n,t.scrollOffset+=e*(s/16.67),t.scrollOffset>=t.contentHeight&&(t.scrollOffset=0),t.scrollInterval=requestAnimationFrame(i)}else t.stopScrolling()};this.scrollInterval=requestAnimationFrame(i)}},stopScrolling:function(){this.scrollInterval&&(cancelAnimationFrame(this.scrollInterval),this.scrollInterval=null)},handleResize:function(){var t=this;this.$nextTick((function(){t.resetScroll()}))},toggleScroll:function(){this.isPaused=!this.isPaused,this.isPaused||this.startScrolling()}}),created:function(){this.base_id||this.updateBaseId("1")},mounted:function(){this.resetScroll();try{this.$route.query.base_id&&this.updateBaseId(this.$route.query.base_id),console.log("当前base_id:",this.base_id,"路由参数base_id:",this.$route.query.base_id),this.GetCamera(),this.getlist()}catch(t){console.error("初始化DeviceList出错:",t)}window.addEventListener("resize",this.handleResize),this.handleResize()},beforeDestroy:function(){this.stopScrolling(),window.removeEventListener("resize",this.handleResize)}},m=u,f=(a("2b7b"),a("2877")),h=Object(f["a"])(m,i,n,!1,null,"7eb241e2",null);e["default"]=h.exports},"609b":function(t,e,a){t.exports=a.p+"static/img/map-icon4.5771efc7.png"},"64ae":function(t,e,a){},"66f6":function(t,e,a){t.exports=a.p+"static/img/fengsu.cbe8c3ae.png"},6993:function(t,e,a){},"6a0b":function(t,e){t.exports="data:image/png;base64,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************************************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"},"6d04":function(t,e,a){},"6f8c":function(t,e,a){"use strict";a("59c1")},"788e":function(t,e,a){t.exports=a.p+"static/img/fengxiang.552c1fc4.png"},"7b47":function(t,e){t.exports="data:image/png;base64,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"},"7fdc":function(t,e,a){},"84e1":function(t,e,a){t.exports=a.p+"static/img/guangzhao .ba750914.png"},8810:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAbCAYAAACN1PRVAAAAAXNSR0IArs4c6QAABNdJREFUSEulln9M1HUYx9/P9+BA7pSfF8dCQag0xVB04kg3pjCKkQM3cIKIUvoH9sst1g9bc7PmMrW11E0WYZTgENJGaJYIIpraKBByuRgSyMnPEI5fB3ffpz7fu8ODgYB8/vre5/nx+jzP53mezxFmsZrbR15iomMM+dMAnTprKlc0lcJk8vpWU4iKpFq7XJZ5dbCf+rfH+XsimMHAbgM0XA+Qn4PzAbPTyLOLdRrDZMDHwv5+YFpitqjbnvenLkcHf903XQMQMYHT6sX+LitmDKtrGA6FSq4G0BIS4Opvd1DXaPoQxPsnc8hMOcsCXdInkk8aWc29wXYAOsWI+XhokNvumsb+MLBUNdU9E+SEFxZqzo3XmxBWVT9QBGCTozKB9zMoGUDwVDAhJ4kCw4Lm/DPWxzjLW3f7dzKglDERvQWZlzNhx3QAjjoM3FqzSBM+KexiDWu0zv19Cgi4ELFEGyu+r9/pa2Bg4cyBtHPtEs1XdrsxaSyvNV4FsBaASW3UekZE0KBQLK8z3gXjuZnChL4ZpI9apm2zBWB1canauBng09Z64JToFe554ru0uvcIA3ueBGS14dNRy923jIH9VNXzLwBPBopfXum+UQgv1jxcyGZqeHKQ1VImaWls2Nw7Shp/vPlwL4CPlbDJsiB+tXezsn+j+1sQbZ0tDMDNuHCPNZRTds/Vw8VDuRswTia86KlUXtG17gCJ0DgViMHvA9ROQAaAlQDuse3gBHwBQCt8qGQ5hAorO9OY6SSAER4iXVK0V48Qnqno+gyEd2wwMxiXARoCsRhdCwBsELLEdd5Kds5c7foeQML//Xk7cZ13qG2vFYCvLZBDlFfeeRZAPMAFyZG6zfZI8so7fwdgn3O9yZE+7o5R5l/pSmfmbMgUkrze+8+8ss6TICQwcCMl0ieGmSn/Smc7QD5WO6qm3NIO0VcaZtqeFuXzjdgWqVXJWjGu5toAxm0bdPNOlXYHWGBuZJB/2gafltzSDjPJiEqN1pWPT3fB9eY5Q4OuTQBsMPTT1z+39wNwY6atr8boTgmjE8UGN2cXJ9EbSr5BMI4MmfVqrUmmYe3S7TG6qpxfOiKZuUwmhL0W/dQf2Rc6FsFZ9pKY+3ZE62sLCljd59HR4gAboKzzbaUA1gP85a5Y/Zv2E2aVtDWARqeGcQi9On3fMPdovTMYtApAitB1khGcHufbkFXSVgHCOgBNu2J9A4Qs63xbhwOskY4WP8gk0EGAunWDnfqkpKXDQvFocesPBCj9BkbP7o16D/F5rLiVHVOWEecrEREfL36Qy6BUgCt3v+InoEL3UYEA5+hIQbOXSu2kPI7MuPx2gp9SZZ+fNcRLRKJ4xBoEOIWY3JmQM+5+XodE9yGLFkA4AfUW5kyVBEm8bQDmKTfBlKyU7eHClnQQZVuDwCUmaU/mJn3d4SLDd/Z0jS+AGf0m9Bpv+3mODuKDhS0HmPGeg5McIrgwQ7xhs1wc9W6if+mYqX8gvzmBSToOsH6W3h+ZM2V+sOXpQ9aiHrf2nTC4SRpLPBOtJeAZgAcYZCDwNtGPMzzEGx+lzD9qt5n2X7m9+S3zVWaLuNfoaQArJMh79qUGiik0uqYNs1vszW1aRbIcz6AIEILA8ALwkJgbWKJfAZR8khZQOdGB/gMCSNOvIriFrQAAAABJRU5ErkJggg=="},"8e6c":function(t,e,a){},9098:function(t,e,a){},"941b":function(t,e,a){"use strict";a("2e81")},9480:function(t,e,a){"use strict";a("e0d0")},"95b0":function(t,e,a){t.exports=a.p+"static/img/top-info-xy.5b36cac3.png"},9980:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAbCAYAAACN1PRVAAAAAXNSR0IArs4c6QAABNdJREFUSEulln9M1HUYx9/P9+BA7pSfF8dCQag0xVB04kg3pjCKkQM3cIKIUvoH9sst1g9bc7PmMrW11E0WYZTgENJGaJYIIpraKBByuRgSyMnPEI5fB3ffpz7fu8ODgYB8/vre5/nx+jzP53mezxFmsZrbR15iomMM+dMAnTprKlc0lcJk8vpWU4iKpFq7XJZ5dbCf+rfH+XsimMHAbgM0XA+Qn4PzAbPTyLOLdRrDZMDHwv5+YFpitqjbnvenLkcHf903XQMQMYHT6sX+LitmDKtrGA6FSq4G0BIS4Opvd1DXaPoQxPsnc8hMOcsCXdInkk8aWc29wXYAOsWI+XhokNvumsb+MLBUNdU9E+SEFxZqzo3XmxBWVT9QBGCTozKB9zMoGUDwVDAhJ4kCw4Lm/DPWxzjLW3f7dzKglDERvQWZlzNhx3QAjjoM3FqzSBM+KexiDWu0zv19Cgi4ELFEGyu+r9/pa2Bg4cyBtHPtEs1XdrsxaSyvNV4FsBaASW3UekZE0KBQLK8z3gXjuZnChL4ZpI9apm2zBWB1canauBng09Z64JToFe554ru0uvcIA3ueBGS14dNRy923jIH9VNXzLwBPBopfXum+UQgv1jxcyGZqeHKQ1VImaWls2Nw7Shp/vPlwL4CPlbDJsiB+tXezsn+j+1sQbZ0tDMDNuHCPNZRTds/Vw8VDuRswTia86KlUXtG17gCJ0DgViMHvA9ROQAaAlQDuse3gBHwBQCt8qGQ5hAorO9OY6SSAER4iXVK0V48Qnqno+gyEd2wwMxiXARoCsRhdCwBsELLEdd5Kds5c7foeQML//Xk7cZ13qG2vFYCvLZBDlFfeeRZAPMAFyZG6zfZI8so7fwdgn3O9yZE+7o5R5l/pSmfmbMgUkrze+8+8ss6TICQwcCMl0ieGmSn/Smc7QD5WO6qm3NIO0VcaZtqeFuXzjdgWqVXJWjGu5toAxm0bdPNOlXYHWGBuZJB/2gafltzSDjPJiEqN1pWPT3fB9eY5Q4OuTQBsMPTT1z+39wNwY6atr8boTgmjE8UGN2cXJ9EbSr5BMI4MmfVqrUmmYe3S7TG6qpxfOiKZuUwmhL0W/dQf2Rc6FsFZ9pKY+3ZE62sLCljd59HR4gAboKzzbaUA1gP85a5Y/Zv2E2aVtDWARqeGcQi9On3fMPdovTMYtApAitB1khGcHufbkFXSVgHCOgBNu2J9A4Qs63xbhwOskY4WP8gk0EGAunWDnfqkpKXDQvFocesPBCj9BkbP7o16D/F5rLiVHVOWEecrEREfL36Qy6BUgCt3v+InoEL3UYEA5+hIQbOXSu2kPI7MuPx2gp9SZZ+fNcRLRKJ4xBoEOIWY3JmQM+5+XodE9yGLFkA4AfUW5kyVBEm8bQDmKTfBlKyU7eHClnQQZVuDwCUmaU/mJn3d4SLDd/Z0jS+AGf0m9Bpv+3mODuKDhS0HmPGeg5McIrgwQ7xhs1wc9W6if+mYqX8gvzmBSToOsH6W3h+ZM2V+sOXpQ9aiHrf2nTC4SRpLPBOtJeAZgAcYZCDwNtGPMzzEGx+lzD9qt5n2X7m9+S3zVWaLuNfoaQArJMh79qUGiik0uqYNs1vszW1aRbIcz6AIEILA8ALwkJgbWKJfAZR8khZQOdGB/gMCSNOvIriFrQAAAABJRU5ErkJggg=="},a18c:function(t,e,a){"use strict";var i=a("1da1"),n=(a("96cf"),a("d3b7"),a("3ca3"),a("ddb0"),a("99af"),a("2b0e")),s=a("8c4f"),o=a("17e8"),r=a("6069"),l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"login-page w-100 h-100 position-relative "},[a("div",{staticClass:"login-box position-absolute p-5"},[a("h2",{staticClass:"mb-5 color-black"},[t._v("Welcome")]),a("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules}},[a("el-form-item",{attrs:{prop:"username"}},[a("el-input",{attrs:{placeholder:"请输入账号"},model:{value:t.form.username,callback:function(e){t.$set(t.form,"username",e)},expression:"form.username"}},[a("svg",{attrs:{slot:"prefix",t:"*************",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"2537",width:"1.3rem",height:"1.3rem"},slot:"prefix"},[a("path",{attrs:{d:"M954.181818 953.746618 512 953.746618 69.818182 953.746618 69.818182 932.126255C78.405818 911.646255 95.604364 892.422982 121.018182 874.945164 146.455273 857.444073 179.618909 841.897891 219.601455 828.772073 286.580364 806.802618 354.141091 798.075345 373.224727 795.934255 395.008 793.490618 414.510545 781.761164 426.612364 763.934255L434.385455 752.297891 437.341091 739.474618C445.789091 702.680436 425.239273 666.002618 412.229818 647.430982 408.040727 641.449891 402.990545 636.120436 397.288727 631.605527 364.776727 605.958982 336.546909 568.280436 315.624727 522.665891 304.872727 499.206982 283.205818 483.125527 258.490182 479.797527 254.557091 478.470982 241.245091 466.718255 235.403636 442.468073 228.794182 415.076073 235.938909 397.412073 238.545455 394.8288 261.213091 381.074618 275.013818 356.056436 274.548364 329.502255 273.128727 245.976436 293.050182 181.301527 333.730909 137.246255 385.186909 81.508073 459.077818 69.825164 512 69.825164 564.922182 69.825164 638.813091 81.508073 690.292364 137.246255 730.973091 181.301527 750.871273 245.976436 749.451636 329.502255 748.986182 356.056436 762.786909 381.074618 784.849455 394.409891 788.061091 397.412073 795.205818 415.076073 788.596364 442.491345 782.754909 466.718255 769.442909 478.470982 766.301091 479.634618 740.770909 483.125527 719.127273 499.206982 708.398545 522.642618 687.453091 568.280436 659.223273 605.958982 626.757818 631.582255 621.009455 636.120436 615.959273 641.449891 611.793455 647.430982 598.760727 666.002618 578.210909 702.680436 586.705455 739.614255L589.986909 753.019345 597.294545 763.771345C609.489455 781.761164 628.992 793.490618 650.752 795.934255 669.858909 798.075345 737.419636 806.802618 804.398545 828.772073 844.381091 841.897891 877.544727 857.444073 902.981818 874.945164 928.628364 892.585891 945.92 911.995345 954.181818 931.986618L954.181818 953.746618ZM1021.975273 913.973527 1021.882182 913.7408C1009.058909 877.225891 982.365091 844.830255 942.568727 817.414982 911.639273 796.143709 872.471273 777.641891 826.181818 762.4448 754.269091 738.846255 683.589818 729.374255 658.571636 726.558255 657.175273 726.395345 655.848727 725.720436 655.057455 724.580073 654.894545 724.324073 654.778182 724.068073 654.708364 723.835345 652.962909 716.225164 660.596364 699.4688 668.974545 687.483345 669.253818 687.064436 669.602909 686.715345 669.998545 686.4128 711.214545 653.854255 746.286545 607.448436 771.863273 551.7568 772.584727 550.174255 774.050909 549.033891 775.773091 548.801164 811.613091 543.890618 844.730182 507.561891 856.482909 458.8288 869.352727 405.441164 854.760727 355.241891 821.666909 335.157527 820.130909 334.203345 819.223273 332.504436 819.246545 330.689164 820.992 228.591709 794.88 147.579345 741.585455 89.886255 672.977455 15.599709 578.862545 0.006982 512 0.006982 445.137455 0.006982 351.022545 15.599709 282.414545 89.886255 229.12 147.579345 203.008 228.591709 204.753455 330.689164 204.776727 332.504436 203.869091 334.203345 202.333091 335.157527 169.239273 355.241891 154.647273 405.441164 167.517091 458.8288 179.269818 507.561891 212.386909 543.890618 248.250182 548.801164 249.949091 549.033891 251.415273 550.174255 252.136727 551.7568 277.713455 607.448436 312.785455 653.854255 354.001455 686.4128 354.397091 686.715345 354.746182 687.064436 355.025455 687.483345 363.403636 699.4688 371.037091 716.225164 369.291636 723.835345 369.221818 724.068073 369.105455 724.324073 368.942545 724.580073 368.151273 725.720436 366.824727 726.395345 365.451636 726.558255 340.410182 729.374255 269.730909 738.846255 197.818182 762.4448 151.528727 777.641891 112.360727 796.143709 81.454545 817.414982 41.634909 844.830255 14.941091 877.225891 2.117818 913.7408L2.024727 913.973527C0.674909 918.022982 0 922.142255 0 926.261527L0 980.370618C0 1004.225164 19.339636 1023.5648 43.194182 1023.5648L512 1023.5648 980.805818 1023.5648C1004.660364 1023.5648 1024 1004.225164 1024 980.370618L1024 926.261527C1024 922.142255 1023.325091 918.022982 1021.975273 913.973527L1021.975273 913.973527Z","p-id":"2538",fill:"#cdcdcd"}})])])],1),a("el-form-item",{attrs:{prop:"password"}},[a("el-input",{attrs:{placeholder:"请输入密码",type:"password"},nativeOn:{keydown:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.login.apply(null,arguments)}},model:{value:t.form.password,callback:function(e){t.$set(t.form,"password",e)},expression:"form.password"}},[a("svg",{attrs:{slot:"prefix",t:"1634354595512",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"3461",width:"1.5rem",height:"1.5rem"},slot:"prefix"},[a("path",{attrs:{d:"M511.970836 499.271094c-67.470671 0-124.724653 53.072753-130.051985 119.469975-0.358157 1.096984-0.775666 2.648316-0.775666 4.466732l0 31.441083c0 7.708566 6.224772 13.96199 13.876032 13.96199l31.203676 0c7.65126 0 13.931291-6.252401 13.931291-13.96199l-0.116657-9.052167c-0.535189-2.594081-0.35918-5.598508-0.11768-8.724709 0.11768-1.935072 0.234337-3.840468 0.234337-5.808286 0-39.924291 32.185027-72.467475 71.816652-72.467475 39.627533 0 71.870888 32.544207 71.870888 72.467475 0 31.771611-20.661575 59.697637-50.166563 69.136613l0-45.552475c0-7.68503-6.251378-13.96506-13.902638-13.96506l-31.174 0c-7.65433 0-13.906731 6.281053-13.906731 13.96506l0 94.259804c0 7.68503 6.252401 13.96506 13.906731 13.96506l31.174 0c1.279133 0 2.52859-0.208754 3.839445-0.596588 66.899666-5.985318 119.213126-63.475683 119.241778-131.214461C642.853745 558.38647 584.141552 499.271094 511.970836 499.271094","p-id":"3462",fill:"#cdcdcd"}}),a("path",{attrs:{d:"M806.553061 336.037383l-76.131935-0.090051 0-55.245232c0-104.908328-83.48439-190.268438-186.147584-190.268438L508.27977 90.433662c-101.113909 0-184.302563 83.84357-185.969529 187.18624l-0.177032 1.533936c0 1.057075 0.118704 1.696642 0 1.548262l0.357134 6.104021 0.952698 0c3.393283 12.400425 14.736633 21.497617 27.868721 21.497617 13.219069 0 24.473391-9.097192 27.836999-21.497617l0.983397 0 0.23843-5.819542c3.333932-74.213235 59.962673-132.331911 128.97956-132.331911l33.853016 0c71.311139 0 129.308041 62.390979 129.308041 139.030473l0 48.352241L375.188566 336.037383l0.089028-0.090051-157.800979 0.090051c-48.680722 0-88.307232 40.46255-88.307232 90.124624l0 417.338037c0 49.665143 39.627533 90.066295 88.307232 90.066295L806.553061 933.566338c48.708352 0 88.277556-40.400129 88.277556-90.066295L894.830617 426.162006C894.829594 376.499933 855.260389 336.037383 806.553061 336.037383M839.364351 430.584736l0 408.480298c0 21.019733-16.645099 38.081317-37.128619 38.081317L221.764268 877.146351c-20.484543 0-37.157271-17.061584-37.157271-38.081317L184.606996 430.584736c0-20.976754 16.672728-38.081317 37.157271-38.081317l580.471464 0C822.719252 392.503419 839.364351 409.609005 839.364351 430.584736","p-id":"3463",fill:"#cdcdcd"}})])])],1),a("el-form-item",{staticClass:"form-button mb-3"},[a("el-button",{attrs:{round:""},on:{click:t.login}},[t._v("登 录")])],1),a("div",{staticClass:"text-right"},[a("el-button",{attrs:{type:"text"},on:{click:t.onRegister}},[t._v("暂无账号，去注册")])],1)],1)],1)])},c=[],d=a("fd03"),u={name:"Login",data:function(){return{form:{username:"",password:""},rules:{username:[{required:!0,message:"账号不能为空",blur:!0}],password:[{required:!0,message:"密码不能为空",blur:!0}]}}},methods:{login:function(){var t=this;this.$refs.form.validate((function(e){e?Object(d["x"])({username:t.form.username,password:t.form.password}).then((function(e){200==e.code?(t.$store.commit("set_token",e.data.token),localStorage.setItem("token",e.data.token),t.$router.push({path:"/home"})):t.$message.error("登录失败，账号或者密码错误")})).catch((function(e){t.$message.error("登录失败，请稍后再试")})):t.$message.error("请填写完整的登录信息")}))},onRegister:function(){this.$router.push("/register")}}},m=u,f=(a("d8e3"),a("2877")),h=Object(f["a"])(m,l,c,!1,null,"00998469",null),p=h.exports,g=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100 d-flex flex-column  "},[a("div",{staticClass:"w-100 h-100 map-container position-absolute ",attrs:{id:"map-container"}}),a("img",{staticClass:"w-100 h-100 position-absolute top-0 left-0",staticStyle:{"pointer-events":"none"},attrs:{src:t.bgImage,alt:""}}),1===t.box?a("div",{staticClass:"equip-box"},[a("img",{staticClass:"adaptive-bg",attrs:{src:t.promptImage,alt:""}}),a("div",{staticClass:"text adaptive-text",staticStyle:{"white-space":"pre-wrap"}},[t._v(t._s(t.miaoshu))]),a("div",{staticClass:"close-icon d-flex align-items-center justify-content-center",on:{click:t.onCloseCover}},[a("svg",{staticClass:"icon",attrs:{t:"1639825855503",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"26567",width:"1rem",height:"1rem"}},[a("path",{attrs:{d:"M846.005097 957.24155c-28.587082 0-57.174164-10.911514-78.996169-32.733519L96.632851 254.131955c-43.644009-43.644009-43.**********.348328 0-157.992337s114.348328-43.**********.992337 0L925.001265 766.515694c43.644009 43.644009 43.**********.348328 0 157.992337C903.17926 946.330036 874.592179 957.24155 846.005097 957.24155z","p-id":"26568",fill:"#ffffff"}}),a("path",{attrs:{d:"M175.62902 957.24155c-28.587082 0-57.174164-10.911514-78.996169-32.733519-43.644009-43.644009-43.**********.348328 0-157.992337L767.008928 96.139617c43.644009-43.**********.348328-43.**********.992337 0s43.**********.348328 0 157.992337L254.**********.508032C232.**********.**********.**********.24155 175.62902 957.24155z","p-id":"26569",fill:"#ffffff"}})])])]):t._e(),a("div",{staticClass:"header"},[a("Head2")],1),a("div",{staticClass:"main d-flex flex-row  "},[a("div",{staticClass:"home-sidebar  "},[a("div",[a("SubtitledSection",{attrs:{title:"企业介绍"}},[[a("CompanyIntroduction")]],2)],1),a("div",[a("SubtitledSection",{attrs:{title:"基地列表"}},[[a("BaseList",{attrs:{baselist:t.baselist}})]],2)],1),a("div",[a("SubtitledSection",{attrs:{title:"设备描述"}},[[a("DeviceDescription")]],2)],1)]),a("div",{staticClass:"home-center"},[a("div",{staticClass:"info-box"},t._l(t.wetherinfo1,(function(e,i){return a("div",{key:i,staticClass:"info-item",style:{backgroundImage:"url("+e.img+")"}},[a("div",{staticStyle:{"margin-left":"3.4rem"}},[t._v(" "+t._s(e.value)+" ")])])})),0),a("div",{staticClass:"bottom-box "},[a("div",{staticStyle:{width:"49.5%"}},[a("SubtitledSectionLong",{attrs:{title:"设备监控"}},[[a("div",{staticClass:"video-wrapper"},[a("div",{staticClass:"video-content",attrs:{id:"video-container"}},[a("img",{attrs:{src:t.jiankong2Image,alt:"视频占位图"}})])])]],2)],1),a("div",{staticStyle:{width:"49.5%","margin-left":"0.6rem"}},[a("SubtitledSectionLong",{attrs:{title:"设备监控"}},[[a("div",{staticClass:"video-wrapper"},[a("div",{staticClass:"video-content",attrs:{id:"video-container"}},[a("img",{attrs:{src:t.jiankong1Image,alt:"视频占位图"}})])])]],2)],1)])]),a("div",{staticClass:"home-sidebar "},[a("div",[a("SubtitledSection",{staticStyle:{"pointer-events":"auto"},attrs:{title:"养殖种类"}},[[a("BreedingTypes",{attrs:{Data:t.optionsData}})]],2)],1),a("div",[a("SubtitledSection",{attrs:{title:"基地规模"}},[[a("BaseSize",{attrs:{volumeDetailData:t.volumeDetailData}})]],2)],1),a("div",[a("SubtitledSection",{attrs:{title:"水质参数"}},[[a("WaterQualityModule",{attrs:{alertlist:t.alertlist}})]],2)],1)])])])},A=[],b=a("3835"),v=(a("159b"),a("b64b"),a("4e82"),a("d81d"),a("4fad"),a("33d1"),a("ea98"),a("ac1f"),a("1276"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100 d-flex flex-column "},[a("div",{staticClass:"title"},[a("span",[t._v(" "+t._s(t.title)+" ")])]),a("div",{staticClass:"title-main"},[t._t("default")],2)])}),C=[],w={props:{title:String}},x=w,y=(a("101a"),Object(f["a"])(x,v,C,!1,null,"02516979",null)),E=y.exports,I=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100  position-relative"},[a("div",{staticClass:"title2"},[a("span",[t._v(" "+t._s(t.title)+" ")])]),a("div",{staticClass:"title2-main"},[t._t("default")],2)])},D=[],S={props:{title:String}},_=S,k=(a("eca2"),Object(f["a"])(_,I,D,!1,null,"9576f336",null)),N=k.exports,B=function(){var t=this,e=t.$createElement;t._self._c;return t._m(0)},M=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100 text"},[a("div",{staticClass:"scrolling-text"},[a("p",[t._v("上海海洋大学（Shanghai Ocean University）是上海市人民政府与国家海洋局、农业农村部共建高校，由上海市人民政府主管，入选国家“双一流”建设高校、卓越农林人才教育培养计划、亚洲大学生集体行动交流计划、上海市首批课程思政教育教学改革整体试点校、上海首批深化创新创业教育改革示范高校，为上海市水产学会、上海市食品学会、上海市渔业经济研究会指导单位，是一所多科性应用研究型大学")]),a("p",[t._v("学校前身是张謇、黄炎培创建于1912年的江苏省立水产学校，历经国立中央大学农学院水产学校、国立四川水产职业学校、上海市立吴淞水产专科学校、上海水产专科学校等办学时期，1952年升格为中国第一所本科水产高校——上海水产学院，1983年获硕士研究生招生权，1985年更名为上海水产大学，1998年获博士学位授予权；2008年更名为上海海洋大学，江泽民同志题写校名。2017年，入选国家“世界一流学科建设高校”。2022年，入选第二轮“双一流”建设高校及建设学科名单。")])])])}],T={},L=T,O=(a("b7a1"),Object(f["a"])(L,B,M,!1,null,"ab8ad364",null)),P=O.exports,Q=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100"},[a("div",{ref:"scrollContainer",staticClass:"scroll-container",on:{mouseover:t.stopScrolling,mouseleave:t.startScrolling}},[a("div",{staticClass:"scroll-content",style:t.scrollStyle},[a("ul",{staticClass:"area-list"},[t._l(t.localBaselist,(function(e,i){return a("li",{key:e.uniqueKey,class:["area-item flex-item cursor-pointer",{"odd-item":i%2===0}],on:{click:function(a){return t.linkshebei(e)}}},[t._m(0,!0),a("div",{staticClass:"flex-content information-title d-flex align-items-center"},[t._v(t._s(e.BASE_NAME))]),a("div",{staticClass:"d-flex align-items-center"},[t._v("运行良好")])])})),t.needExtraItem?a("li",{key:"extra-item",staticClass:"area-item flex-item"}):t._e()],2),t.shouldScroll?a("ul",{staticClass:"area-list"},[t._l(t.localBaselist,(function(e,i){return a("li",{key:e.uniqueKey,class:["area-item flex-item cursor-pointer",{"odd-item":i%2===0}],on:{click:function(a){return t.linkshebei(e)}}},[t._m(1,!0),a("div",{staticClass:"flex-content information-title d-flex align-items-center"},[t._v(t._s(e.BASE_NAME))]),a("div",{staticClass:"d-flex align-items-center"},[t._v("运行良好")])])})),t.needExtraItem?a("li",{key:"duplicate-extra-item",staticClass:"area-item flex-item"}):t._e()],2):t._e()])])])},U=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"d-flex h-100 align-items-center justify-content-center"},[i("img",{attrs:{src:a("e864"),alt:""}})])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"d-flex h-100 align-items-center justify-content-center"},[i("img",{attrs:{src:a("e864"),alt:""}})])}],F=a("5530"),z={props:{baselist:{type:Array,required:!0}},data:function(){return{scrollOffset:0,scrollInterval:null,contentHeight:0,localBaselist:[]}},computed:{needExtraItem:function(){return this.baselist.length%2!==0},shouldScroll:function(){return this.baselist.length>5},scrollStyle:function(){return this.shouldScroll?{transform:"translateY(".concat(-this.scrollOffset,"px)")}:{}}},watch:{baselist:{immediate:!0,handler:function(){this.initializeLocalBaselist(),this.resetScroll()},deep:!0}},mounted:function(){this.resetScroll()},methods:{initializeLocalBaselist:function(){this.localBaselist=this.baselist.map((function(t,e){return Object(F["a"])(Object(F["a"])({},t),{},{uniqueKey:"".concat(t.ID,"-").concat(e)})}))},linkshebei:function(t){this.$router.push({path:"/detail",query:{base_id:t.ID,base_name:t.BASE_NAME}})},resetScroll:function(){var t=this;this.stopScrolling(),this.$nextTick((function(){var e=t.$refs.scrollContainer;t.contentHeight=t.shouldScroll?e.children[0].offsetHeight/2:0,t.scrollOffset=0,t.shouldScroll&&t.startScrolling()}))},startScrolling:function(){var t=this;if(this.shouldScroll){var e=.5,a=performance.now(),i=function i(n){var s=n-a;a=n,t.scrollOffset+=e*(s/16.67),t.scrollOffset>=t.contentHeight&&(t.scrollOffset=0),t.scrollInterval=requestAnimationFrame(i)};this.scrollInterval=requestAnimationFrame(i)}},stopScrolling:function(){this.scrollInterval&&(cancelAnimationFrame(this.scrollInterval),this.scrollInterval=null)}},beforeDestroy:function(){this.stopScrolling()}},j=z,R=(a("fddf"),Object(f["a"])(j,Q,U,!1,null,"58b1a1f8",null)),Y=R.exports,q=function(){var t=this,e=t.$createElement;t._self._c;return t._m(0)},W=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"w-100 h-100 position-relative d-flex flex-column"},[i("div",{staticClass:"w-100 h-50 d-flex flex-row"},[i("div",{staticClass:"device-info mb-n2 mr-2"},[i("div",{staticClass:"device-icon"},[i("img",{attrs:{src:a("ce6c"),alt:""}})]),i("div",{staticClass:"device-text"},[i("div",{staticClass:"device-name"},[t._v("投饲机")]),i("div",{staticClass:"device-number"},[i("span",[t._v("6")]),i("span",[t._v("台")])]),i("div",{staticClass:"device-line"})])]),i("div",{staticClass:"device-info mb-n2 ml-2"},[i("div",{staticClass:"device-icon"},[i("img",{attrs:{src:a("a355"),alt:""}})]),i("div",{staticClass:"device-text"},[i("div",{staticClass:"device-name"},[t._v("增氧机")]),i("div",{staticClass:"device-number"},[i("span",[t._v("6")]),i("span",[t._v("台")])]),i("div",{staticClass:"device-line"})])])]),i("div",{staticClass:"w-100 h-50 d-flex flex-row"},[i("div",{staticClass:"device-info mt-2 mr-2"},[i("div",{staticClass:"device-icon"},[i("img",{attrs:{src:a("09d8"),alt:""}})]),i("div",{staticClass:"device-text"},[i("div",{staticClass:"device-name"},[t._v("水质监测")]),i("div",{staticClass:"device-number"},[i("span",[t._v("8")]),i("span",[t._v("套")])]),i("div",{staticClass:"device-line"})])]),i("div",{staticClass:"device-info mt-2 ml-2"},[i("div",{staticClass:"device-icon"},[i("img",{attrs:{src:a("ce6c"),alt:""}})]),i("div",{staticClass:"device-text"},[i("div",{staticClass:"device-name"},[t._v("气象监测站")]),i("div",{staticClass:"device-number"},[i("span",[t._v("8")]),i("span",[t._v("套")])]),i("div",{staticClass:"device-line"})])])])])}],H={},G=H,X=(a("0814"),Object(f["a"])(G,q,W,!1,null,"5daee5f8",null)),J=X.exports,K=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"header-box d-flex flex-row position-relative"},[a("div",{staticClass:"header-left position-absolute d-flex flex-row"},[a("div",{staticClass:"other"},[t._v(" "+t._s(t.currentTime)+" ")]),t.isShow?a("div",{staticClass:"button-box "},[a("div",{class:["cursor-pointer",{"active-route":t.isActiveCultiver}],on:{click:function(e){return t.goToMonitor("/cultiver")}}},[t._v("养殖日志")]),a("div",{staticClass:"cursor-pointer"},[t._v("智能控制")])]):t._e()]),a("div",{staticClass:" w-100 h-100 d-flex justify-content-center"},[a("div",{staticClass:"header-center "},[t._v(" "+t._s(t.base_name||"渔业数智化养殖管控平台")+" ")])]),a("div",{staticClass:"header-right position-absolute d-flex flex-row ",staticStyle:{"justify-content":"flex-end"}},[t.isShow?a("div",{staticClass:"button-box"},[a("div",{staticClass:"cursor-pointer",on:{click:function(e){return t.$router.push("/home")}}},[t._v("监控大屏")]),a("div",{staticClass:"cursor-pointer",on:{click:function(e){return t.goToMonitor("/data/history/")}}},[t._v("数据中心")])]):t._e(),a("div",{staticClass:"other d-flex flex-row-reverse align-items-start cursor-pointer",on:{click:function(e){return t.exitLogion()}}},[a("div",[t._v(" 退出登录 ")]),a("img",{attrs:{src:t.loginImg,alt:""}})])])])},V=[],Z=(a("b0c0"),a("4d90"),{props:{isShow:{type:Boolean,default:!1}},data:function(){return{base_name:"",timer:"",currentTime:(new Date).toLocaleString(),loginImg:a("40f4")}},mounted:function(){var t=this;this.timer=setInterval((function(){t.currentTime=t.formatDate(new Date)}),1e3)},computed:{isActiveCultiver:function(){return"/cultiver"===this.$route.path}},watch:{"$route.query.base_name":{immediate:!0,handler:function(t){this.base_name=t||"渔业数智化养殖管控平台"}}},methods:{goToMonitor:function(t){this.$route.path!==t&&this.$router.push({path:t,query:{base_id:this.$route.query.base_id,base_name:this.$route.query.base_name}}).catch((function(t){if("NavigationDuplicated"!==t.name)throw t}))},formatDate:function(t){var e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0"),n=String(t.getHours()).padStart(2,"0"),s=String(t.getMinutes()).padStart(2,"0"),o=String(t.getSeconds()).padStart(2,"0");return"".concat(e,"-").concat(a,"-").concat(i," ").concat(n,":").concat(s,":").concat(o)},exitLogion:function(){this.$router.push({path:"/login"})}},beforeDestroy:function(){clearInterval(this.timer)}}),$=Z,tt=(a("ef50"),Object(f["a"])($,K,V,!1,null,"68634e70",null)),et=tt.exports,at=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"chartContainer",staticStyle:{width:"100%",height:"100%"}})},it=[],nt=(a("e39c"),a("2909"));function st(t,e,a,i,n,s){var o=(t+e)/2,r=t*Math.PI*2,l=e*Math.PI*2,c=o*Math.PI*2;0===t&&1===e&&(a=!1),n="undefined"!==typeof n?n:1/3;var d=a?.1*Math.cos(c):0,u=a?.1*Math.sin(c):0,m=1.2;return{u:{min:-Math.PI,max:3*Math.PI,step:Math.PI/32},v:{min:0,max:2*Math.PI,step:Math.PI/20},x:function(t,e){return t<r?d+Math.cos(r)*(1+Math.cos(e)*n)*m:t>l?d+Math.cos(l)*(1+Math.cos(e)*n)*m:d+Math.cos(t)*(1+Math.cos(e)*n)*m},y:function(t,e){return t<r?u+Math.sin(r)*(1+Math.cos(e)*n)*m:t>l?u+Math.sin(l)*(1+Math.cos(e)*n)*m:u+Math.sin(t)*(1+Math.cos(e)*n)*m},z:function(t,e){return t<.5*-Math.PI||t>2.5*Math.PI?Math.sin(t):Math.sin(e)>0?.8*s:-1}}}function ot(t,e){for(var a=[],i=0,n=0,s=0,o=[],r=.2,l=0;l<t.length;l++){i+=t[l].value;var c={name:"undefined"===typeof t[l].name?"series".concat(l):t[l].name,type:"surface",parametric:!0,wireframe:{show:!1},pieData:t[l],pieStatus:{selected:!1,hovered:!1,k:r}};if("undefined"!=typeof t[l].itemStyle){var d={};"undefined"!=typeof t[l].itemStyle.color&&(d.color=t[l].itemStyle.color),"undefined"==typeof t[l].itemStyle.opacity||(d.opacity=1),c.itemStyle=d}a.push(c)}for(var u=Math.max.apply(Math,Object(nt["a"])(a.map((function(t){return t.pieData.value})))),m=200,f=0;f<a.length;f++){s=n+a[f].pieData.value,a[f].pieData.startRatio=n/i,a[f].pieData.endRatio=s/i;var h=a[f].pieData.value/u*m;a[f].parametricEquation=st(a[f].pieData.startRatio,a[f].pieData.endRatio,!1,!1,r,h),n=s,o.push(a[f].name)}return a.push({name:"mouseoutSeries",type:"surface",parametric:!0,wireframe:{show:!1},itemStyle:{opacity:.6,color:function(t){var e=t.data[0],a=t.data[1],i=t.data[2],n=Math.max(0,1-Math.sqrt(e*e+a*a+i*i)/5);return"rgba(44, 120, 157, ".concat(n,")")}},parametricEquation:{u:{min:0,max:2*Math.PI,step:Math.PI/40},v:{min:0,max:2*Math.PI,step:Math.PI/40},x:function(t,e){return(Math.sin(e)*Math.sin(t)+Math.sin(t))/Math.PI*2.5},y:function(t,e){return(Math.sin(e)*Math.cos(t)+Math.cos(t))/Math.PI*2.5},z:function(t,e){return Math.cos(e)>0?-.5:-20}}}),a.push({name:"mouseoutSeries",type:"surface",parametric:!0,wireframe:{show:!1},itemStyle:{opacity:.1,color:"#99e5f7"},parametricEquation:{u:{min:0,max:2*Math.PI,step:Math.PI/20},v:{min:0,max:2*Math.PI,step:Math.PI/20},x:function(t,e){return(Math.sin(e)*Math.sin(t)+Math.sin(t))/Math.PI*2.9},y:function(t,e){return(Math.sin(e)*Math.cos(t)+Math.cos(t))/Math.PI*2.9},z:function(t,e){return Math.cos(e)>0?-16:-20}}}),a}function rt(t){var e=ot(t,.8);return e.push({name:"pie2d",type:"pie",label:{opacity:1,fontSize:13,lineHeight:20,textStyle:{fontSize:12}},labelLine:{length:5,length2:50},startAngle:-70,clockwise:!1,radius:["15%","60%"],center:["50%","45%"],data:t,itemStyle:{opacity:0}}),{legend:{tooltip:{show:!0},data:t.map((function(t){return t.name})),bottom:"0%",textStyle:{fontSize:12},itemStyle:{opacity:1},itemWidth:15,itemHeight:8,itemGap:10},animation:!0,tooltip:{backgroundColor:"rgb(189, 219, 241,0.8)",formatter:function(e){if("mouseoutSeries"!==e.seriesName&&"pie2d"!==e.seriesName)return"<span style='color:#23457f'>".concat(e.seriesName,"<br/>").concat(t[e.seriesIndex].value,"</span>")},textStyle:{fontSize:12}},title:{x:"center",top:"20",textStyle:{color:"#fff",fontSize:22}},backgroundColor:"rgba(0, 0, 0,0)",labelLine:{show:!0},label:{show:!0,position:"outside",formatter:function(t){var e=Math.round(t.percent);t.color;return"{name|".concat(t.name,"}\n{style|").concat(e,"%}")},rich:{name:{fontSize:12,color:"#bddbf1",fontWeight:400,fontFamily:"PingFangSC"},style:{fontSize:16,fontWeight:400,fontFamily:"PingFangSC-Semibold",color:"auto"}}},xAxis3D:{min:-1,max:1},yAxis3D:{min:-1,max:1},zAxis3D:{min:-1,max:1},grid3D:{show:!1,boxHeight:.5,top:"-5%",bottom:"50%",viewControl:{distance:180,alpha:25,beta:60,autoRotate:!1,rotateSensitivity:0,zoomSensitivity:0,panSensitivity:0}},series:e}}var lt=a("313e"),ct={props:{Data:{type:Array,required:!0}},data:function(){return{chartInstance2:null,resizeObserver:null,optionsData:[{name:"贝",value:2e3,itemStyle:{color:"rgba(179,229,251,1)"}},{name:"蟹",value:1e3,itemStyle:{color:"rgba(250,210,95,1)"}},{name:"鱼",value:100,itemStyle:{color:"rgba(249,170,114,1)"}},{name:"虾",value:200,itemStyle:{color:"rgba(79,191,227,1)"}}]}},watch:{Data:{handler:function(t){console.log(t),this.updateData(t)},deep:!0}},mounted:function(){var t=this;this.$refs.chartContainer&&(this.chartInstance2=lt["init"](this.$refs.chartContainer,this.$echartsConfig.theme),this.updateChart(),this.resizeObserver=new ResizeObserver((function(){t.chartInstance2&&t.chartInstance2.resize()})),this.resizeObserver.observe(this.$refs.chartContainer))},methods:{updateChart:function(){var t=rt(this.optionsData);this.chartInstance2&&this.chartInstance2.setOption(t,!0)},updateData:function(t){this.optionsData=t,this.updateChart()}},beforeDestroy:function(){this.chartInstance2&&this.chartInstance2.dispose(),this.resizeObserver&&this.$refs.chartContainer&&(this.resizeObserver.unobserve(this.$refs.chartContainer),this.resizeObserver.disconnect())}},dt=ct,ut=Object(f["a"])(dt,at,it,!1,null,null,null),mt=ut.exports,ft=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"chartContainer",staticStyle:{width:"100%",height:"100%"}})},ht=[],pt=(a("00b4"),a("fb6a"),a("a15b"),a("201b"));function gt(t){var e=Math.max.apply(Math,Object(nt["a"])(t[0].data)),a=Math.max.apply(Math,Object(nt["a"])(t[1].data)),i=[],n=[],s=[],o=16,r=9,l=60,c=460,d=["#000","#000","#000"],u=pt.color,m={series:t,chartList:["金坛","宣城","城电","竖新","金山","奉贤"]};function f(t,e){var a=/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/,i=t.toLowerCase();if("transparent"==i)return"transparent";if(i&&a.test(i)){if(4===i.length){for(var n="#",s=1;s<4;s+=1)n+=i.slice(s,s+1).concat(i.slice(s,s+1));i=n}for(var o=[],r=1;r<7;r+=2)o.push(parseInt("0x"+i.slice(r,r+2)));return"rgba("+o.join(",")+","+e+")"}return i}return m.series&&m.series.length&&(m.series.forEach((function(t,e){i=i.concat(t.data)})),m.series[0].data.map((function(){return c})),m.series[0].data.map((function(t,e){return 1})),m.series.forEach((function(t,e){s.push({name:t.name,bottom:"-1%",itemStyle:{color:u[e%u.length]}});var a=e;n.push({data:t.data,type:"pictorialBar",yAxisIndex:a,tooltip:{show:!1},barMaxWidth:12,symbolPosition:"end",symbol:"diamond",symbolOffset:[(-.5*(m.series.length-1)+e+-.5*l*.01*(m.series.length-1)+.01*l*e)*r,"-47%"],symbolRotate:280,symbolSize:[.4*r,1*r],zlevel:2}),n.push({data:t.data,type:"pictorialBar",yAxisIndex:a,tooltip:{show:!1},barMaxWidth:12,color:{x:0,y:1,x2:0,y2:0,type:"linear",global:!1,colorStops:[{offset:0,color:"transparent"},{offset:.2,color:f(d[e%d.length],.1)},{offset:1,color:f(d[e%d.length],.3)}]},symbolPosition:"end",symbol:"rect",symbolSize:[r/1.1,"100%"],symbolOffset:[(-.5*(m.series.length-1)+e+-.5*l*.01*(m.series.length-1)+.01*l*e-.25)*r,0],zlevel:1}),n.push({data:t.data,type:"bar",name:t.name,yAxisIndex:a,barGap:l+"%",barWidth:r,barMaxWidth:12,label:{show:!1,position:"top",distance:.3*o,textStyle:{color:u[e%u.length],fontSize:o}},showBackground:!1,itemStyle:{color:{x:0,y:1,x2:0,y2:0,type:"linear",global:!1,colorStops:[{offset:0,color:"rgba(23, 74, 121, 1)"},{offset:.5,color:f(u[e%u.length],.5)},{offset:1,color:f(u[e%u.length],1)}]}}})})),n.push({name:"1",type:"pictorialBar",z:-1,yAxisIndex:0,tooltip:{show:!1},data:[0,0,0,0,0,0],barWidth:12,color:{x:0,y:1,x2:0,y2:0,type:"linear",global:!1,colorStops:[{offset:1,color:"rgba(23, 74, 121, 0)"},{offset:0,color:"rgba(23, 74, 121, 0.4)"}]},symbol:"rect",symbolSize:[25,118],symbolPosition:"start",symbolOffset:[0,0]})),{title:{text:"",top:20,left:"center",textStyle:{color:"#fff",fontSize:20}},tooltip:{show:!0,trigger:"axis",borderWidth:0,confine:!1,appendToBody:!0,backgroundColor:"rgb(189, 219, 241,.1)",formatter:function(t){var e="<div style='font-size: 12px;'>"+t[0].name+"</div>";return t.forEach((function(t,a){var i='<span style="display:inline-block;font-size: 12px;margin-right:5px;border-radius:50%;width:'.concat(o,"px;height:").concat(o,"px;background:").concat(u[a],'"></span>');e+="<div style='display:flex;align-items:center;font-size: 12px; width: 150px'>"+i+'<span style="color:'.concat(u[a],'; font-weight: bold">').concat(t.seriesName,"</span>")+(t.seriesName?"<span style='display:inline-block;margin-right:6px;color: #00b5eb;'></span>":"")+'<span style="color:'.concat(u[a],'; font-weight: bold">').concat(t.data,"</span>")+"</div>"})),e},axisPointer:{type:"shadow",shadowStyle:{color:"rgba(35,49,77,0.01)"}},textStyle:{color:"#fff",fontSize:o}},grid:{left:"6%",right:"3%",bottom:"12%",top:"18%",containLabel:!0},legend:{data:m.series.map((function(t){return t.name})),itemWidth:15,itemHeight:8,itemGap:10,left:"center",bottom:"-5"},xAxis:{data:m.chartList,type:"category",interval:0},yAxis:[{type:"value",min:0,max:e,name:"养殖规模(亩)",nameTextStyle:{color:"#bddbf1",fontSize:12}},{max:a,type:"value",name:"预期产量(吨)",nameTextStyle:{color:"#bddbf1",fontSize:12},position:"right",min:0}],series:n}}var At={data:function(){return{chartInstance2:null,resizeObserver:null}},props:{volumeDetailData:{type:Array,required:!0,default:function(){return[{name:"养殖规模(亩)",data:[1,2,3,4,5,8]},{name:"预期产量(吨)",data:[369,350,269,460,200,213]}]}}},mounted:function(){var t=this;this.$refs.chartContainer&&(this.chartInstance2=lt["init"](this.$refs.chartContainer,this.$echartsConfig.theme),this.updateChart(),this.resizeObserver=new ResizeObserver((function(){t.chartInstance2&&t.chartInstance2.resize()})),this.resizeObserver.observe(this.$refs.chartContainer))},methods:{updateChart:function(){var t=gt(this.volumeDetailData);this.chartInstance2&&this.chartInstance2.setOption(t,!0)}},watch:{volumeDetailData:{deep:!0,handler:function(){this.updateChart()}}},beforeDestroy:function(){this.chartInstance2&&this.chartInstance2.dispose(),this.resizeObserver&&this.$refs.chartContainer&&(this.resizeObserver.unobserve(this.$refs.chartContainer),this.resizeObserver.disconnect())}},bt=At,vt=Object(f["a"])(bt,ft,ht,!1,null,null,null),Ct=vt.exports,wt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100 position-relative d-flex flex-row justify-content-sm-between"},[a("div",{staticClass:"water-item"},[a("div",{staticClass:"d-flex align-items-center justify-content-center w-100 mt-2 position-relative",staticStyle:{height:"40%"}},[a("div",{staticClass:"g-progress1"}),a("div",{staticClass:"g-progress2"}),a("div",{staticClass:"g-progress",style:t.getProgressStyle(t.alertlist.o2,10)}),t._m(0)]),a("div",{staticClass:"water-item-box"},[a("div",{staticClass:"water-item-title"},[t._v("O2")]),a("div",{staticClass:"water-item-value"},[a("span",[t._v(t._s(t.alertlist.o2))]),a("span",[t._v("mg/L")])])])]),a("div",{staticClass:"water-item"},[a("div",{staticClass:"d-flex align-items-center justify-content-center w-100 mt-2 position-relative",staticStyle:{height:"40%"}},[a("div",{staticClass:"g-progress1"}),a("div",{staticClass:"g-progress2"}),a("div",{staticClass:"g-progress",style:t.getProgressStyle(t.alertlist.ph,14)}),t._m(1)]),a("div",{staticClass:"water-item-box"},[a("div",{staticClass:"water-item-title"},[t._v("PH")]),a("div",{staticClass:"water-item-value"},[a("span",[t._v(t._s(t.alertlist.ph))])])])]),a("div",{staticClass:"water-item"},[a("div",{staticClass:"d-flex align-items-center justify-content-center w-100 mt-2 position-relative",staticStyle:{height:"40%"}},[a("div",{staticClass:"g-progress1"}),a("div",{staticClass:"g-progress2"}),a("div",{staticClass:"g-progress",style:t.getProgressStyle(t.alertlist.tem,40)}),t._m(2)]),a("div",{staticClass:"water-item-box"},[a("div",{staticClass:"water-item-title"},[t._v("温度")]),a("div",{staticClass:"water-item-value"},[a("span",[t._v(t._s(t.alertlist.tem))]),a("span",[t._v("℃")])])])])])},xt=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"progress"},[i("img",{attrs:{src:a("1afe"),alt:""}})])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"progress"},[i("img",{attrs:{src:a("8810"),alt:""}})])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"progress"},[i("img",{staticStyle:{width:"45%"},attrs:{src:a("b603"),alt:""}})])}],yt={props:{alertlist:{type:Object,required:!0}},methods:{getProgressStyle:function(t,e){var a=t/e*360;return{background:"conic-gradient(#5596df 0deg, #5596df ".concat(a,"deg, rgba(238, 238, 238, 0) ").concat(a,"deg, rgba(238, 238, 238, 0) 360deg)")}}}},Et=yt,It=(a("6f8c"),Object(f["a"])(Et,wt,xt,!1,null,"0033d709",null)),Dt=It.exports,St=a("bc3a"),_t=a.n(St),kt=a("c38d"),Nt=a.n(kt),Bt={components:{SubtitledSection:E,SubtitledSectionLong:N,CompanyIntroduction:P,BaseList:Y,DeviceDescription:J,Head2:et,BreedingTypes:mt,BaseSize:Ct,WaterQualityModule:Dt},data:function(){return{videoSrc:a("eb59"),videoSrc2:a("5f68"),bgImage:a("317e"),promptImage:a("df74"),jiankong1Image:a("470a"),jiankong2Image:a("fea9c"),videoAddresses:[],SerialNumber:[],video_address:[],cameralist:[],base_id:"",shelist:[],jktokens:{value:""},shebeilist:{water:[],meteorological:[],monitoring:[]},box:0,intervalId:null,baselist:[{ID:1,BASE_NAME:"上海海洋大学养殖基地 ",BASE_LOCATION:"121.89691,30.882894",INTRODUCE:"上海海洋大学将利用学科优势，整合学校资源，以崇明为示范基地，探索河蟹湖泊化生态养殖新模式以及以种植为主的立体化养殖新模式、稻渔综合种养新模式、陆基工业化对虾养殖新模式，形成绿色种养产业综合体，为上海市乡村振兴提供示范。",abbreviation:"shou"},{ID:2,BASE_NAME:"常州金坛数字化渔场",BASE_LOCATION:"119.481091,31.600419",INTRODUCE:'金坛区隶属于江苏省常州市，是久负盛名的江南鱼米之乡。拥有"长荡湖大闸蟹"养殖基地，连续多次获得"中国十大名蟹"的荣誉称号。',abbreviation:""},{ID:3,BASE_NAME:"上海裕安养殖场 ",BASE_LOCATION:"121.83524,31.57505",INTRODUCE:'陈家镇裕安养殖场"渔光互补"光伏发电项目，占地3180亩，电站总装机容量110兆瓦，是崇明区第一个"渔光互补"光伏发电项目，也是上海最大的渔光互补示范工程',abbreviation:"yuan"}],wetherinfo1:{temperature:{img:a("4c23"),value:"0"},humidity:{img:a("95b0"),value:"0"},wind_direction:{img:a("5141"),value:"0"},wind_power:{img:a("378c"),value:"0"},guangzhao:{img:a("ca38"),value:"-"},precipitation:{img:a("b6a0"),value:"-"},pressure:{img:a("fdea"),value:"-"}},city:"上海",livetianqi:null,alertlist:{tem:0,ph:0,o2:0},optionsData:[],volumeDetailData:[{name:"养殖规模(亩)",data:[0,0,0,0,0,0]},{name:"预期产量(吨)",data:[0,0,0,0,0,0]}]}},mounted:function(){this.renderMap(),this.getlist(),this.startPolling(),this.GetWeather()},methods:{onCloseCover:function(){this.box=0},startPolling:function(){var t=this;this.intervalId=setInterval((function(){t.getlist()}),6e4)},GetWeather:function(){var t=this;Object(d["v"])({city:this.city}).then((function(e){t.livetianqi={temperature:e.temperature,humidity:e.humidity,wind_direction:e.wind_direction,wind_power:e.wind_power,guangzhao:e.guangzhao,precipitation:e.precipitation,pressure:e.pressure},Object.keys(t.livetianqi).forEach((function(e){Object.prototype.hasOwnProperty.call(t.wetherinfo1,e)&&(t.wetherinfo1[e].value=t.livetianqi[e])})),console.log(t.wetherinfo1)})).catch((function(e){console.error("获取天气数据失败:",e),t.errorMessage="获取天气数据失败，请稍后再试。"}))},getlist:function(){var t=this;Object(d["i"])({}).then((function(e){var a={};console.log(e),e.ponds.forEach((function(t){var e=t.base,i=parseFloat(t.area)||0,n=parseFloat(t.total)/1e3||0;a[e]||(a[e]={totalArea:0,totalYield:0}),a[e].totalArea+=i,a[e].totalYield+=n})),t.volumeDetailData=[{name:"养殖规模(亩)",data:[a["常州金坛数字化渔场"]?a["常州金坛数字化渔场"].totalArea:1e3,a["宣城水产养殖基地"]?a["宣城水产养殖基地"].totalArea:1500,a["上海裕安养殖场"]?a["上海裕安养殖场"].totalArea:1300,a["竖新养殖基地"]?a["竖新养殖基地"].totalArea:1400,a["上海金山水产养殖场"]?a["上海金山水产养殖场"].totalArea:800,a["奉贤欣兴鲈鱼养殖基地"]?a["奉贤欣兴鲈鱼养殖基地"].totalArea:900]},{name:"预期产量(吨)",data:[a["常州金坛数字化渔场"]?a["常州金坛数字化渔场"].totalYield:2100,a["宣城水产养殖基地"]?a["宣城水产养殖基地"].totalYield:1900,a["上海裕安养殖场"]?a["上海裕安养殖场"].totalYield:3e3,a["竖新养殖基地"]?a["竖新养殖基地"].totalYield:1600,a["上海金山水产养殖场"]?a["上海金山水产养殖场"].totalYield:2500,a["奉贤欣兴鲈鱼养殖基地"]?a["奉贤欣兴鲈鱼养殖基地"].totalYield:3e3]}],console.log(t.volumeDetailData)})),Object(d["g"])({}).then((function(e){var a=["rgba(179,229,251,1)","rgba(250,210,95,1)","rgba(249,170,114,1)","rgba(79,191,227,1)"],i=e.productionReports.reduce((function(t,e){var a=e.species,i=parseInt(e.seedQuantity,10)||0;return t[a]||(t[a]=0),t[a]+=i,t}),{});t.optionsData=Object.entries(i).map((function(t,e){var i=Object(b["a"])(t,2),n=i[0],s=i[1];return{name:n,value:s,itemStyle:{color:a[e%a.length]}}})).sort((function(t,e){return e.value-t.value}))})),Object(d["w"])({}).then((function(e){if(t.baselist=e.data.baselist,t.updateMapMarkers(),e.data.alertlist.length>0){var a=e.data.alertlist[0];a.describe}else console.error("alertlist 为空")})),Object(d["y"])({base_id:"3",equipmentID:11}).then((function(e){var a=e.data.shuju;t.lineData=[],t.alertlist.tem=a.templist[0].y[0].data.at(-1),t.alertlist.ph=a.templist[2].y[0].data.at(-1),t.alertlist.o2=a.templist[1].y[0].data.at(-1)}))},renderMap:function(){var t=this;this.$AMapLoader.load({key:"bb41d02b6376f70646e2490b6bf5f80b",version:"1.4.15",plugins:[],AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}}).then((function(e){t.AMap=e,t.map=new e.Map("map-container",{mapStyle:"amap://styles/whitesmoke",zoom:8,layers:[new e.TileLayer.Satellite],center:[120.559745,31.29811]}),t.updateMapMarkers()}))},updateMapMarkers:function(){var t=this;this.map&&(this.map.clearMap(),this.baselist.forEach((function(e,i){var n=0===i||2===i,s=new t.AMap.Marker({position:e.BASE_LOCATION.split(",").map(parseFloat),label:{content:e.BASE_NAME,direction:n?"right":"center",offset:n?new t.AMap.Pixel(0,-20):new t.AMap.Pixel(0,-50),style:{fontSize:"20",color:"#000"}},icon:new t.AMap.Icon({size:new t.AMap.Size(60,50),image:a("edcb"),imageSize:new t.AMap.Size(60,50)}),offset:new t.AMap.Pixel(-25,-55)});s.on("click",(function(){t.box=1,t.miaoshu=e.INTRODUCE,t.map.setCenter(e.BASE_LOCATION.split(",").map((function(t){return parseFloat(t)}))),t.$forceUpdate(),t.$nextTick((function(){t.adjustEquipBoxSize()}))})),t.map.add(s)})))},adjustEquipBoxSize:function(){var t=document.querySelector(".equip-box .adaptive-text"),e=document.querySelector(".equip-box .adaptive-bg");if(t&&e){var a=document.createElement("div");a.style.cssText="\n          position: absolute;\n          visibility: hidden;\n          white-space: pre-wrap;\n          font-family: PingFang SC;\n          font-size: 16px;\n          line-height: 1.6;\n          padding: 2rem 2rem;\n          box-sizing: border-box;\n          width: ".concat(t.offsetWidth,"px;\n          word-wrap: break-word;\n          overflow-wrap: break-word;\n          text-align: center;\n        "),a.textContent=this.miaoshu,document.body.appendChild(a);var i=Math.max(a.offsetHeight,200),n=400,s=Math.min(i,n);e.style.height=s+"px",document.body.removeChild(a)}}},beforeDestroy:function(){this.intervalId&&clearInterval(this.intervalId)}},Mt=Bt,Tt=(a("da54"),Object(f["a"])(Mt,g,A,!1,null,"1361d71f",null)),Lt=Tt.exports,Ot=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100 d-flex flex-column  "},[a("div",{staticClass:"w-100 h-100 map-container position-absolute ",attrs:{id:"map-container"}}),a("img",{staticClass:"w-100 h-100 position-absolute top-0 left-0",staticStyle:{"pointer-events":"none"},attrs:{src:t.bgImage,alt:""}}),1===t.box?a("div",{staticClass:"equip-box",style:{backgroundImage:"url("+t.backgroundImage+")",height:(this.miaoshuFormatted.length,"20rem")}},[a("div",{staticClass:"text"},[a("div",{staticClass:"info-table"},t._l(t.miaoshuFormatted,(function(e,i){return a("div",{key:i,class:["info-row",{"full-width":i<3}]},[a("div",{staticClass:"info-label"},[a("span",{style:{color:t.labelcolor}},[t._v(t._s(e.label))])]),a("div",{staticClass:"info-value"},[a("span",{style:{color:t.valuecolor}},[t._v(t._s(e.value))])])])})),0)]),a("div",{staticClass:"close-icon d-flex align-items-center justify-content-center",on:{click:t.onCloseCover}},[a("svg",{staticClass:"icon",attrs:{t:"1639825855503",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"26567",width:"1rem",height:"1rem"}},[a("path",{attrs:{d:"M846.005097 957.24155c-28.587082 0-57.174164-10.911514-78.996169-32.733519L96.632851 254.131955c-43.644009-43.644009-43.**********.348328 0-157.992337s114.348328-43.**********.992337 0L925.001265 766.515694c43.644009 43.644009 43.**********.348328 0 157.992337C903.17926 946.330036 874.592179 957.24155 846.005097 957.24155z","p-id":"26568",fill:"#ffffff"}}),a("path",{attrs:{d:"M175.62902 957.24155c-28.587082 0-57.174164-10.911514-78.996169-32.733519-43.644009-43.644009-43.**********.348328 0-157.992337L767.008928 96.139617c43.644009-43.**********.348328-43.**********.992337 0s43.**********.348328 0 157.992337L254.**********.508032C232.**********.**********.**********.24155 175.62902 957.24155z","p-id":"26569",fill:"#ffffff"}})])])]):t._e(),a("div",{staticClass:"header"},[a("Head2",{attrs:{isShow:!0}})],1),a("div",{staticClass:"main d-flex flex-row  "},[a("div",{staticClass:"home-sidebar  "},[a("div",{staticStyle:{height:"64.8%"}},[a("SubtitledSection",{attrs:{title:"设备列表"}},[[a("DeviceList",{attrs:{shebeilist:t.shebeilist},on:{"link-shebei":t.handleLinkShebei}})]],2)],1),a("div",{staticStyle:{height:"32.6%"}},[a("SubtitledSection",{attrs:{title:"报警信息"}},[[a("AlarmList",{attrs:{baselist:t.alertYuanList}})]],2)],1)]),a("div",{staticClass:"home-center"},[a("div",{staticClass:"info-box"},t._l(t.wetherinfo1,(function(e,i){return a("div",{key:i,staticClass:"info-item",style:{backgroundImage:"url("+e.img+")"}},[a("div",{staticStyle:{"margin-left":"3.4rem"}},[t._v(" "+t._s(e.value)+" ")])])})),0),a("div",{staticClass:"bottom-box "},[a("div",{staticStyle:{width:"49.5%"}},[a("SubtitledSectionLong",{attrs:{title:"设备监控"}},[[a("div",{staticClass:"video-wrapper"},[a("div",{staticClass:"video-content",attrs:{id:"video-container"}},[a("img",{attrs:{src:t.jiankong2Image,alt:"视频占位图"}})])])]],2)],1),a("div",{staticStyle:{width:"49.5%","margin-left":"0.6rem"}},[a("SubtitledSectionLong",{attrs:{title:"设备监控"}},[[a("div",{staticClass:"video-wrapper"},[a("div",{staticClass:"video-content",attrs:{id:"video-container1"}},[a("img",{attrs:{src:t.jiankong1Image,alt:"视频占位图"}})])])]],2)],1)])]),a("div",{staticClass:"home-sidebar "},[a("div",{staticStyle:{height:"100%"}},[a("SubtitledSection",{staticStyle:{"pointer-events":"auto"},attrs:{title:"设备参数"}},[[a("div",{staticClass:"h-100 w-100"},[a("div",{staticClass:"w-100 ",staticStyle:{height:"15px"}},[a("el-row",{attrs:{type:"flex",align:"middle",justify:"center"}},[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"time-container"},[a("i",{staticClass:"el-icon-time"}),a("span",{staticStyle:{"margin-left":"10px"}},[t._v(t._s(t.currentTime))])])])],1)],1),a("div",{staticClass:"w-100 right-item1"},[a("div",{staticClass:"right-item1-bg"},[a("div",[a("div",[t._v(t._s(this.TEM))]),a("div",[t._v("温度")])]),a("div",[a("div",[t._v(t._s(1==this.selectid?this.PH:this.HUMIDITY))]),a("div",[t._v(t._s(1==this.selectid?"P H":"湿度"))])]),a("div",[a("div",[t._v(t._s(1==this.selectid?this.O2:this.ATM))]),a("div",[t._v(t._s(1==this.selectid?"含氧量":"气压"))])]),a("div",[a("div",[t._v(t._s(1==this.selectid?this.SALT:this.CON))]),a("div",[t._v(t._s(1==this.selectid?"电导":"雨量"))])])])]),a("div",{staticClass:"w-100 right-item2",class:1==this.selectid?"shebei1":"shebei2"},[a("span",[t._v(t._s(1==this.selectid?"水温":"温度"))]),t.lineData&&t.lineData.length>0&&t.lineData[0]?a("chartline",{attrs:{Data:t.lineData[0]}}):t._e()],1),a("div",{staticClass:"w-100 right-item2",class:1==this.selectid?"shebei1":"shebei2"},[a("span",[t._v(t._s(1==this.selectid?"PH":"湿度"))]),t.lineData&&t.lineData.length>0&&t.lineData[1]?a("chartline",{attrs:{Data:t.lineData[1]}}):t._e()],1),a("div",{staticClass:"w-100 right-item2",class:1==this.selectid?"shebei1":"shebei2"},[a("span",[t._v(t._s(1==this.selectid?"含氧量":"气压"))]),t.lineData&&t.lineData.length>0&&t.lineData[2]?a("chartline",{attrs:{Data:t.lineData[2]}}):t._e()],1)])]],2)],1)])])])},Pt=[],Qt=a("b85c"),Ut=a("53ca"),Ft=(a("5319"),a("7db0"),a("25f0"),a("a9e3"),a("caad"),a("2532"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-tabs",{staticClass:"h-100 w-100",attrs:{type:"border-card"}},[i("el-tab-pane",{attrs:{label:"水质设备"}},[i("div",{staticClass:"scrollable-container h-100"},[i("ul",{staticClass:"area-list"},t._l(t.shebeilist.water,(function(e){return i("li",{key:e.ID,staticClass:"area-item flex-item cursor-pointer",on:{click:function(a){return t.emitLinkShebei("water",e)}}},[i("div",{staticClass:"d-flex h-100 align-items-center justify-content-center"},[i("img",{attrs:{src:a("e864"),alt:""}})]),i("div",{staticClass:"flex-content information-title d-flex align-items-center"},[t._v(t._s(e.BACILITIES_NAME)+" -> "+t._s(e.EQUIPMENT_NAME))]),i("div",{staticClass:"d-flex align-items-center"},[t._v("运行良好")])])})),0)])]),i("el-tab-pane",{attrs:{label:"气象设备"}},[i("div",{staticClass:"scrollable-container h-100"},[i("ul",{staticClass:"area-list"},t._l(t.shebeilist.meteorological,(function(e){return i("li",{key:e.ID,staticClass:"area-item flex-item cursor-pointer",on:{click:function(a){return t.emitLinkShebei("meteorological",e)}}},[i("div",{staticClass:"d-flex h-100 align-items-center justify-content-center"},[i("img",{attrs:{src:a("e864"),alt:""}})]),i("div",{staticClass:"flex-content information-title d-flex align-items-center"},[t._v(t._s(e.BACILITIES_NAME)+" -> "+t._s(e.EQUIPMENT_NAME))]),i("div",{staticClass:"d-flex align-items-center"},[t._v("运行良好")])])})),0)])]),i("el-tab-pane",{attrs:{label:"监控设备"}},[i("ul",{staticClass:"area-list"},t._l(t.shebeilist.monitoring,(function(e,n){return i("li",{key:e.ID,staticClass:"area-item flex-item cursor-pointer",on:{click:function(a){return t.emitLinkShebei("monitoring",e,n)}}},[i("div",{staticClass:"d-flex h-100 align-items-center justify-content-center"},[i("img",{attrs:{src:a("e864"),alt:""}})]),i("div",{staticClass:"flex-content information-title d-flex align-items-center"},[t._v(" "+t._s(e.camera_name))]),i("div",{staticClass:"d-flex align-items-center",style:{color:1==e.state?"#43C8FF":"#F69E29"}},[t._v(t._s(1==e.state?"运行良好":"运行异常"))])])})),0)])],1)}),zt=[],jt={props:{shebeilist:{type:Object,default:function(){return{water:[],meteorological:[],monitoring:[]}}}},methods:{emitLinkShebei:function(t,e,a){this.$emit("link-shebei",t,e,a)}}},Rt=jt,Yt=(a("e7e4"),Object(f["a"])(Rt,Ft,zt,!1,null,"27eff163",null)),qt=Yt.exports,Wt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100 position-relative alarm-list-container"},[a("div",{ref:"scrollContainer",staticClass:"scroll-container",on:{mouseover:t.stopScrolling,mouseleave:t.startScrolling}},[a("div",{staticClass:"scroll-content",style:t.scrollStyle},[a("ul",{staticClass:"area-list"},[t._l(t.baselist,(function(e,i){return a("li",{key:e.ID,class:["area-item flex-item cursor-pointer",{"odd-item":i%2===0}]},[a("div",{staticClass:"d-flex align-items-center justify-content-center area-item-img"},[a("img",{attrs:{src:t.getImagePath(e.type),alt:""}})]),a("div",{staticClass:"area-item-content"},[a("div",{staticClass:"flex-content information-title d-flex align-items-center"},[t._v(t._s(e.message)+" ")]),a("div",{staticClass:" d-flex flex-row justify-content-between"},[a("div",{staticClass:"area-item-left"},[a("span",[t._v("当前值为")]),a("span",{class:1==e.type?"error1":"error2",staticStyle:{"padding-left":"5px"}},[t._v(t._s(e.value))])]),a("div",{staticClass:"area-item-right"},[a("span",[t._v(t._s(e.time))])])])])])})),t.needExtraItem?a("li",{key:"extra-item",staticClass:"area-item flex-item"}):t._e()],2),t.shouldScroll?a("ul",{staticClass:"area-list"},[t._l(t.baselist,(function(e,i){return a("li",{key:e.ID,class:["area-item flex-item cursor-pointer",{"odd-item":i%2===0}]},[a("div",{staticClass:"d-flex align-items-center justify-content-center area-item-img"},[a("img",{attrs:{src:t.getImagePath(e.type),alt:""}})]),a("div",{staticClass:"area-item-content"},[a("div",{staticClass:"flex-content information-title d-flex align-items-center"},[t._v(t._s(e.message)+" ")]),a("div",{staticClass:" d-flex flex-row justify-content-between"},[a("div",{staticClass:"area-item-left"},[a("span",[t._v("当前值为")]),a("span",{class:1==e.type?"error1":"error2",staticStyle:{"padding-left":"5px"}},[t._v(t._s(e.value))])]),a("div",{staticClass:"area-item-right"},[a("span",[t._v(t._s(e.time))])])])])])})),t.needExtraItem?a("li",{key:"extra-item",staticClass:"area-item flex-item"}):t._e()],2):t._e()])])])},Ht=[],Gt={props:{baselist:{type:Array,required:!0}},data:function(){return{scrollOffset:0,scrollInterval:null,contentHeight:0}},computed:{needExtraItem:function(){return this.baselist.length%2!==0&&this.baselist.length>3},shouldScroll:function(){return this.baselist.length>1},scrollStyle:function(){return this.shouldScroll?{transform:"translateY(".concat(-this.scrollOffset,"px)")}:{}}},watch:{baselist:{handler:function(){this.resetScroll()},deep:!0}},mounted:function(){this.resetScroll()},methods:{getImagePath:function(t){return a(1==t?"7b47":"59e0")},resetScroll:function(){var t=this;this.stopScrolling(),this.$nextTick((function(){var e=t.$refs.scrollContainer;t.contentHeight=t.shouldScroll?e.children[0].offsetHeight/2:0,t.scrollOffset=0,t.shouldScroll&&t.startScrolling()}))},startScrolling:function(){var t=this;if(this.shouldScroll){var e=.5,a=performance.now(),i=function i(n){var s=n-a;a=n,t.scrollOffset+=e*(s/16.67),t.scrollOffset>=t.contentHeight&&(t.scrollOffset=0),t.scrollInterval=requestAnimationFrame(i)};this.scrollInterval=requestAnimationFrame(i)}},stopScrolling:function(){this.scrollInterval&&(cancelAnimationFrame(this.scrollInterval),this.scrollInterval=null)}},beforeDestroy:function(){this.stopScrolling()}},Xt=Gt,Jt=(a("242c"),Object(f["a"])(Xt,Wt,Ht,!1,null,"5e7c3387",null)),Kt=Jt.exports,Vt=a("3876"),Zt={components:{SubtitledSection:E,SubtitledSectionLong:N,Head2:et,DeviceList:qt,AlarmList:Kt,chartline:Vt["a"]},data:function(){return{videoAddresses:[],SerialNumber:[],video_address:[],cameralist:[],base_id:"",shelist:[],jktokens:{value:""},bgImage:a("317e"),jiankong1Image:a("470a"),jiankong2Image:a("fea9c"),formattedData:[{label:"设备名称",value:""},{label:"设备描述",value:""},{label:"运行状态",value:""},{label:"电压",value:""},{label:"光照",value:""},{label:"风速",value:""},{label:"风向",value:""},{label:"辐射",value:""},{label:"ORP",value:""},{label:"CON",value:""}],currentTime:"",miaoshu:{EQUIPMENT_NAME:"",INTRODUCE:"",Online_Voltage:"",state:"",LIGHT:"",WIND_SPEED:"",WIND_DIRECTION:"",RADIATION:"",ORP:"",CON:"",type:1},box:0,zuobiaolist:[],shebeilist:{water:[],meteorological:[],monitoring:[]},equipmentID:"",lineData:[{name:"温度",data:[],xAxisData:[],data1:[],data2:[]},{name:"溶氧值",data:[],xAxisData:[],data1:[],data2:[]},{name:"PH",data:[],xAxisData:[],data1:[],data2:[]},{name:"雨量",data:[],xAxisData:[],data1:[],data2:[]}],videoSrc1:"",videoSrc2:"",wetherinfo1:{temperature:{img:a("4c23"),value:"0"},humidity:{img:a("95b0"),value:"0"},wind_direction:{img:a("5141"),value:"0"},wind_power:{img:a("378c"),value:"0"},guangzhao:{img:a("ca38"),value:"-"},precipitation:{img:a("b6a0"),value:"-"},pressure:{img:a("fdea"),value:"-"}},selectid:0,alertYuanList:[],TEM:0,PH:0,O2:0,SALT:0,CON:0,HUMIDITY:0,ATM:0,RAINFALL:0,markerBackgrounds:{"水质检测":a("ca67"),"气象检测":a("e973")},city:"上海",livetianqi:null,markers:[]}},computed:{backgroundImage:function(){return"水质检测"===this.miaoshu.EQUIPMENT_TYPE?a("f928"):a("bacf")},labelcolor:function(){return"水质检测"==this.miaoshu.EQUIPMENT_TYPE?"#5CA0B1":"#99BB6D"},valuecolor:function(){return"水质检测"==this.miaoshu.EQUIPMENT_TYPE?"#c6e1e7":"#e9f6d8"},miaoshuFormatted:function(){if("水质检测"==this.miaoshu.EQUIPMENT_TYPE){var t=[{label:"设备名称",value:this.miaoshu.EQUIPMENT_NAME},{label:"采集时间",value:this.miaoshu.ACQUISITION_TIME},{label:"设备描述",value:this.miaoshu.INTRODUCE},{label:"电压",value:this.miaoshu.VOLTAGE},{label:"状态",value:this.miaoshu.state},{label:"ORP",value:this.miaoshu.ORP},{label:"电导",value:this.miaoshu.CON},{label:"温度",value:this.miaoshu.TEM},{label:"PH",value:this.miaoshu.PH},{label:"含氧量",value:this.miaoshu.O2},{label:"盐度",value:this.miaoshu.SALT}];return t}if("气象检测"==this.miaoshu.EQUIPMENT_TYPE){var e=[{label:"设备名称",value:this.miaoshu.EQUIPMENT_NAME},{label:"采集时间",value:this.miaoshu.ACQUISITION_TIME},{label:"设备描述",value:this.miaoshu.INTRODUCE},{label:"电压",value:this.miaoshu.VOLTAGE},{label:"状态",value:this.miaoshu.state},{label:"光照",value:this.miaoshu.LIGHT},{label:"风速",value:this.miaoshu.WIND_SPEED},{label:"风向",value:this.miaoshu.WIND_DIRECTION},{label:"辐射",value:this.miaoshu.RADIATION},{label:"温度",value:this.miaoshu.TEM},{label:"湿度",value:this.miaoshu.HUMIDITY}];return e}return[]}},mounted:function(){this.renderMap(),this.GetCamera(),this.getlist(),this.getToken(),this.GetWeather()},methods:{startPolling:function(){this.intervalId=setInterval((function(){}),6e4)},addAlarm:function(t,e,a,i){this.alertYuanList.push({message:t,value:e,type:a,time:i})},getCurrentTime:function(){var t=new Date;return"".concat(t.getFullYear(),"-").concat(t.getMonth()+1,"-").concat(t.getDate()," ").concat(t.getHours(),":").concat(t.getMinutes(),":").concat(t.getSeconds())},handleLinkShebei:function(t,e,a){if("water"==t?this.selectid=1:"meteorological"==t?this.selectid=2:"monitoring"==t&&(this.selectid=1),console.log("处理设备点击:",t,e),"monitoring"!==t){if(e.ACQUISITION_TIME&&(this.currentTime=e.ACQUISITION_TIME.replace("T"," ")),this.selectEquipment(e,1),!e.EQUIPMENT_LOCATION)return void console.error("缺少设备位置信息:",e);console.log("设备位置:",e.EQUIPMENT_LOCATION),console.log("可用坐标列表:",this.zuobiaolist);var i=null;if(Array.isArray(e.EQUIPMENT_LOCATION))i=e.EQUIPMENT_LOCATION;else if("string"===typeof e.EQUIPMENT_LOCATION){if(i=this.zuobiaolist.find((function(t){return t.toString()===e.EQUIPMENT_LOCATION})),!i){var n=e.EQUIPMENT_LOCATION.split(",").map(Number);i=this.zuobiaolist.find((function(t){return t[0]===n[0]&&t[1]===n[1]}))}!i&&e.EQUIPMENT_LOCATION.includes(",")&&(i=e.EQUIPMENT_LOCATION.split(",").map(Number))}if(i){console.log("找到匹配坐标:",i),this.map.setCenter(i),this.map.setZoom(18),this.map.panTo(i),this.markers.forEach((function(t){t.setzIndex(100)}));var s=i.toString(),o=!1;this.markers.forEach((function(t){var e=t.getPosition();e&&e.toString()===s&&(t.setzIndex(1e3),o=!0,console.log("设置标记层级成功"))})),o||console.warn("未找到匹配的标记:",s)}else console.error("未找到匹配坐标:",e.EQUIPMENT_LOCATION);this.miaoshu=e,this.miaoshu.state=0==e.STATE?"停止":"正常",this.miaoshu.type="water"==t?1:2,this.getshebeidata(e)}else if(a%2==0?this.initPlayer(e.video_address,e.serial_number):this.initPlayer1(e.video_address,e.serial_number),e.camera_location){var r=e.camera_location.split(",").map((function(t){return parseFloat(t)}));if(r&&2===r.length){this.map.setCenter(r),this.map.setZoom(18),this.map.panTo(r),this.markers.forEach((function(t){t.setzIndex(100)}));var l=r.toString(),c=!1;this.markers.forEach((function(t){var e=t.getPosition();e&&e.toString()===l&&(t.setzIndex(1e3),c=!0,console.log("找到匹配的摄像头标记",e.toString()))})),c||console.warn("未找到匹配的摄像头标记",l)}return}},getshebeidata:function(t){var e=this;Object(d["y"])({base_id:this.$route.query.base_id,equipmentID:t.ID}).then((function(t){var a=t.data.shuju;e.lineData=[],e.lineData=[{name:"温度",id:1,data:a.templist[0].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[0].y[1].data,data2:a.templist[0].y[2].data,max:a.templist[0].max,min:a.templist[0].min}],1==e.selectid?e.lineData.push({name:"PH",id:2,data:a.templist[2].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[2].y[1].data,data2:a.templist[2].y[2].data,max:a.templist[2].max,min:a.templist[2].min},{name:"溶氧值",id:3,data:a.templist[1].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[1].y[1].data,data2:a.templist[1].y[2].data,max:a.templist[1].max,min:a.templist[1].min}):2==e.selectid&&(a.templist&&a.templist.length>0?e.lineData.push({name:"溶氧值",id:2,data:a.templist[3].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[3].y[1].data,data2:a.templist[3].y[2].data,max:a.templist[3].max,min:a.templist[3].min},{name:"PH",id:3,data:a.templist[4].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[4].y[1].data,data2:a.templist[4].y[2].data,max:a.templist[4].max,min:a.templist[4].min},{name:"雨量",id:4,data:a.templist[5].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[5].y[1].data,data2:a.templist[5].y[2].data,max:a.templist[5].max,min:a.templist[5].min}):(e.selectid=1,e.lineData=[]))}))},getlist:function(){var t=this;this.base_id=this.$route.query.base_id,Object(d["z"])({base_id:this.base_id}).then((function(e){console.log("数据接口:",e),t.getshebeidata(e.data.shebeilist[0]),console.log("res",e),t.shelist=e.data.shebeilist,t.shelist.map((function(e){e.UANDL=JSON.parse(e.UANDL),"水质检测"==e.EQUIPMENT_TYPE?t.shebeilist.water.push(e):"气象检测"==e.EQUIPMENT_TYPE?t.shebeilist.meteorological.push(e):"监控检测"==e.EQUIPMENT_TYPE&&t.shebeilist.monitoring.push(e)}));for(var a=1;a<t.shelist.length;a++){var i=t.shelist[a],n=i.Online_Voltage;if("水质检测"===i.EQUIPMENT_TYPE){var s=i.O2,o=i.TEM,r=i.PH,l=i.UANDL,c=i.ACQUISITION_TIME,d=l;if("string"===typeof l)try{d=JSON.parse(l)}catch(v){console.error("解析 UANDL 失败:",v)}else if("object"!==Object(Ut["a"])(l)||null===l)return void console.error("UANDL 不是有效的对象或 JSON 字符串");if(d.o2&&d.o2.th&&void 0!==s&&null!==s&&(s<d.o2.th[0]||s>d.o2.th[1])){var u="".concat(i.EQUIPMENT_NAME,"的溶氧值异常"),m=s;t.addAlarm(u,m,1,c)}if(d.te&&d.te.th&&void 0!==o&&null!==o&&(o<d.te.th[0]||o>d.te.th[1])){var f="".concat(i.EQUIPMENT_NAME,"的温度异常"),h=o;t.addAlarm(f,h,1,c)}if(d.ph&&d.ph.th&&void 0!==r&&null!==r&&(r<d.ph.th[0]||r>d.ph.th[1])){var p="".concat(i.EQUIPMENT_NAME,"的PH值异常"),g=r;t.addAlarm(p,g,1,c)}if(d.vol&&d.vol.th&&void 0!==n&&null!==n&&(n<d.vol.th[0]||n>d.vol.th[1])){var A="".concat(i.EQUIPMENT_NAME,"的电压值异常"),b=n;t.addAlarm(A,b,2,c)}}}t.zuobiaolist=e.data.zuobiaoilist,t.inforlist=e.data.inforlist,t.alertlist=e.data.alertlist,t.chuangan2=e.data.chuangan2,t.chuangan=e.data.chuangan,t.shuju2=e.data.shuju2,t.aa=t.shuju2.templist[0],t.renderMap(),t.handleLinkShebei("water",{ID:1}),t.selectEquipment(t.shelist[0],1)}))},selectEquipment:function(t,e){t.ACQUISITION_TIME&&(this.currentTime=t.ACQUISITION_TIME.replace("T"," ")),this.equipmentID=t.ID,"水质检测"==t.EQUIPMENT_TYPE?(this.weatherData=!1,this.waterData=!0,this.shebeiname="设备参数  "+t.EQUIPMENT_NAME,this.PH=t.PH,this.TEM=t.TEM,this.O2=t.O2,this.SALT=t.SALT,this.CON=t.CON,this.aa=""):"气象检测"==t.EQUIPMENT_TYPE&&(this.weatherData=!0,this.waterData=!1,this.shebeiname="设备参数  "+t.EQUIPMENT_NAME,this.HUMIDITY=t.HUMIDITY,this.TEM=t.TEM,this.RAINFALL=t.RAINFALL,this.ATM=t.ATM,this.aa="");var a,i=Object(Qt["a"])(this.zuobiaolist);try{for(i.s();!(a=i.n()).done;){this.zuoBiaoProp=a.value,this.X=this.zuoBiaoProp[0],this.Y=this.zuoBiaoProp[1];var n=this.zuoBiaoProp.toString();n==t.EQUIPMENT_LOCATION&&this.map.setCenter([this.zuoBiaoProp[0],this.zuoBiaoProp[1]])}}catch(s){i.e(s)}finally{i.f()}},addEquipment:function(t){var e=this;console.log("this.equipmentID",this.equipmentID),Object(d["y"])({base_id:this.$route.query.base_id,equipmentID:this.equipmentID}).then((function(t){e.shuju=t.data.shuju,e.aa=e.shuju.templist[e.active]}))},renderMap:function(){var t=this;this.$AMapLoader.load({key:"bb41d02b6376f70646e2490b6bf5f80b",version:"1.4.15",plugins:["AMap.MapType"],AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}}).then((function(e){t.map=new e.Map("map-container",{layers:[new e.TileLayer.Satellite,new e.TileLayer.RoadNet],zoom:18,center:t.zuobiaolist[0]}),e.plugin("AMap.MapType",(function(){new e.MapType({defaultType:1})}));var n=t.zuobiaolist,s="pointer";function o(t,e){return r.apply(this,arguments)}function r(){return r=Object(i["a"])(regeneratorRuntime.mark((function t(i,n){var o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return o="",t.next=3,Object(d["m"])({zuobiao:i,base_id:n}).then((function(t){o=t&&"水质检测"==t.data?new e.Icon({size:new e.Size(40,40),cursor:s,image:a("0369"),imageSize:new e.Size(40,40)}):new e.Icon({size:new e.Size(40,40),image:a("4240"),cursor:s,imageSize:new e.Size(40,40)})}));case 3:return t.abrupt("return",o);case 4:case"end":return t.stop()}}),t)}))),r.apply(this,arguments)}var l=t.shelist;t.markers=[],n.forEach(function(){var a=Object(i["a"])(regeneratorRuntime.mark((function a(i,n){var s,r,c;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,o(i,t.base_id);case 2:return s=a.sent,a.next=5,t.getEquipmentType(i,t.base_id);case 5:r=a.sent,c=new e.Marker({position:i,offset:new e.Pixel(-20,-25),icon:s,zIndex:100,label:{content:'<div class="custom-label-'.concat(n,'" style="padding: 10px;margin:0px 0 0 40px">').concat(l[n].EQUIPMENT_NAME,"</div>")}}),t.$nextTick((function(){t.setCustomLabelStyle(c,r,n)})),t.markers.push(c),c.on("click",(function(){t.markers.forEach((function(t){t.setzIndex(100)})),c.setzIndex(1e3),t.box=1,t.map.setCenter([i[0],i[1]]);var e,a=i.toString(),n=Object(Qt["a"])(t.shelist);try{for(n.s();!(e=n.n()).done;)if(t.abc=e.value,a==t.abc.EQUIPMENT_LOCATION){var s="";"水质检测"==t.abc.EQUIPMENT_TYPE?(s="water",t.selectid=1):"气象检测"==t.abc.EQUIPMENT_TYPE&&(s="meteorological",t.selectid=2),t.handleLinkShebei(s,t.abc,0)}}catch(o){n.e(o)}finally{n.f()}})),t.map.add(c);case 11:case"end":return a.stop()}}),a)})));return function(t,e){return a.apply(this,arguments)}}()),t.cameralist&&t.cameralist.length>0&&t.cameralist.forEach((function(i,n){if(i.camera_location){var s=i.camera_location.split(",").map((function(t){return parseFloat(t)})),o=new e.Marker({position:s,offset:new e.Pixel(-20,-25),icon:new e.Icon({size:new e.Size(40,40),image:a("bbe0"),imageSize:new e.Size(40,40)}),label:{content:'<div class="camera-label" style="padding: 10px; margin:0px 0 0 40px">'.concat(i.camera_name,"</div>")},zIndex:100});o.on("click",(function(){t.markers.forEach((function(t){t.setzIndex(100)})),o.setzIndex(1e3),n%2===0?t.initPlayer(i.video_address,i.serial_number):t.initPlayer1(i.video_address,i.serial_number)})),t.markers.push(o),t.map.add(o)}}))}))},getEquipmentType:function(t,e){return Object(i["a"])(regeneratorRuntime.mark((function a(){var i;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return i="水质检测",a.next=3,Object(d["m"])({zuobiao:t,base_id:e}).then((function(t){t&&t.data&&(i=t.data)}));case 3:return a.abrupt("return",i);case 4:case"end":return a.stop()}}),a)})))()},setCustomLabelStyle:function(t,e,a){console.log("labelDom",t,e,a);var i=function(){var t=document.querySelector(".amap-marker-label .custom-label-".concat(a));return!!t&&("水质检测"===e?t.classList.add("water-quality-label"):"气象检测"===e&&t.classList.add("meteorological-label"),!0)};if(!i())var n=0,s=setInterval((function(){(i()||n>=10)&&clearInterval(s),n++}),300)},onCloseCover:function(){this.box=0},initPlayer:function(t,e){e&&this.jktokens.value?this.player=new Nt.a.EZUIKitPlayer({id:"video-container",accessToken:this.jktokens.value,url:t,height:"220",width:"450",template:"e33cecdfc3bb4019a06591a15cda2b1f",autoplay:!0,footer:["fullScreen"],handleError:function(t){console.error("播放器错误:",t)}}):console.error("设备序列号或访问令牌未设置")},initPlayer1:function(t,e){e&&this.jktokens.value?this.player1=new Nt.a.EZUIKitPlayer({id:"video-container1",accessToken:this.jktokens.value,url:t,height:"220",width:"450",template:"e33cecdfc3bb4019a06591a15cda2b1f",autoplay:!0,handleError:function(t){console.error("播放器错误:",t)}}):console.error("设备序列号或访问令牌未设置")},GetCamera:function(){var t=this;this.base_id=this.$route.query.base_id,Object(d["t"])({base_id:this.base_id}).then((function(e){t.cameralist=e.item_list;var a=t.cameralist.map((function(t){return t.video_address})),i=t.cameralist.map((function(t){return t.serial_number}));t.videoAddresses=a,t.SerialNumber=i,console.log("视频地址数组:",e),t.cameralist.length>0&&(t.shebeilist.monitoring=t.cameralist)}))},getToken:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,i,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,a="3d0be5dc16b846e58ba2e4efb80d6d7f",i="1d040ec6b1a4d12061fa97ef21987942",e.next=5,_t.a.post("https://open.ys7.com/api/lapp/token/get","appKey=".concat(a,"&appSecret=").concat(i),{headers:{"Content-Type":"application/x-www-form-urlencoded"}});case 5:n=e.sent,t.jktokens.value=n.data.data.accessToken,console.log("shuju:",n.data.data.accessToken),e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](0),console.error("获取Token失败:",e.t0);case 13:case"end":return e.stop()}}),e,null,[[0,10]])})))()},GetWeather:function(){var t=this;Object(d["v"])({city:this.city}).then((function(e){t.livetianqi={temperature:e.temperature,humidity:e.humidity,wind_direction:e.wind_direction,wind_power:e.wind_power,guangzhao:e.guangzhao,precipitation:e.precipitation,pressure:e.pressure},Object.keys(t.livetianqi).forEach((function(e){Object.prototype.hasOwnProperty.call(t.wetherinfo1,e)&&(t.wetherinfo1[e].value=t.livetianqi[e])})),console.log(t.wetherinfo1)})).catch((function(e){console.error("获取天气数据失败:",e),t.errorMessage="获取天气数据失败，请稍后再试。"}))}}},$t=Zt,te=(a("0bb7"),Object(f["a"])($t,Ot,Pt,!1,null,"1364abbb",null)),ee=te.exports,ae=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100 d-flex flex-column  "},[a("div",{staticClass:"w-100 h-100 map-container position-absolute ",attrs:{id:"map-container"}}),a("img",{staticClass:"w-100 h-100 position-absolute top-0 left-0",staticStyle:{"pointer-events":"none"},attrs:{src:t.bgImage,alt:""}}),1===t.box?a("div",{staticClass:"equip-box"},[a("div",{staticClass:"text"},[a("div",{staticClass:"info-table"},t._l(t.miaoshu,(function(e,i){return a("div",{key:i,class:["info-row",{"full-width":i<3}]},[a("div",{staticClass:"info-label"},[a("span",[t._v(t._s(e.label))])]),a("div",{staticClass:"info-value"},[t._v(t._s(e.value))])])})),0)]),a("div",{staticClass:"close-icon d-flex align-items-center justify-content-center",on:{click:t.onCloseCover}},[a("svg",{staticClass:"icon",attrs:{t:"1639825855503",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"26567",width:"1rem",height:"1rem"}},[a("path",{attrs:{d:"M846.005097 957.24155c-28.587082 0-57.174164-10.911514-78.996169-32.733519L96.632851 254.131955c-43.644009-43.644009-43.**********.348328 0-157.992337s114.348328-43.**********.992337 0L925.001265 766.515694c43.644009 43.644009 43.**********.348328 0 157.992337C903.17926 946.330036 874.592179 957.24155 846.005097 957.24155z","p-id":"26568",fill:"#ffffff"}}),a("path",{attrs:{d:"M175.62902 957.24155c-28.587082 0-57.174164-10.911514-78.996169-32.733519-43.644009-43.644009-43.**********.348328 0-157.992337L767.008928 96.139617c43.644009-43.**********.348328-43.**********.992337 0s43.**********.348328 0 157.992337L254.**********.508032C232.**********.**********.**********.24155 175.62902 957.24155z","p-id":"26569",fill:"#ffffff"}})])])]):t._e(),a("div",{staticClass:"header"},[a("Head2",{attrs:{isShow:!0}})],1),a("div",{staticClass:"main d-flex flex-row  "},[a("div",{staticClass:"home-sidebar  "},[a("div",{staticStyle:{height:"64.8%"}},[a("SubtitledSection",{attrs:{title:"设施列表"}},[[a("DeviceList2",{attrs:{shebeilist:t.shelist},on:{"link-shebei":t.handleLinkShebei}})]],2)],1),a("div",{staticStyle:{height:"32.6%"}},[a("SubtitledSection",{attrs:{title:"控制面板"}},[[a("div",{staticClass:"control-panel"},[a("div",{staticClass:"row img-box row-text",staticStyle:{height:"40%"}},[a("div",{staticClass:"d-flex justify-content-between",staticStyle:{width:"45%"}},[a("span",{staticStyle:{"line-height":"33px",color:"#FFFFFF"}},[t._v("投饲机数量")]),a("div",{staticClass:"row-text1"},[t._v(" 1 ")])])]),a("div",{staticClass:"row img-box row-text",staticStyle:{height:"40%",filter:"hue-rotate(180deg)"}},[a("div",{staticClass:"d-flex justify-content-between",staticStyle:{width:"45%"}},[a("span",{staticStyle:{"line-height":"33px",color:"#FFFFFF"}},[t._v("塘口数量")]),a("div",{staticClass:"row-text1"},[t._v(" 1 ")])])]),a("div",{staticClass:"row split ",staticStyle:{height:"20%","margin-top":"5px",cursor:"pointer"}},[a("div",{staticClass:"half img-box3 row-text",staticStyle:{color:"#EE6E2A"},on:{click:function(e){return t.showAddDialog()}}},[t._v(" 投饲设置 ")]),a("div",{staticClass:"half img-box4 row-text",staticStyle:{color:"#FDEE21"}},[t._v(" 读取参数 ")])])])]],2)],1)]),a("div",{staticClass:"home-center"},[a("div",{staticClass:"info-box"},t._l(t.wetherinfo,(function(e,i){return a("div",{key:i,staticClass:"info-item",style:{backgroundImage:"url("+e.img+")"}},[a("div",{staticStyle:{"margin-left":"3.4rem"}},[t._v(" "+t._s(e.value)+" ")])])})),0),a("div",{staticClass:"bottom-box "},[a("div",{staticStyle:{width:"49.5%"}},[a("SubtitledSectionLong",{attrs:{title:" "}},[[a("div",{staticClass:"video-wrapper"},[a("Line2",{attrs:{Data:t.dailyfeedlist}})],1)]],2)],1),a("div",{staticStyle:{width:"49.5%","margin-left":"0.6rem"}},[a("SubtitledSectionLong",{attrs:{title:" "}},[[a("div",{staticClass:"video-wrapper"},[a("Line2",{attrs:{Data:t.planlist}})],1)]],2)],1)])]),a("div",{staticClass:"home-sidebar "},[a("div",{staticStyle:{height:"51.5%"}},[a("SubtitledSection",{staticStyle:{"pointer-events":"auto"},attrs:{title:t.tianjiarizhi}},[[a("div",{staticClass:"h-100 w-100 log-form-container"},[a("el-form",{staticClass:"log-form",attrs:{model:t.addForm,"label-position":"top"}},[a("el-form-item",{attrs:{label:"时间"}},[a("el-date-picker",{attrs:{"value-format":"yyyy-MM-dd%20HH:mm",placeholder:"选择日期时间"},model:{value:t.addForm.time,callback:function(e){t.$set(t.addForm,"time",e)},expression:"addForm.time"}})],1),a("div",{staticClass:"form-row"},[a("el-form-item",{staticClass:"half-width",attrs:{label:"姓名"}},[a("el-input",{model:{value:t.addForm.name,callback:function(e){t.$set(t.addForm,"name",e)},expression:"addForm.name"}})],1),a("el-form-item",{staticClass:"half-width",attrs:{label:"动作"}},[a("el-select",{attrs:{placeholder:"请选择动作"},model:{value:t.addForm.action,callback:function(e){t.$set(t.addForm,"action",e)},expression:"addForm.action"}},t._l(t.actionOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1),a("div",{staticClass:"form-row"},[a("el-form-item",{staticClass:"half-width",attrs:{label:"种类"}},[a("el-input",{model:{value:t.addForm.zhonglei,callback:function(e){t.$set(t.addForm,"zhonglei",e)},expression:"addForm.zhonglei"}})],1),a("el-form-item",{staticClass:"half-width",attrs:{label:"数量"}},[a("el-input",{model:{value:t.addForm.num,callback:function(e){t.$set(t.addForm,"num",e)},expression:"addForm.num"}})],1)],1),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{rows:2},model:{value:t.addForm.remark,callback:function(e){t.$set(t.addForm,"remark",e)},expression:"addForm.remark"}})],1),a("el-form-item",[a("el-button",{staticClass:"submit-btn",attrs:{type:"primary"},on:{click:t.addLog}},[t._v("提交")])],1)],1)],1)]],2)],1),a("div",{staticClass:"touwei"},[a("div",{staticClass:"touwei-container"},[a("div",{staticClass:"touwei-box"},[a("img",{staticClass:"touwei-img",attrs:{src:t.touwei1Image,alt:""}}),a("div",{staticClass:"touwei-text"},[a("div",{staticClass:"text-top"},[t._v("当天投喂量")]),a("div",{staticClass:"text-bottom"},[t._v(t._s(t.total_quantity)+"kg")])])]),a("div",{staticClass:"divider"}),a("div",{staticClass:"touwei-box"},[a("img",{staticClass:"touwei-img",attrs:{src:t.touwei2Image,alt:""}}),a("div",{staticClass:"touwei-text"},[a("div",{staticClass:"text-top"},[t._v("计划投喂量")]),a("div",{staticClass:"text-bottom"},[t._v(t._s(t.latestDailyFeedAmount)+"kg")])])])])]),a("div",{staticStyle:{height:"32.6%"}},[a("SubtitledSection",{attrs:{title:"日志记录"}},[[a("LogList",{attrs:{baselist:t.loglist}})]],2)],1)])]),a("el-dialog",{attrs:{visible:t.addDialogVisible,modal:!1,title:"投饲设置",width:"500px","append-to-body":!1,"custom-class":"custom-dialog"},on:{"update:visible":function(e){t.addDialogVisible=e}}},[a("el-form",{staticClass:"settings-form",attrs:{model:t.newSettingsForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"塘 口 号：",prop:"pond_number"}},[a("el-input",{attrs:{placeholder:"请输入塘口号"},model:{value:t.newSettingsForm.pond_number,callback:function(e){t.$set(t.newSettingsForm,"pond_number",e)},expression:"newSettingsForm.pond_number"}})],1),a("el-form-item",{attrs:{label:"投饲机号：",prop:"feeder_number"}},[a("el-input",{attrs:{placeholder:"请输入投饲机号"},model:{value:t.newSettingsForm.feeder_number,callback:function(e){t.$set(t.newSettingsForm,"feeder_number",e)},expression:"newSettingsForm.feeder_number"}})],1),a("el-form-item",{attrs:{label:"鱼 数 量：",prop:"fish_quantity"}},[a("el-input",{attrs:{placeholder:"请输入鱼数量"},model:{value:t.newSettingsForm.fish_quantity,callback:function(e){t.$set(t.newSettingsForm,"fish_quantity",e)},expression:"newSettingsForm.fish_quantity"}},[a("template",{staticClass:"unit-box",slot:"append"},[t._v("条")])],2)],1),a("el-form-item",{attrs:{label:"鱼 规 格：",prop:"fish_specification"}},[a("el-input",{attrs:{placeholder:"请输入鱼规格"},model:{value:t.newSettingsForm.fish_specification,callback:function(e){t.$set(t.newSettingsForm,"fish_specification",e)},expression:"newSettingsForm.fish_specification"}},[a("template",{staticClass:"unit-box",slot:"append"},[t._v("g")])],2)],1),a("el-form-item",{attrs:{label:"饲料系数：",prop:"feed_coefficient"}},[a("el-input",{attrs:{placeholder:"请输入饲料系数"},model:{value:t.newSettingsForm.feed_coefficient,callback:function(e){t.$set(t.newSettingsForm,"feed_coefficient",e)},expression:"newSettingsForm.feed_coefficient"}})],1),a("el-form-item",{attrs:{label:"日 饵 率：",prop:"daily_feed_rate"}},[a("el-input",{attrs:{placeholder:"请输入日饵率"},model:{value:t.newSettingsForm.daily_feed_rate,callback:function(e){t.$set(t.newSettingsForm,"daily_feed_rate",e)},expression:"newSettingsForm.daily_feed_rate"}},[a("template",{staticClass:"unit-box",slot:"append"},[t._v("%")])],2)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.addDialogVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.addNewSettings}},[t._v("确 定")])],1)],1)],1)},ie=[],ne=(a("498a"),a("4de4"),a("b680"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"h-100 w-100",attrs:{type:"border-card"}},[a("div",{staticClass:"scrollable-container h-100"},[a("ul",{staticClass:"area-list"},t._l(t.shebeilist,(function(e){return a("li",{key:e.ID,staticClass:"area-item flex-item cursor-pointer",on:{click:function(a){return t.emitLinkShebei(e)}}},[t._m(0,!0),a("div",{staticClass:"flex-content information-title d-flex align-items-center"},[t._v(t._s(e.BACILITIES_NAME)+" "+t._s(e.EQUIPMENT_NAME))]),a("div",{staticClass:"d-flex align-items-center"},[t._v("运行良好")])])})),0)])])}),se=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"d-flex h-100 align-items-center justify-content-center"},[i("img",{attrs:{src:a("e864"),alt:""}})])}],oe={props:{shebeilist:{type:Array}},methods:{emitLinkShebei:function(t){this.$emit("link-shebei",t)}}},re=oe,le=(a("9480"),Object(f["a"])(re,ne,se,!1,null,"0d8e0805",null)),ce=le.exports,de=a("4105"),ue=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"chartContainer",staticStyle:{width:"100%",height:"100%"}})},me=[];a("466d");function fe(t,e){var a="",i=/^#[\da-f]{6}$/i;return i.test(t)&&(a="rgba(".concat(parseInt("0x"+t.slice(1,3)),",").concat(parseInt("0x"+t.slice(3,5)),",").concat(parseInt("0x"+t.slice(5,7)),",").concat(e,")")),a}function he(t,e,a,i){return console.log("mainColor",a),{color:[a],toolbox:{show:!0,feature:{dataZoom:{yAxisIndex:"none",iconStyle:{borderColor:a}},dataView:{readOnly:!1,iconStyle:{borderColor:a}},magicType:{type:["line","bar"],iconStyle:{borderColor:a}},restore:{iconStyle:{borderColor:a}},saveAsImage:{iconStyle:{borderColor:a}}}},tooltip:{trigger:"axis",formatter:function(t){var e='<div style="color: #fff;font-size: 14px;line-height: 24px;padding-bottom: 5px;border-bottom: 1px solid rgba(255,255,255,0.3);margin-bottom: 0px;">\n        '.concat(t[0].axisValue,"\n    </div>");return t.forEach((function(t){e+='<div style="color: #fff;font-size: 14px;line-height: 24px;">\n                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'.concat(t.color,';"></span>\n                    ').concat(t.seriesName,': \n                    <span style="color:#fff;font-weight:700;font-size: 18px">').concat(t.value,"</span>")})),e},extraCssText:"background: rgba(28, 46, 81,0.4);  border: none; border-radius: 4px;",axisPointer:{type:"shadow",shadowStyle:{color:" rgba(28, 46, 81,0.4)",shadowColor:"rgba(225,225,225,1)",shadowBlur:5}}},grid:{top:90,left:20,right:35,bottom:0,containLabel:!0},xAxis:[{type:"category",boundaryGap:!1,axisLabel:{formatter:"{value}",textStyle:{color:"#fff"}},axisLine:{lineStyle:{color:"rgba(44, 81, 153, 1)"}},data:e}],yAxis:[{type:"value",name:i,axisLabel:{textStyle:{color:"#fff"}},nameTextStyle:{color:"#fff",fontSize:12,lineHeight:40},splitLine:{lineStyle:{type:"dashed",color:"rgba(44, 81, 153, 1)"}},axisLine:{show:!1},axisTick:{show:!1}}],series:[{name:"实际值",type:"line",smooth:!0,symbol:"none",symbolSize:8,zlevel:3,lineStyle:{normal:{color:a,shadowBlur:3,shadowColor:fe(a,.5),shadowOffsetY:8}},areaStyle:{normal:{color:new lt["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:fe(a,.2)},{offset:1,color:fe(a,0)}],!1),shadowColor:fe(a,.1),shadowBlur:10}},data:t}]}}var pe={props:{Data:{type:Array,required:!0}},data:function(){return{chartInstance2:null,resizeObserver:null}},watch:{Data:{handler:function(t){if(t&&t.length>0){var e=t.map((function(t){return Object(F["a"])(Object(F["a"])({},t),{},{plan_date:t.plan_date?t.plan_date.replace("T"," "):t.plan_date})}));this.updateChart(e)}},deep:!0,immediate:!0}},mounted:function(){var t=this;if(this.$refs.chartContainer){this.chartInstance2=lt["init"](this.$refs.chartContainer,this.$echartsConfig.theme);var e=he([0,0,0,0,0],["","","","",""],"#51b7de");this.chartInstance2.setOption(e,!0),this.resizeObserver=new ResizeObserver((function(){t.chartInstance2&&t.chartInstance2.resize()})),this.resizeObserver.observe(this.$refs.chartContainer)}},methods:{updateChart:function(t){if(t){var e=[],a=[],i="#51b7de",n="每日计划投喂量(g)";t.forEach((function(t){if(t.daily_feed_amount&&e.push(t.daily_feed_amount),t.plan_date){var s=t.plan_date.match(/^\d{4}-\d{2}-\d{2}/)[0];a.push(s)}console.log("item.id",t.id),t.id&&"1"==t.id?(i="#fad25f",n="每日计划投喂量(g)"):t.id&&"2"==t.id&&(i="#51b7de",n="每日鱼规格(g)")}));var s=he(e,a,i,n);this.chartInstance2&&this.chartInstance2.setOption(s,!0)}else console.warn("数据不完整，无法更新图表")}},beforeDestroy:function(){this.chartInstance2&&this.chartInstance2.dispose(),this.resizeObserver&&this.$refs.chartContainer&&(this.resizeObserver.unobserve(this.$refs.chartContainer),this.resizeObserver.disconnect())}},ge=pe,Ae=Object(f["a"])(ge,ue,me,!1,null,null,null),be=Ae.exports,ve={components:{SubtitledSection:E,SubtitledSectionLong:N,Head2:et,DeviceList2:ce,LogList:de["a"],Line2:be},data:function(){return{recordlist:[],totalFeed:0,planlist:[],dailyfeedlist:[],allData:[],addDialogVisible:!1,bgImage:a("317e"),touwei1Image:a("5ea1"),touwei2Image:a("3967"),newSettingsForm:{pond_number:"",feeder_number:"",fish_quantity:"",fish_specification:"",feed_coefficient:"",daily_feed_rate:"",set_date:""},box:0,isdisabled:!0,tianqi:{},temp:{},rizhishuru:0,tianjiarizhi:"添加日志",map:"",shelist:[],zuobiaolist:[],base_name:"",miaoshu:[],param:"b2-1",equipList:[],actionOptions:[{name:"投饲料",value:"投饲料"},{name:"投药物",value:"投药物"},{name:"捕捞水产品",value:"捕捞水产品"},{name:"投放水产品",value:"投放水产品"}],typeOptions:[{name:"饲料1",value:1},{name:"饲料2",value:2}],addForm:{time:"",name:"",action:"",num:"",remark:"",zhonglei:"",sheshiid:"",base_id:""},addFromName:{sheshiid:"点击左侧设施选择，设施号",time:"时间",name:"操作人姓名",action:"动作",zhonglei:"种类",num:"数量"},addLoading:!1,loglist:[],total_quantity:"",latestDailyFeedAmount:"",errMsg:"",dis:!0,wetherinfo:{wendu:{img:a("4c23"),value:"0"},shidu:{img:a("95b0"),value:"0"},fengxiang:{img:a("5141"),value:"0"},fengsu:{img:a("378c"),value:"0"},guangzhao:{img:a("ca38"),value:"0"},jiangyuliang:{img:a("b6a0"),value:"0"},qiya:{img:a("fdea"),value:"0"}},markers:[]}},computed:{},mounted:function(){this.getEquipList(),this.getlist(),this.GetFeedplan(),this.getfeeder()},methods:{startPolling:function(){this.intervalId=setInterval((function(){}),6e4)},getpeoplefeed:function(){var t=this;Object(d["j"])({base_id:this.$route.query.base_id}).then((function(e){t.total_quantity=e.total_quantity+t.totalFeed,console.log("12344",t.total_quantity)})).catch((function(t){alert("获取失败"+t)}))},addLog:function(){var t=this;this.addLoading=!1,""!=this.addForm.sheshiid?""!=this.addForm.time?""!=this.addForm.name?""!=this.addForm.action?""!=this.addForm.zhonglei?""!=this.addForm.num?(this.addForm.base_id=this.$route.query.base_id,Object(d["k"])(this.addForm).then((function(e){t.addLoading=!1,t.loglist=e.data.loglist,t.alertlist=e.data.alertlist,alert("提交成功")})).catch((function(t){alert("提交失败"+t)})),this.rizhishuru=0):this.$message.warning("请填写数量"):this.$message.warning("请填写种类"):this.$message.warning("请选择动作"):this.$message.warning("请填写操作人姓名"):this.$message.warning("请选择时间"):this.$message.warning("请选择设施")},convertBacilitiesName:function(t){var e=t.trim();return e=e.toLowerCase(),e=e.replace(/塘$/,""),e=e.replace(/-0+(\d+)/,"-$1"),e},handleLinkShebei:function(t){this.param=this.convertBacilitiesName(t.BACILITIES_NAME),this.rizhishuru=1,this.addForm.sheshiid=t.ID,this.tianjiarizhi="添加日志 "+t.BACILITIES_NAME;var e=t.BACILITIES_LOCATION.split(",").map((function(t){return parseFloat(t)}));if(e&&2===e.length){this.map.setCenter(e),this.map.setZoom(18),this.map.panTo(e),this.markers.forEach((function(t){t.setzIndex(100)}));var a=e.toString();this.markers.forEach((function(t){var e=t.getPosition();e&&e.toString()===a&&(t.setzIndex(1e3),!0)}))}this.miaoshu=[{label:"设施名",value:t.BACILITIES_NAME},{label:"设施类型",value:t.BACILITIES_TYPE},{label:"设施描述",value:t.INTRODUCE}],this.box=1,this.GetFeedplan()},addNewSettings:function(){var t={pond_number:this.newSettingsForm.pond_number,feeder_number:this.newSettingsForm.feeder_number,fish_quantity:this.newSettingsForm.fish_quantity,fish_specification:this.newSettingsForm.fish_specification,feed_coefficient:this.newSettingsForm.feed_coefficient,daily_feed_rate:this.newSettingsForm.daily_feed_rate};console.log("New Settings Data:",t),this.addDialogVisible=!1,this.addSettingsToBackend(t)},addSettingsToBackend:function(t){var e=this;Object(d["b"])(t).then((function(t){e.$message({message:"设置添加成功",type:"success",duration:2e3,showClose:!0}),console.log("Settings added successfully")})).catch((function(t){e.$message({message:"设置添加失败："+t.message,type:"error",duration:3e3,showClose:!0}),console.error("An error occurred:",t)}))},GetFeedplan:function(){var t=this;Object(d["f"])().then((function(e){if(e.FishFeedingPlan)if(t.allData=Array.isArray(e.FishFeedingPlan)?e.FishFeedingPlan:[],void 0!==t.param){var a=new Date,i=new Date(a.getFullYear(),a.getMonth(),a.getDate(),0,0,0,0),n=new Date(a.getFullYear(),a.getMonth(),a.getDate()+1,0,0,0,-1),s=new Date,o=new Date;o.setMonth(s.getMonth()-3);var r=t.allData.filter((function(e){var a=new Date(e.plan_date);return e.pond_number.toLowerCase()===t.param.toLowerCase()&&a>=o&&a<=s}));r.length>0?(t.planlist=r.map((function(t){return{plan_date:t.plan_date,daily_feed_amount:t.fish_specification,id:2}})),t.dailyfeedlist=r.map((function(t){return{plan_date:t.plan_date,daily_feed_amount:t.daily_feed_amount,id:1}}))):(t.planlist=[{plan_date:[1],fish_specification:["1"],id:2}],t.dailyfeedlist=[{plan_date:[0],daily_feed_amount:["1"],id:1}]);var l=t.dailyfeedlist.filter((function(t){var e=new Date(t.plan_date);return e>=i&&e<=n})),c=l.length>0?l[l.length-1].daily_feed_amount:0;t.latestDailyFeedAmount=c}else console.error("active 未定义");else console.error("FishFeedingPlan 数据未定义")})).catch((function(t){console.error("获取数据时发生错误:",t)}))},getCurrentTime:function(){var t=new Date;return"".concat(t.getFullYear(),"-").concat(t.getMonth()+1,"-").concat(t.getDate()," ").concat(t.getHours(),":").concat(t.getMinutes(),":").concat(t.getSeconds())},getEquipList:function(){var t=this;this.base_name=this.$route.query.base_name,Object(d["n"])({base_id:this.$route.query.base_id}).then((function(e){t.zuobiaolist=e.data.zuobiaoilist,t.loglist=e.data.loglist,t.tianqi=e.data.tianqi,t.renderMap()}))},getlist:function(){var t=this;this.base_name=this.$route.query.base_name,Object(d["z"])({base_id:this.$route.query.base_id}).then((function(e){var a=e.data.tianqi;Object.keys(a).forEach((function(e){Object.prototype.hasOwnProperty.call(t.wetherinfo,e)&&(t.wetherinfo[e].value=parseFloat(parseFloat(a[e]).toFixed(1)))})),t.shelist=e.data.sheshilist,t.renderMap()}))},showAddDialog:function(){this.addDialogVisible=!0},renderMap:function(){var t=this;this.$AMapLoader.load({key:"bb41d02b6376f70646e2490b6bf5f80b",version:"1.4.15",plugins:[],AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}}).then((function(e){function n(t){return s.apply(this,arguments)}function s(){return s=Object(i["a"])(regeneratorRuntime.mark((function t(i){var n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n="",n=new e.Icon({size:new e.Size(40,40),image:a("609b"),imageSize:new e.Size(40,40)}),t.abrupt("return",n);case 3:case"end":return t.stop()}}),t)}))),s.apply(this,arguments)}t.map=new e.Map("map-container",{mapStyle:"amap://styles/blue",layers:[new e.TileLayer.Satellite,new e.TileLayer.RoadNet],zoom:18,center:t.shelist[0].BACILITIES_LOCATION.split(",")}),t.markers=[],t.shelist.forEach(function(){var a=Object(i["a"])(regeneratorRuntime.mark((function a(i){var s,o;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,n(i);case 2:s=a.sent,o=new e.Marker({position:i.BACILITIES_LOCATION.split(","),offset:new e.Pixel(-20,-25),icon:s,zIndex:100,label:{content:i.BACILITIES_NAME}}),o.on("click",(function(){t.markers.forEach((function(t){t.setzIndex(100)})),o.setzIndex(1e3),t.box=1,t.map.setCenter(i.BACILITIES_LOCATION.split(","));i.BACILITIES_LOCATION;t.miaoshu=[{label:"设施名",value:i.BACILITIES_NAME},{label:"设施类型",value:i.BACILITIES_TYPE},{label:"设施描述",value:i.INTRODUCE}],t.handleLinkShebei(i)})),t.markers.push(o),t.map.add(o);case 7:case"end":return a.stop()}}),a)})));return function(t){return a.apply(this,arguments)}}())}))},getEquipmentType:function(t,e){return Object(i["a"])(regeneratorRuntime.mark((function a(){var i;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return i="水质检测",a.next=3,Object(d["m"])({zuobiao:t,base_id:e}).then((function(t){t&&t.data&&(i=t.data)}));case 3:return a.abrupt("return",i);case 4:case"end":return a.stop()}}),a)})))()},onCloseCover:function(){this.box=0},getfeeder:function(){var t=this,e=new Date,a=new Date(e.getFullYear(),e.getMonth(),e.getDate(),0,0,0,0),i=new Date(e.getFullYear(),e.getMonth(),e.getDate()+1,0,0,0,-1);Object(d["u"])({}).then((function(e){t.recordlist=e.res3,console.log("this.recordlist",t.recordlist);var n=t.recordlist.filter((function(t){var e=new Date(t.update_time);return e>=a&&e<i}));console.log("111111",n);var s=0;0===n.length?(console.log("没有当天记录，总饲料量设置为 0"),s=0):n.forEach((function(t){var e=parseFloat(t.TOUERJI1_FEED)||0,a=parseFloat(t.TOUERJI2_FEED)||0,i=parseFloat(t.TOUERJI3_FEED)||0,n=parseFloat(t.TOUERJI4_FEED)||0;s+=e+a+i+n})),t.totalFeed=s,console.log("总量",s),t.getpeoplefeed()})).catch((function(t){console.error("获取数据失败:",t)}))}}},Ce=ve,we=(a("cc0d"),Object(f["a"])(Ce,ae,ie,!1,null,"74ab5a5a",null)),xe=we.exports,ye=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100 p-4 border-box bg-deep color-white flex-vertical font3 "},[a("div",{staticClass:"flex-content h-50 flex-item"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading1,expression:"loading1"}],staticClass:"flex-content flex-vertical bg-cover mr-4 py-3 px-4 round-md border-box",attrs:{"element-loading-background":"rgba(0, 0, 0, 0.8)"}},[a("div",{staticClass:"flex-label flex-item justify-content-between align-items-center"},[a("div",{staticClass:"flex-label"},[t._v("温度")]),a("div",{staticClass:"flex-content d-flex justify-content-end pl-4"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.date1,callback:function(e){t.date1=e},expression:"date1"}}),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.loading1,expression:"loading1"}],attrs:{icon:"el-icon-search",type:"primary"},on:{click:function(e){t.update_charts(t.date1,"TEM",t.checkOptions1),t.loading1=!0}}},[t._v("查询")])],1)]),a("div",{staticClass:"flex-content flex-item pt-3"},[a("div",{staticClass:"w-30",staticStyle:{width:"30%"}},[a("el-checkbox-group",{attrs:{max:t.max},model:{value:t.checkVal1,callback:function(e){t.checkVal1=e},expression:"checkVal1"}},[t._v("> "),t._l(t.checkOptions,(function(e){return a("el-checkbox",{key:e.id,attrs:{label:e.id,value:e.id}},[t._v(" "+t._s(e.name)+" ")])}))],2)],1),a("div",{staticClass:"flex-content d-flex justify-content-center chart1 overflow-hidden no-scroll"})])]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading2,expression:"loading2"}],staticClass:"flex-content flex-vertical bg-cover mr-4 py-3 px-4 round-md border-box",attrs:{"element-loading-background":"rgba(0, 0, 0, 0.8)"}},[a("div",{staticClass:"flex-label flex-item justify-content-between align-items-center"},[a("div",{staticClass:"flex-label"},[t._v("PH")]),a("div",{staticClass:"flex-content d-flex justify-content-end pl-4"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.date2,callback:function(e){t.date2=e},expression:"date2"}}),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.loading2,expression:"loading2"}],attrs:{icon:"el-icon-search",type:"primary"},on:{click:function(e){t.update_charts(t.date2,"PH",t.checkOptions2),t.loading2=!0}}},[t._v("查询")])],1)]),a("div",{staticClass:"flex-content flex-item pt-3"},[a("div",{staticClass:"w-30",staticStyle:{width:"30%"}},[a("el-checkbox-group",{attrs:{max:t.max},model:{value:t.checkVal2,callback:function(e){t.checkVal2=e},expression:"checkVal2"}},[t._v("> "),t._l(t.checkOptions,(function(e){return a("el-checkbox",{key:e.id,attrs:{label:e.id,value:e.id}},[t._v(" "+t._s(e.name)+" ")])}))],2)],1),a("div",{staticClass:"flex-content d-flex justify-content-center chart2 overflow-hidden no-scroll"})])])]),a("div",{staticClass:"flex-content h-50 flex-item"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading3,expression:"loading3"}],staticClass:"flex-content flex-vertical bg-cover mr-4 py-3 px-4 round-md border-box",attrs:{"element-loading-background":"rgba(0, 0, 0, 0.8)"}},[a("div",{staticClass:"flex-label flex-item justify-content-between align-items-center"},[a("div",{staticClass:"flex-label"},[t._v("溶氧")]),a("div",{staticClass:"flex-content d-flex justify-content-end pl-4"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.date3,callback:function(e){t.date3=e},expression:"date3"}}),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.loading3,expression:"loading3"}],attrs:{icon:"el-icon-search",type:"primary"},on:{click:function(e){t.update_charts(t.date3,"O2",t.checkOptions3),t.loading3=!0}}},[t._v("查询")])],1)]),a("div",{staticClass:"flex-content flex-item pt-3"},[a("div",{staticClass:"w-30",staticStyle:{width:"30%"}},[a("el-checkbox-group",{attrs:{max:t.max},model:{value:t.checkVal3,callback:function(e){t.checkVal3=e},expression:"checkVal3"}},[t._v("> "),t._l(t.checkOptions,(function(e){return a("el-checkbox",{key:e.id,attrs:{label:e.id,value:e.id}},[t._v(" "+t._s(e.name)+" ")])}))],2)],1),a("div",{staticClass:"flex-content d-flex justify-content-center chart3 overflow-hidden no-scroll"})])]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading4,expression:"loading4"}],staticClass:"flex-content flex-vertical bg-cover mr-4 py-3 px-4 round-md border-box",attrs:{"element-loading-background":"rgba(0, 0, 0, 0.8)"}},[a("div",{staticClass:"flex-label flex-item justify-content-between align-items-center"},[a("div",{staticClass:"flex-label"},[t._v("电导")]),a("div",{staticClass:"flex-content d-flex justify-content-end pl-4"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.date4,callback:function(e){t.date4=e},expression:"date4"}}),a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.loading4,expression:"loading4"}],attrs:{icon:"el-icon-search",type:"primary"},on:{click:function(e){t.update_charts(t.date4,"CONDUCTANCE",t.checkOptions4),t.loading4=!0}}},[t._v("查询")])],1)]),a("div",{staticClass:"flex-content flex-item pt-3"},[a("div",{staticClass:"w-30",staticStyle:{width:"30%"}},[a("el-checkbox-group",{attrs:{max:t.max},model:{value:t.checkVal4,callback:function(e){t.checkVal4=e},expression:"checkVal4"}},[t._v("> "),t._l(t.checkOptions,(function(e){return a("el-checkbox",{key:e.id,attrs:{label:e.id,value:e.id}},[t._v(" "+t._s(e.name)+" ")])}))],2)],1),a("div",{staticClass:"flex-content d-flex justify-content-center chart4 overflow-hidden no-scroll"})])])])])},Ee=[],Ie=(a("07ac"),["#28D9E2","#FE9601","#FD5C66","#8c8afd","#f08afd","#fbfd8a","#8afd90"]),De={name:"dataHistory",data:function(){return{loading1:!1,loading2:!1,loading3:!1,loading4:!1,max:5,chartObj:{},checkOptions:[],breed_bases:[],date1:"",showdata1:[],checkVal1:[],checkOptions1:[],selected_base:"",chart1:"",date2:"",showdata2:[],checkVal2:[],checkOptions2:[],chart2:"",date3:"",showdata3:[],checkVal3:[],checkOptions3:[],chart3:"",date4:"",showdata4:[],checkVal4:[],checkOptions4:[],chart4:"",chart_dates1:{},chart_dates2:{},chart_dates3:{},chart_dates4:{},pieData:[]}},mounted:function(){this.getList()},computed:{isCollapse:function(){return this.$parent.isCollapse}},watch:{isCollapse:function(t){setTimeout(this.resize,t?0:200)},selected_base:{handler:function(t){this.updateBaseDataByName(t),this.renderLine("chart1",this.chart_dates1),this.renderLine("chart2",this.chart_dates2),this.renderLine("chart3",this.chart_dates3),this.renderLine("chart4",this.chart_dates4)},immediate:!0},checkVal1:{handler:function(t){this.checkOptions1=this.getOption(t),this.showdata1=this.getshowdata(this.chart_dates1,t)},immediate:!0},checkVal2:{handler:function(t){this.checkOptions2=this.getOption(t),this.showdata2=this.getshowdata(this.chart_dates2,t)},immediate:!0},checkVal3:{handler:function(t){this.checkOptions3=this.getOption(t),this.showdata3=this.getshowdata(this.chart_dates3,t)},immediate:!0},checkVal4:{handler:function(t){this.checkOptions4=this.getOption(t),this.showdata4=this.getshowdata(this.chart_dates4,t)},immediate:!0},showdata1:{handler:function(t){this.renderLine("chart1",t)}},showdata2:{handler:function(t){this.renderLine("chart2",t)}},showdata3:{handler:function(t){this.renderLine("chart3",t)}},showdata4:{handler:function(t){this.renderLine("chart4",t)}}},methods:{getOption:function(t){var e=this,a=[];return t.forEach((function(t){a.push(e.checkOptions.find((function(e){return e.id==t})))})),a},getshowdata:function(t,e){var a=[];return e.forEach((function(e){a.push({dimensions:t.y.find((function(t){return t.id==e})).dimensions,source:t.y.find((function(t){return t.id==e})).source})})),a},update_charts:function(t,e,a){t&&this.changeDate1(t,e,a)},changeDate1:function(t,e,a){var i=this;Object(d["q"])({dates:t,checkval:a,type:e,base_id:this.selected_base}).then((function(t){"TEM"==e?(i.chart_dates1=t.data.TEM,i.loading1=!1,i.showdata1=i.getshowdata(i.chart_dates1,i.checkVal1)):"PH"==e?(i.chart_dates2=t.data.PH,i.showdata2=i.getshowdata(i.chart_dates2,i.checkVal2),i.loading2=!1):"O2"==e?(i.chart_dates3=t.data.O2,i.showdata3=i.getshowdata(i.chart_dates3,i.checkVal3),i.loading3=!1):"CONDUCTANCE"==e&&(i.chart_dates4=t.data.con,i.showdata4=i.getshowdata(i.chart_dates4,i.checkVal4),i.loading4=!1)}))},formatDate:function(t){var e=new Date(t),a=""+(e.getMonth()+1),i=""+e.getDate(),n=e.getFullYear();return a.length<2&&(a="0"+a),i.length<2&&(i="0"+i),[n,a,i].join("-")},getList:function(){this.selected_base=this.$route.query.base_id,this.updateBaseDataByName(this.selected_base)},updateBaseDataByName:function(t){var e=this;Object(d["s"])({base_id:t}).then((function(t){if(t.status){e.checkOptions=t.result.equipments.map((function(t){return{name:t.EQUIPMENT_NAME,id:t.ID}})),e.checkVal1=e.checkVal2=e.checkVal3=e.checkVal4=e.checkVal5=e.checkVal6=[];var a=new Date;e.date1=e.date2=e.date3=e.date4=e.date5=e.date6=[e.formatDate(a),e.formatDate(a)]}})),this.pieData=[{value:1048,name:"设备 A"},{value:735,name:"设备 B"},{value:580,name:"设备 C"},{value:484,name:"设备 D"},{value:300,name:"设备 E"}]},limit_reached:function(){return this.checkOptions.length>5},resize:function(){Object.values(this.chartObj).forEach((function(t){t.resize()}))},renderLine:function(t,e){var a=document.getElementsByClassName(t)[0],i=lt["init"](a),n=[],s={};console.log("data:",e),e.forEach((function(t,e){n.push({smooth:!0,symbol:"circle",symbolSize:2,color:Ie[e],lineStyle:{width:3},datasetIndex:e,connectNulls:!1,type:"scatter"})})),s={tooltip:{trigger:"axis",backgroundColor:"rgba(255, 255, 255, 0.4)",textStyle:{color:"#000"}},grid:{left:"2%",right:0,top:"5%",bottom:"5%",containLabel:!0},dataset:e,xAxis:{type:"time",axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1,lineStyle:{color:"rgba(255, 255, 255, 0.1)"}},axisLabel:{color:"#fff"},minInterval:1200,maxInterval:864e5},yAxis:{type:"value",axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1,lineStyle:{color:"rgba(255, 255, 255, 0.1)"}},axisLabel:{color:"#fff"}},series:n,dataZoom:[{type:"slider",xAxisIndex:0},{type:"inside",xAxisIndex:0,zoomOnMouseWheel:"alt"}]},i.clear(),i.setOption(s),this.chartObj[t]=i},renderBar:function(t,e){var a=this.$echarts.init(document.getElementsByClassName(t)[0]),i=[];e.y.forEach((function(t,e){i.push(Object(F["a"])({type:"bar",barWidth:10,color:Ie[e]},t))}));var n={tooltip:{padding:[10,15],backgroundColor:"rgba(0, 0, 0, 0.4)"},grid:{left:"2%",right:0,top:"5%",bottom:"5%",containLabel:!0},xAxis:{type:"category",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"#fff"},data:e.x},yAxis:{type:"value",axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!0,lineStyle:{color:"rgba(255, 255, 255, 0.1)"}},axisLabel:{color:"#fff"}},series:i};a.setOption(n),this.chartObj[t]=a},renderPie:function(t,e,a,i){var n=this.$echarts.init(document.getElementsByClassName(t)[0]),s=[];a.forEach((function(t,e){s.push(Object(F["a"])({itemStyle:{color:Ie[e]}},t))}));var o={tooltip:{trigger:"item",padding:[10,15],backgroundColor:"rgba(0, 0, 0, 0.4)"},series:[{name:e,type:"pie",radius:i?["20%","60%"]:"60%",itemStyle:{borderWidth:4,borderColor:"#1d2c4e"},data:s}]};n.setOption(o),this.chartObj[t]=n}}},Se=De,_e=(a("941b"),Object(f["a"])(Se,ye,Ee,!1,null,"9d7b68d6",null)),ke=_e.exports,Ne=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100 p-4 border-box bg-deep color-white flex-vertical font3"},[a("div",{staticClass:"d-flex align-items-center justify-content-between bg-cover round-md p-4 mb-4"},[a("el-form",{attrs:{inline:"",model:t.form}},[a("el-from-item",{staticClass:"mb-0"},[a("span",[t._v("养殖场和设备选择")]),a("el-cascader",{attrs:{options:t.option},model:{value:t.form.select,callback:function(e){t.$set(t.form,"select",e)},expression:"form.select"}})],1),a("el-form-item",{staticClass:"mb-0"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.form.date,callback:function(e){t.$set(t.form,"date",e)},expression:"form.date"}})],1)],1),a("div",[a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.loading1,expression:"loading1"}],attrs:{icon:"el-icon-search",type:"primary"},on:{click:t.getList}},[t._v("查询")]),a("el-button",{staticClass:"ml-3",attrs:{icon:"el-icon-download",type:"warning",loading:t.exportLoading},on:{click:t.onExport}},[t._v("导出 ")])],1)],1),a("div",{staticClass:"bg-cover round-md py-4"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading1,expression:"loading1"}],staticClass:"w-100 custom",attrs:{data:t.tableData.slice((t.currentPage-1)*t.pageSize,t.currentPage*t.pageSize),size:"large",height:t.height,"element-loading-background":"rgba(0, 0, 0, 0.8)"}},["2"==t.type?a("el-table-column",{attrs:{prop:"EQUIPMENT_id",label:"设备号",align:"center"}}):t._e(),"2"==t.type?a("el-table-column",{attrs:{prop:"TEM",label:"温度",align:"center"}}):t._e(),"2"==t.type?a("el-table-column",{attrs:{prop:"PH",label:"PH",align:"center"}}):t._e(),"2"==t.type?a("el-table-column",{attrs:{prop:"O2",label:"溶氧",align:"center"}}):t._e(),"2"==t.type?a("el-table-column",{attrs:{prop:"SALT",label:"盐度",align:"center"}}):t._e(),"2"==t.type?a("el-table-column",{attrs:{prop:"VOLTAGE",label:"电压",align:"center"}}):t._e(),"2"==t.type?a("el-table-column",{attrs:{prop:"ORP",label:"ORP",align:"center"}}):t._e(),"2"==t.type?a("el-table-column",{attrs:{prop:"ACQUISITION_TIME",label:"采集时间",align:"center"}}):t._e(),"1"==t.type?a("el-table-column",{attrs:{prop:"EQUIPMENT_id",label:"设备号",align:"center"}}):t._e(),"1"==t.type?a("el-table-column",{attrs:{prop:"TEM",label:"温度",align:"center"}}):t._e(),"1"==t.type?a("el-table-column",{attrs:{prop:"HUMIDITY",label:"湿度",align:"center"}}):t._e(),"1"==t.type?a("el-table-column",{attrs:{prop:"LIGHT",label:"光照",align:"center"}}):t._e(),"1"==t.type?a("el-table-column",{attrs:{prop:"ATM",label:"气压",align:"center"}}):t._e(),"1"==t.type?a("el-table-column",{attrs:{prop:"WIND_SPEED ",label:"风速",align:"center"}}):t._e(),"1"==t.type?a("el-table-column",{attrs:{prop:"WIND_DIRECTION",label:"风向",align:"center"}}):t._e(),"1"==t.type?a("el-table-column",{attrs:{prop:"RAINFALL",label:"雨量",align:"center"}}):t._e(),"1"==t.type?a("el-table-column",{attrs:{prop:"RADIATION",label:"辐射量",align:"center"}}):t._e(),"1"==t.type?a("el-table-column",{attrs:{prop:"ACQUISITION_TIME",label:"采集时间",align:"center"}}):t._e()],1),a("div",{staticClass:"text-right mt-4 pr-4"},[a("el-pagination",{staticClass:"custom",attrs:{background:"","current-page":t.currentPage,"page-sizes":[20,40,80,160],"page-size":t.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:t.total},on:{"size-change":t.onSizeChange,"current-change":t.onCurrentChange}})],1)],1)])},Be=[],Me=a("25ca"),Te={name:"dataReport",data:function(){return{height:0,loading1:!1,exportLoading:!1,form:{date:["",""],select:""},currentPage:1,pageSize:20,total:0,type:0,tableData:[],value:[],option:[]}},mounted:function(){var t=this;this.$nextTick((function(){t.height=document.body.clientHeight-330})),this.get_option()},methods:{get_option:function(){var t=this;Object(d["o"])({}).then((function(e){t.option=e.option}))},onSizeChange:function(t){this.pageSize=t,this.getList()},onCurrentChange:function(t){this.currentPage=t,this.getList()},getList:function(){var t=this;this.loading1=!0;var e=this.option.filter((function(e,a){return e.value==t.form.select[0]}));e=e[0].children.filter((function(e,a){return e.value==t.form.select[1]})),this.e_name=e[0].label,this.tableData=[],""!=this.form.date[0]&&""!=this.form.date[1]?Object(d["r"])({base_id:this.form.select[0],e_id:this.form.select[1],e_name:this.e_name,s_date:this.form.date[0],end_date:this.form.date[1]}).then((function(e){t.tableData=e.data,t.total=t.tableData.length,t.type=e.type,t.loading1=!1})).catch((function(e){t.$message.error("出错了，请检查后重试")})):this.$message.error("请选择查询日期范围。")},onExport:function(){this.exportLoading=!0;var t=Me["a"].json_to_sheet(this.tableData),e=Me["a"].book_new();Me["a"].book_append_sheet(e,t,"kalacloud-data");var a=this.e_name+"_"+this.form.date[0]+"_"+this.form.date[1];Me["b"](e,a+".xlsx"),this.exportLoading=!1}}},Le=Te,Oe=(a("58fb"),Object(f["a"])(Le,Ne,Be,!1,null,"7f64dc18",null)),Pe=Oe.exports,Qe=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100 p-4 border-box bg-deep color-white flex-item font3"},[a("div",{staticClass:"flex-label h-100 py-4 px-5 border-box bg-cover round-md"},[a("el-tree",{staticStyle:{"max-height":"100%","overflow-y":"auto"},attrs:{data:t.three_data,"node-key":"id",props:t.defaultProps,accordion:""},on:{"node-click":t.handleNodeClick}})],1),a("div",{staticClass:"flex-content ml-4 h-100 p-4 border-box bg-cover round-md color-white",staticStyle:{"overflow-y":"auto"}},[a("div",{staticStyle:{margin:"10px 0px"}},[a("el-button",{attrs:{type:"primary",disabled:t.isdisabled},on:{click:function(e){return t.addData()}}},[a("i",{staticClass:"el-icon-circle-plus-outline"}),t._v("新增")])],1),a("el-table",{staticClass:"w-100 custom ",staticStyle:{width:"100%"},attrs:{data:t.tableData,size:"large",height:t.height,"header-cell-style":{textAlign:"center"},"cell-style":{textAlign:"center"},"cell-class-name":t.overflowClassName}},[a("el-table-column",{attrs:{prop:"ID",label:"ID",align:"center"}}),a("el-table-column",{attrs:{prop:"EQUIPMENT_NAME",label:"设备名",align:"center","min-width":"150","class-name":"equipment-name-cell"}}),a("el-table-column",{attrs:{prop:"EQUIPMENT_TYPE",label:"设备类型",align:"center"}}),a("el-table-column",{attrs:{prop:"EQUIPMENT_LOCATION",label:"设备位置",align:"center"}}),a("el-table-column",{attrs:{prop:"STATE",label:"状态",align:"center"}}),a("el-table-column",{attrs:{prop:"MOBILITY",label:"移动性",align:"center"}}),a("el-table-column",{attrs:{prop:"CARD_NUMBER",label:"卡号",align:"center"}}),a("el-table-column",{attrs:{prop:"VOLTAGE",label:"电压",align:"center"}}),a("el-table-column",{attrs:{prop:"CREATE_TIME",label:"创建时间",align:"center"}}),a("el-table-column",{attrs:{prop:"UPDATE_TIME",label:"修改时间",align:"center"}}),a("el-table-column",{attrs:{prop:"CONFIG",label:"配置",align:"center"}}),a("el-table-column",{attrs:{prop:"UANDL",label:"上下限",align:"center"}}),a("el-table-column",{attrs:{prop:"INTRODUCE",label:"简介",align:"center"}}),a("el-table-column",{attrs:{prop:"BACILITIES_id",label:"所属设施",align:"center"}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"220px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"primary",icon:"el-icon-edit"},on:{click:function(a){return t.editData(e.row)}}},[t._v("编辑")]),a("el-button",{attrs:{type:"warning",icon:"el-icon-delete"},on:{click:function(a){return t.DeleteData(e.row)}}},[t._v("删除")])]}}])})],1),a("el-dialog",{attrs:{title:t.title,center:"",visible:t.ChangeData,"before-close":t.outClose,"append-to-body":""},on:{"update:visible":function(e){t.ChangeData=e}}},[a("el-form",{attrs:{model:t.formData,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"设备类型"}},[a("el-select",{attrs:{placeholder:"设备类型"},model:{value:t.formData.EQUIPMENT_TYPE,callback:function(e){t.$set(t.formData,"EQUIPMENT_TYPE",e)},expression:"formData.EQUIPMENT_TYPE"}},[a("el-option",{attrs:{value:"水质检测",label:"水质检测"}}),a("el-option",{attrs:{value:"气象检测",label:"气象检测"}})],1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"设备名"}},[a("el-input",{model:{value:t.formData.EQUIPMENT_NAME,callback:function(e){t.$set(t.formData,"EQUIPMENT_NAME",e)},expression:"formData.EQUIPMENT_NAME"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"设备号"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{disabled:t.canchange},model:{value:t.formData.ID,callback:function(e){t.$set(t.formData,"ID",e)},expression:"formData.ID"}}),t.access?a("el-tooltip",{attrs:{content:"编号可以使用",placement:"bottom"}},[a("i",{staticClass:"el-icon-circle-check",staticStyle:{color:"blue","font-size":"100%"}})]):t._e(),t.exist?a("el-tooltip",{attrs:{content:"编号已存在请重新填写",placement:"bottom"}},[a("i",{staticClass:"el-icon-circle-close",staticStyle:{color:"red","font-size":"100%"}})]):t._e()],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"设备位置"}},[a("el-input",{model:{value:t.formData.EQUIPMENT_LOCATION,callback:function(e){t.$set(t.formData,"EQUIPMENT_LOCATION",e)},expression:"formData.EQUIPMENT_LOCATION"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"状态"}},[a("el-select",{attrs:{placeholder:"设备状态"},model:{value:t.formData.STATE,callback:function(e){t.$set(t.formData,"STATE",e)},expression:"formData.STATE"}},[a("el-option",{attrs:{value:"0",label:"停止"}}),a("el-option",{attrs:{value:"1",label:"正常"}})],1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"移动性"}},[a("el-select",{attrs:{placeholder:"移动性"},model:{value:t.formData.MOBILITY,callback:function(e){t.$set(t.formData,"MOBILITY",e)},expression:"formData.MOBILITY"}},[a("el-option",{attrs:{value:"0",label:"不可移动"}}),a("el-option",{attrs:{value:"1",label:"可移动"}})],1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"卡号"}},[a("el-input",{model:{value:t.formData.CARD_NUMBER,callback:function(e){t.$set(t.formData,"CARD_NUMBER",e)},expression:"formData.CARD_NUMBER"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"电压"}},[a("el-input",{model:{value:t.formData.VOLTAGE,callback:function(e){t.$set(t.formData,"VOLTAGE",e)},expression:"formData.VOLTAGE"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"配置"}},[a("el-input",{model:{value:t.formData.CONFIG,callback:function(e){t.$set(t.formData,"CONFIG",e)},expression:"formData.CONFIG"}})],1)],1)],1),a("el-form-item",{attrs:{label:"简介"}},[a("el-input",{model:{value:t.formData.INTRODUCE,callback:function(e){t.$set(t.formData,"INTRODUCE",e)},expression:"formData.INTRODUCE"}})],1),a("el-form-item",{attrs:{label:"所属设施"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.formData.BACILITIES_id,callback:function(e){t.$set(t.formData,"BACILITIES_id",e)},expression:"formData.BACILITIES_id"}},t._l(t.BACILITIES,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}},[a("span",{staticStyle:{float:"left"}},[t._v(t._s(e.label))]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v(t._s(e.value))])])})),1)],1),a("el-form-item",{attrs:{label:"上下限"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"sub-title"},[t._v("温度")]),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("co（有效值范围）")]),a("el-input-number",{model:{value:t.formData.UANDL1.te.co[0],callback:function(e){t.$set(t.formData.UANDL1.te.co,0,e)},expression:"formData.UANDL1.te.co[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.te.co[1],callback:function(e){t.$set(t.formData.UANDL1.te.co,1,e)},expression:"formData.UANDL1.te.co[1]"}})],1),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("th（报警阈值）")]),a("el-input-number",{model:{value:t.formData.UANDL1.te.th[0],callback:function(e){t.$set(t.formData.UANDL1.te.th,0,e)},expression:"formData.UANDL1.te.th[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.te.th[1],callback:function(e){t.$set(t.formData.UANDL1.te.th,1,e)},expression:"formData.UANDL1.te.th[1]"}})],1)],1),0==t.e_type?a("el-col",{attrs:{span:12}},[a("div",{staticClass:"sub-title"},[t._v("溶氧")]),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("co（有效值范围）")]),a("el-input-number",{model:{value:t.formData.UANDL1.o2.co[0],callback:function(e){t.$set(t.formData.UANDL1.o2.co,0,e)},expression:"formData.UANDL1.o2.co[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.o2.co[1],callback:function(e){t.$set(t.formData.UANDL1.o2.co,1,e)},expression:"formData.UANDL1.o2.co[1]"}})],1),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("th（报警阈值）")]),a("el-input-number",{model:{value:t.formData.UANDL1.o2.th[0],callback:function(e){t.$set(t.formData.UANDL1.o2.th,0,e)},expression:"formData.UANDL1.o2.th[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.o2.th[1],callback:function(e){t.$set(t.formData.UANDL1.o2.th,1,e)},expression:"formData.UANDL1.o2.th[1]"}})],1)],1):t._e(),1==t.e_type?a("el-col",{attrs:{span:12}},[a("div",{staticClass:"sub-title"},[t._v("湿度")]),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("co（有效值范围）")]),a("el-input-number",{model:{value:t.formData.UANDL1.humidity.co[0],callback:function(e){t.$set(t.formData.UANDL1.humidity.co,0,e)},expression:"formData.UANDL1.humidity.co[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.humidity.co[1],callback:function(e){t.$set(t.formData.UANDL1.humidity.co,1,e)},expression:"formData.UANDL1.humidity.co[1]"}})],1),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("th（报警阈值）")]),a("el-input-number",{model:{value:t.formData.UANDL1.humidity.th[0],callback:function(e){t.$set(t.formData.UANDL1.humidity.th,0,e)},expression:"formData.UANDL1.humidity.th[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.humidity.th[1],callback:function(e){t.$set(t.formData.UANDL1.humidity.th,1,e)},expression:"formData.UANDL1.humidity.th[1]"}})],1)],1):t._e()],1),a("el-row",[0==t.e_type?a("el-col",{attrs:{span:12}},[a("div",{staticClass:"sub-title"},[t._v("PH")]),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("co（有效值范围）")]),a("el-input-number",{model:{value:t.formData.UANDL1.ph.co[0],callback:function(e){t.$set(t.formData.UANDL1.ph.co,0,e)},expression:"formData.UANDL1.ph.co[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.ph.co[1],callback:function(e){t.$set(t.formData.UANDL1.ph.co,1,e)},expression:"formData.UANDL1.ph.co[1]"}})],1),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("th（报警阈值）")]),a("el-input-number",{model:{value:t.formData.UANDL1.ph.th[0],callback:function(e){t.$set(t.formData.UANDL1.ph.th,0,e)},expression:"formData.UANDL1.ph.th[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.ph.th[1],callback:function(e){t.$set(t.formData.UANDL1.ph.th,1,e)},expression:"formData.UANDL1.ph.th[1]"}})],1)],1):t._e(),1==t.e_type?a("el-col",{attrs:{span:12}},[a("div",{staticClass:"sub-title"},[t._v("气压")]),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("co（有效值范围）")]),a("el-input-number",{model:{value:t.formData.UANDL1.atm.co[0],callback:function(e){t.$set(t.formData.UANDL1.atm.co,0,e)},expression:"formData.UANDL1.atm.co[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.atm.co[1],callback:function(e){t.$set(t.formData.UANDL1.atm.co,1,e)},expression:"formData.UANDL1.atm.co[1]"}})],1),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("th（报警阈值）")]),a("el-input-number",{model:{value:t.formData.UANDL1.atm.th[0],callback:function(e){t.$set(t.formData.UANDL1.atm.th,0,e)},expression:"formData.UANDL1.atm.th[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.atm.th[1],callback:function(e){t.$set(t.formData.UANDL1.atm.th,1,e)},expression:"formData.UANDL1.atm.th[1]"}})],1)],1):t._e(),0==t.e_type?a("el-col",{attrs:{span:12}},[a("div",{staticClass:"sub-title"},[t._v("盐度")]),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("co（有效值范围）")]),a("el-input-number",{model:{value:t.formData.UANDL1.salt.co[0],callback:function(e){t.$set(t.formData.UANDL1.salt.co,0,e)},expression:"formData.UANDL1.salt.co[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.salt.co[1],callback:function(e){t.$set(t.formData.UANDL1.salt.co,1,e)},expression:"formData.UANDL1.salt.co[1]"}})],1),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("th（报警阈值）")]),a("el-input-number",{model:{value:t.formData.UANDL1.salt.th[0],callback:function(e){t.$set(t.formData.UANDL1.salt.th,0,e)},expression:"formData.UANDL1.salt.th[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.salt.th[1],callback:function(e){t.$set(t.formData.UANDL1.salt.th,1,e)},expression:"formData.UANDL1.salt.th[1]"}})],1)],1):t._e(),1==t.e_type?a("el-col",{attrs:{span:12}},[a("div",{staticClass:"sub-title"},[t._v("雨量")]),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("co（有效值范围）")]),a("el-input-number",{model:{value:t.formData.UANDL1.rainfall.co[0],callback:function(e){t.$set(t.formData.UANDL1.rainfall.co,0,e)},expression:"formData.UANDL1.rainfall.co[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.rainfall.co[1],callback:function(e){t.$set(t.formData.UANDL1.rainfall.co,1,e)},expression:"formData.UANDL1.rainfall.co[1]"}})],1),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("th（报警阈值）")]),a("el-input-number",{model:{value:t.formData.UANDL1.rainfall.th[0],callback:function(e){t.$set(t.formData.UANDL1.rainfall.th,0,e)},expression:"formData.UANDL1.rainfall.th[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.rainfall.th[1],callback:function(e){t.$set(t.formData.UANDL1.rainfall.th,1,e)},expression:"formData.UANDL1.rainfall.th[1]"}})],1)],1):t._e()],1),0==t.e_type?a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"sub-title"},[t._v("电导")]),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("co（有效值范围）")]),a("el-input-number",{model:{value:t.formData.UANDL1.con.co[0],callback:function(e){t.$set(t.formData.UANDL1.con.co,0,e)},expression:"formData.UANDL1.con.co[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.con.co[1],callback:function(e){t.$set(t.formData.UANDL1.con.co,1,e)},expression:"formData.UANDL1.con.co[1]"}})],1),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("th（报警阈值）")]),a("el-input-number",{model:{value:t.formData.UANDL1.con.th[0],callback:function(e){t.$set(t.formData.UANDL1.con.th,0,e)},expression:"formData.UANDL1.con.th[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.con.th[1],callback:function(e){t.$set(t.formData.UANDL1.con.th,1,e)},expression:"formData.UANDL1.con.th[1]"}})],1)],1),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"sub-title"},[t._v("电压")]),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("co（有效值范围）")]),a("el-input-number",{model:{value:t.formData.UANDL1.vol.co[0],callback:function(e){t.$set(t.formData.UANDL1.vol.co,0,e)},expression:"formData.UANDL1.vol.co[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.vol.co[1],callback:function(e){t.$set(t.formData.UANDL1.vol.co,1,e)},expression:"formData.UANDL1.vol.co[1]"}})],1),a("el-col",[a("div",{staticClass:"sub-title"},[t._v("th（报警阈值）")]),a("el-input-number",{model:{value:t.formData.UANDL1.vol.th[0],callback:function(e){t.$set(t.formData.UANDL1.vol.th,0,e)},expression:"formData.UANDL1.vol.th[0]"}}),t._v("~ "),a("el-input-number",{model:{value:t.formData.UANDL1.vol.th[1],callback:function(e){t.$set(t.formData.UANDL1.vol.th,1,e)},expression:"formData.UANDL1.vol.th[1]"}})],1)],1)],1):t._e()],1)],1),a("span",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.saveData()}}},[t._v("保存")]),a("el-button",{on:{click:t.outClose}},[t._v("返回")])],1)],1),a("div",{staticClass:"text-right mt-4 pr-4"},[a("el-pagination",{staticClass:"custom",attrs:{background:"","current-page":t.currentPage,"page-sizes":[20,40,80,160],"page-size":t.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:t.total},on:{"size-change":t.onSizeChange,"current-change":t.onCurrentChange}})],1)],1)])},Ue=[],Fe=(a("e9c4"),a("c740"),a("a434"),{name:"deviceManage",data:function(){return{base_id:"",water_len:0,weather_len:0,BACILITIES:[],isdisabled:!0,BACILITIES_id:"",EQUIPMENT_LOCATION:"",height:0,e_type:0,title:"模态框",ChangeData:!1,formData:{base_id:"",ID:"",EQUIPMENT_NAME:"",EQUIPMENT_TYPE:"",EQUIPMENT_LOCATION:"",STATE:"",MOBILITY:"",CARD_NUMBER:"",VOLTAGE:"",CREATE_TIME:"",UPDATE_TIME:"",CONFIG:"",UANDL1:{te:{co:[0,0],th:[0,0]},o2:{co:[0,0],th:[0,0]},ph:{co:[0,0],th:[0,0]},salt:{co:[0,0],th:[0,0]},con:{co:[0,0],th:[0,0]},humidity:{co:[0,0],th:[0,0]},atm:{co:[0,0],th:[0,0]},rainfall:{co:[0,0],th:[0,0]},vol:{co:[0,0],th:[0,0]}},INTRODUCE:"",BACILITIES_id:""},three_data:[],defaultProps:{children:"children",label:"label"},currentPage:1,pageSize:20,total:0,tableData:[],canchange:!0,access:!1,exist:!1}},mounted:function(){var t=this;this.$nextTick((function(){t.height=document.body.clientHeight-330})),this.get_data(),window.vue=this},created:function(){},watch:{"formData.EQUIPMENT_TYPE":{handler:function(t){t&&"add"===this.formData.type?"气象检测"===t?(this.e_type=1,this.formData.EQUIPMENT_NAME=t+"设备"+(this.weather_len+1)):"水质检测"===t&&(this.e_type=0,this.formData.EQUIPMENT_NAME=t+"设备"+(this.water_len+1)):"气象检测"===t?this.e_type=1:"水质检测"===t&&(this.e_type=0)}},"formData.ID":{handler:function(t){var e=this;t&&"add"==this.formData.type&&Object(d["A"])({ID:t}).then((function(t){200==t.code?(e.access=!0,e.exist=!1):(e.exist=!0,e.access=!1)}))}}},methods:{onSizeChange:function(t){this.pageSize=t,this.getList()},onCurrentChange:function(t){this.currentPage=t,this.getList()},overflowClassName:function(t){t.row,t.column;return"cell-content"},get_children:function(t){var e=this,a=[];return t.children?t.children.forEach((function(t){var i=e.get_children(t);i.forEach((function(t){a.push(t)}))})):a.push(t),a},handleNodeClick:function(t){this.arrs=[];var e=[];"BASE"!=t.type?(this.BACILITIES_id=t.BACILITIES_id,this.EQUIPMENT_LOCATION=t.EQUIPMENT_LOCATION,this.isdisabled=!1):(this.base_id=t.id,this.isdisabled=!0,this.BACILITIES_id="",this.EQUIPMENT_LOCATION="",this.water_len=t.water_len,this.weather_len=t.weather_len),e=this.get_children(t),this.tableData=e},show_message:function(t,e){this.$message({message:t,type:e})},outClose:function(t){this.ChangeData=!1,this.formData={base_id:"",ID:"",EQUIPMENT_NAME:"",EQUIPMENT_TYPE:"",EQUIPMENT_LOCATION:"",STATE:"",MOBILITY:"",CARD_NUMBER:"",VOLTAGE:"",CREATE_TIME:"",UPDATE_TIME:"",CONFIG:"",UANDL1:{te:{co:[0,0],th:[0,0]},o2:{co:[0,0],th:[0,0]},ph:{co:[0,0],th:[0,0]},salt:{co:[0,0],th:[0,0]},con:{co:[0,0],th:[0,0]},humidity:{co:[0,0],th:[0,0]},atm:{co:[0,0],th:[0,0]},rainfall:{co:[0,0],th:[0,0]},vol:{co:[0,0],th:[0,0]}},BACILITIES_id:""},this.exist=!1,this.access=!1},editData:function(t){this.ChangeData=!0,this.title="编辑数据",console.log(t),this.formData=t,this.formData.type="update";try{var e=JSON.parse(this.formData.UANDL);e.vol||(e.vol={co:[0,0],th:[0,0]}),this.formData.UANDL1=e}catch(a){console.error("解析 UANDL 数据出错:",a),this.formData.UANDL1={te:{co:[0,0],th:[0,0]},o2:{co:[0,0],th:[0,0]},ph:{co:[0,0],th:[0,0]},salt:{co:[0,0],th:[0,0]},con:{co:[0,0],th:[0,0]},humidity:{co:[0,0],th:[0,0]},atm:{co:[0,0],th:[0,0]},rainfall:{co:[0,0],th:[0,0]},vol:{co:[0,0],th:[0,0]}}}this.canchange=!0,"气象检测"===this.formData.EQUIPMENT_TYPE?this.e_type=1:"水质检测"===this.formData.EQUIPMENT_TYPE&&(this.e_type=0)},addData:function(){this.ChangeData=!0,this.canchange=!1,this.title="新增数据",this.formData={base_id:"",ID:"",EQUIPMENT_NAME:"",EQUIPMENT_TYPE:"",EQUIPMENT_LOCATION:"",STATE:"",MOBILITY:"",CARD_NUMBER:"",VOLTAGE:"",CREATE_TIME:"",UPDATE_TIME:"",CONFIG:"",UANDL1:{te:{co:[0,0],th:[0,0]},o2:{co:[0,0],th:[0,0]},ph:{co:[0,0],th:[0,0]},salt:{co:[0,0],th:[0,0]},con:{co:[0,0],th:[0,0]},humidity:{co:[0,0],th:[0,0]},atm:{co:[0,0],th:[0,0]},rainfall:{co:[0,0],th:[0,0]},vol:{co:[0,0],th:[0,0]}},INTRODUCE:"",BACILITIES_id:""},this.formData.type="add",this.formData.BACILITIES_id=this.BACILITIES_id,this.formData.EQUIPMENT_LOCATION=this.EQUIPMENT_LOCATION},saveData:function(){var t=this;this.ChangeData=!1,this.canchange=!0,this.exist=!1,this.access=!1;var e=this.formData.UANDL1,a={};a.te=e.te,this.formData.base_id=this.base_id,"气象检测"==this.formData.EQUIPMENT_TYPE&&(a.humidity=e.humidity,a.atm=e.atm,a.rainfall=e.rainfall),"水质检测"==this.formData.EQUIPMENT_TYPE&&(a.ph=e.ph,a.salt=e.salt,a.o2=e.o2,a.con=e.con,a.vol=e.vol),this.formData.UANDL1=JSON.stringify(a),this.formData.UANDL=JSON.stringify(a);var i=this.formData.type;"add"==i&&(i="添加"),"update"==i&&(i="修改"),Object(d["a"])({form:this.formData}).then((function(e){"200"==e.code?(t.show_message(i+"成功","success"),t.ChangeData=!1,"添加"==i&&(t.tableData.push(e.data),"气象检测"==t.formData.EQUIPMENT_TYPE&&(t.weather_len=t.weather_len+1),"水质检测"==t.formData.EQUIPMENT_TYPE&&(t.water_len=t.water_len+1)),t.outClose()):t.show_message(i+"失败"+e.result,"warning")})).catch((function(e){t.show_message(i+"失败请检查填写数据","warning"),t.outClose()}))},DeleteData:function(t){var e=this;this.$confirm("确认删除","提示框").then((function(){var a=e.tableData.findIndex((function(e){if(e.id==t.id)return!0}));Object(d["c"])({ID:t.ID,base_id:e.base_id}).then((function(i){i.status?(e.show_message("删除成功","success"),e.tableData.splice(a,1),"水质检测"==t.EQUIPMENT_TYPE?e.water_len=e.water_len-1:e.weather_len=e.weather_len-1):e.show_message("删除失败","warning")})).catch((function(t){e.show_message("删除失败"+t,"warning")}))})).catch((function(t){e.show_message("取消删除","warning")}))},get_data:function(){var t=this,e=this.$route.query.base_id;console.log("base_id from URL:",e),Object(d["p"])({base_id:e}).then((function(e){t.three_data=e.data,t.BACILITIES=e.BACILITIES})).catch((function(t){console.error("获取数据失败:",t)}))}}}),ze=Fe,je=(a("be38"),Object(f["a"])(ze,Qe,Ue,!1,null,"0d83147c",null)),Re=je.exports,Ye=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100 p-4 border-box bg-deep color-white flex-item font3"},[a("div",{staticClass:"flex-label h-100 py-4 px-5 border-box bg-cover round-md"},[a("el-tree",{attrs:{data:t.camera_data,"node-key":"id",props:t.defaultProps,accordion:""},on:{"node-click":t.handleNodeClick}})],1),a("div",{staticClass:"flex-content ml-4 h-100 p-4 border-box bg-cover round-md color-white"},[t.selectedCamera?[a("div",{staticClass:"camera-info"},[a("div",{attrs:{id:"video-container"}}),a("div",{staticClass:"ptz-controls"},[a("div",{staticClass:"ptz-direction"},[a("el-button",{staticClass:"ptz-btn",on:{click:function(e){return t.ptzControl("up")}},nativeOn:{mousedown:function(e){return t.startPtz("up")},mouseup:function(e){return t.stopPtz("up")},mouseleave:function(e){return t.stopPtz("up")}}},[a("i",{staticClass:"el-icon-top"})]),a("div",{staticClass:"ptz-horizontal"},[a("el-button",{staticClass:"ptz-btn",on:{click:function(e){return t.ptzControl("left")}},nativeOn:{mousedown:function(e){return t.startPtz("left")},mouseup:function(e){return t.stopPtz("left")},mouseleave:function(e){return t.stopPtz("left")}}},[a("i",{staticClass:"el-icon-back"})]),a("el-button",{staticClass:"ptz-btn",on:{click:function(e){return t.ptzControl("right")}},nativeOn:{mousedown:function(e){return t.startPtz("right")},mouseup:function(e){return t.stopPtz("right")},mouseleave:function(e){return t.stopPtz("right")}}},[a("i",{staticClass:"el-icon-right"})])],1),a("el-button",{staticClass:"ptz-btn",on:{click:function(e){return t.ptzControl("down")}},nativeOn:{mousedown:function(e){return t.startPtz("down")},mouseup:function(e){return t.stopPtz("down")},mouseleave:function(e){return t.stopPtz("down")}}},[a("i",{staticClass:"el-icon-bottom"})])],1)])])]:a("div",{staticClass:"empty-tip"},[t._v(" 请从左侧选择摄像头查看详细信息 ")])],2)])},qe=[],We=(a("d9e2"),{name:"deviceCamera",data:function(){return{base_id:"",cameraList:[],loading:!1,camera_data:[],selectedCamera:null,jktokens:{value:null},player:null,defaultProps:{children:"children",label:"label"}}},mounted:function(){this.getCameraData(),this.jktokens.value="your-access-token"},beforeDestroy:function(){this.player&&(this.player.stop(),this.player=null)},methods:{getCameraData:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,t.loading=!0,a=t.$route.query.base_id,a){e.next=6;break}return console.error("缺少基地ID参数"),e.abrupt("return");case 6:return e.next=8,Object(d["t"])({base_id:a});case 8:i=e.sent,i&&i.item_list&&(t.cameraList=i.item_list.map((function(t){return{videoAddress:t.video_address,serialNumber:t.serial_number,state:t.state,name:t.camera_name,location:t.camera_location}})),t.camera_data=t.cameraList.map((function(t){return{id:t.serialNumber,label:t.name,type:"CAMERA",data:t}}))),e.next=15;break;case 12:e.prev=12,e.t0=e["catch"](0),console.error("获取摄像头数据失败:",e.t0);case 15:return e.prev=15,t.loading=!1,e.finish(15);case 18:case"end":return e.stop()}}),e,null,[[0,12,15,18]])})))()},handleNodeClick:function(t){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function a(){var i;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,console.log("Node clicked:",t),t&&"object"===Object(Ut["a"])(t)){a.next=4;break}throw new Error("Invalid node data");case 4:if("CAMERA"!==t.type){a.next=13;break}return e.selectedCamera=t,e.player&&(e.player.stop(),e.player=null),a.next=9,e.getToken();case 9:return a.next=11,e.$nextTick();case 11:i=document.getElementById("video-container"),i?e.initPlayer(t.data.videoAddress,t.data.serialNumber):console.error("video-container元素未找到");case 13:a.next=19;break;case 15:a.prev=15,a.t0=a["catch"](0),console.error("处理节点点击时出错:",a.t0),e.$message.error("无法加载摄像头信息");case 19:case"end":return a.stop()}}),a,null,[[0,15]])})))()},initPlayer:function(t,e){var a=this;return Object(i["a"])(regeneratorRuntime.mark((function i(){var n,s;return regeneratorRuntime.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(e&&a.jktokens.value){i.next=3;break}return console.error("设备序列号或访问令牌未设置"),i.abrupt("return");case 3:if(window.EZUIKit){i.next=6;break}return console.error("EZUIKit未加载"),i.abrupt("return");case 6:if(n=document.getElementById("video-container"),n){i.next=10;break}return console.error("video-container元素未找到"),i.abrupt("return");case 10:if(n.innerHTML="",i.prev=11,n.innerHTML='<div class="loading">加载中...</div>',window.EZUIKit&&window.EZUIKit.EZUIKitPlayer){i.next=15;break}throw new Error("EZUIKit未正确加载");case 15:if(a.jktokens.value){i.next=17;break}throw new Error("访问令牌无效");case 17:if(t&&"string"===typeof t){i.next=19;break}throw new Error("视频地址无效");case 19:return i.next=21,new Promise((function(t){return setTimeout(t,500)}));case 21:if(s=new Nt.a.EZUIKitPlayer({id:"video-container",accessToken:a.jktokens.value,url:t,height:"540",width:"960",template:"e33cecdfc3bb4019a06591a15cda2b1f",autoplay:!0,footer:["fullScreen"],handleError:function(t){console.error("播放器错误:",t),a.$message.error("视频加载失败: "+t.message),n.innerHTML='<div class="error">视频加载失败: '+t.message+"</div>"}}),s&&"object"===Object(Ut["a"])(s)){i.next=24;break}throw new Error("播放器实例创建失败");case 24:return i.next=26,new Promise((function(t){return setTimeout(t,1e3)}));case 26:"function"===typeof s.on?(s.on("play",(function(){console.log("播放器开始播放")})),s.on("error",(function(t){console.error("播放器错误:",t),a.$message.error("播放器错误: "+t.message),n.innerHTML='<div class="error">播放器错误: '+t.message+"</div>"})),s.on("dblclick",(function(){"function"===typeof s.fullScreen&&s.fullScreen()}))):console.warn("播放器实例不支持事件监听"),a.player=s,i.next=34;break;case 30:i.prev=30,i.t0=i["catch"](11),console.error("播放器初始化失败:",i.t0),a.$message.error("播放器初始化失败");case 34:case"end":return i.stop()}}),i,null,[[11,30]])})))()},ptzControl:function(t){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.player&&e.selectedCamera){a.next=3;break}return e.$message.warning("请先选择摄像头"),a.abrupt("return");case 3:return a.prev=3,a.next=6,e.startPtz(t);case 6:setTimeout(Object(i["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,e.stopPtz(t);case 2:case"end":return a.stop()}}),a)}))),500),a.next=13;break;case 9:a.prev=9,a.t0=a["catch"](3),console.error("云台控制失败:",a.t0),e.$message.error("云台控制失败");case 13:case"end":return a.stop()}}),a,null,[[3,9]])})))()},startPtz:function(t){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function a(){var i,n;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.player&&e.selectedCamera){a.next=2;break}return a.abrupt("return");case 2:return i={up:0,down:1,left:2,right:3},n=e.selectedCamera.data.serialNumber,a.next=6,_t.a.post("https://open.ys7.com/api/lapp/device/ptz/start","accessToken=".concat(e.jktokens.value,"&deviceSerial=").concat(n,"&channelNo=1&direction=").concat(i[t],"&speed=1"),{headers:{"Content-Type":"application/x-www-form-urlencoded"}});case 6:case"end":return a.stop()}}),a)})))()},stopPtz:function(t){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.player&&e.selectedCamera){t.next=2;break}return t.abrupt("return");case 2:return a=e.selectedCamera.data.serialNumber,t.next=5,_t.a.post("https://open.ys7.com/api/lapp/device/ptz/stop","accessToken=".concat(e.jktokens.value,"&deviceSerial=").concat(a,"&channelNo=1"),{headers:{"Content-Type":"application/x-www-form-urlencoded"}});case 5:case"end":return t.stop()}}),t)})))()},getToken:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,i,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,a="3d0be5dc16b846e58ba2e4efb80d6d7f",i="1d040ec6b1a4d12061fa97ef21987942",e.next=5,_t.a.post("https://open.ys7.com/api/lapp/token/get","appKey=".concat(a,"&appSecret=").concat(i),{headers:{"Content-Type":"application/x-www-form-urlencoded"}});case 5:n=e.sent,t.jktokens.value=n.data.data.accessToken,console.log("获取Token成功:",n.data.data.accessToken),e.next=14;break;case 10:e.prev=10,e.t0=e["catch"](0),console.error("获取Token失败:",e.t0),t.$message.error("获取访问令牌失败");case 14:case"end":return e.stop()}}),e,null,[[0,10]])})))()}}}),He=We,Ge=(a("eba0"),Object(f["a"])(He,Ye,qe,!1,null,"632f1ff8",null)),Xe=Ge.exports,Je=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100 p-4 border-box bg-deep color-white font3"},[a("div",{staticClass:"form-box border-box bg-cover round-md d-flex justify-content-center"},[a("el-form",{staticClass:"w-60",attrs:{model:t.form,rules:t.rules,"label-width":"auto"}},[a("el-form-item",{attrs:{label:"姓名",prop:"nickname"}},[a("el-input",{attrs:{placeholder:"请输入姓名"},model:{value:t.form.nickname,callback:function(e){t.$set(t.form,"nickname",e)},expression:"form.nickname"}})],1),a("el-form-item",{attrs:{label:"账号",prop:"username"}},[a("el-input",{attrs:{placeholder:"请输入账号"},model:{value:t.form.username,callback:function(e){t.$set(t.form,"username",e)},expression:"form.username"}})],1),a("el-form-item",{attrs:{label:"密码",prop:"password"}},[a("el-input",{attrs:{placeholder:"请输入密码"},model:{value:t.form.password,callback:function(e){t.$set(t.form,"password",e)},expression:"form.password"}})],1),a("el-form-item",[a("div",{staticClass:"text-right"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.add()}}},[t._v("新 增")])],1)])],1)],1)])},Ke=[],Ve={name:"addUser",data:function(){return{form:{nickname:"",username:"",password:""},rules:{nickname:[{required:!0,massage:"请输入姓名",trigger:"blur"}],username:[{required:!0,massage:"请输入登录账号",trigger:"blur"}],password:[{required:!0,massage:"请输入密码",trigger:"blur"}]}}},methods:{add:function(){var t=this;Object(d["l"])(this.form).then((function(e){console.log(e),200==e.code&&(t.$notify({message:"注册成功"}),t.$router.push("/login"))})),this.rizhishuru=0}}},Ze=Ve,$e=(a("5941"),Object(f["a"])(Ze,Je,Ke,!1,null,"3977c6ea",null)),ta=$e.exports,ea=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100 p-4 border-box bg-deep color-white flex-item font3"},[a("div",{staticClass:"flex-content ml-4 h-100 p-4 border-box bg-cover round-md color-white"},[a("div",{staticClass:"top-form",staticStyle:{"margin-bottom":"20px"}},[a("div",{staticStyle:{margin:"10px 0px"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.showAddDialog()}}},[a("i",{staticClass:"el-icon-circle-plus-outline"}),t._v("设置 ")])],1),a("el-table",{staticClass:"w-100 custom",attrs:{size:"large",data:t.SettingsData}},[a("el-table-column",{attrs:{prop:"pond_number",label:"塘口号",align:"center"}}),a("el-table-column",{attrs:{prop:"feeder_number",label:"投饲机号",align:"center"}}),a("el-table-column",{attrs:{prop:"fish_quantity",label:"鱼数量/条",align:"center"}}),a("el-table-column",{attrs:{prop:"fish_specification",label:"鱼规格/g",align:"center"}}),a("el-table-column",{attrs:{prop:"feed_coefficient",label:"饲料系数",align:"center"}}),a("el-table-column",{attrs:{prop:"daily_feed_rate",label:"日饵率",align:"center"}}),a("el-table-column",{attrs:{prop:"set_date",label:"设置日期",align:"center"}}),a("el-table-column",{attrs:{prop:"modify_date",label:"修改日期",align:"center"}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"220px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"primary",icon:"el-icon-edit"},on:{click:function(a){return t.editData(e.row)}}},[t._v("编辑")]),a("el-button",{attrs:{type:"warning",icon:"el-icon-delete"},on:{click:function(a){return t.DeleteData(e.row)}}},[t._v("删除")])]}}])})],1),a("el-dialog",{attrs:{visible:t.editDialogVisible,title:"编辑数据"},on:{"update:visible":function(e){t.editDialogVisible=e}}},[a("el-form",{attrs:{model:t.editForm,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"塘口号"}},[a("el-input",{attrs:{disabled:""},model:{value:t.editForm.pond_number,callback:function(e){t.$set(t.editForm,"pond_number",e)},expression:"editForm.pond_number"}})],1),a("el-form-item",{attrs:{label:"投饲机号"}},[a("el-input",{model:{value:t.editForm.feeder_number,callback:function(e){t.$set(t.editForm,"feeder_number",e)},expression:"editForm.feeder_number"}})],1),a("el-form-item",{attrs:{label:"鱼数量/条"}},[a("el-input",{model:{value:t.editForm.fish_quantity,callback:function(e){t.$set(t.editForm,"fish_quantity",e)},expression:"editForm.fish_quantity"}})],1),a("el-form-item",{attrs:{label:"鱼规格/g"}},[a("el-input",{model:{value:t.editForm.fish_specification,callback:function(e){t.$set(t.editForm,"fish_specification",e)},expression:"editForm.fish_specification"}})],1),a("el-form-item",{attrs:{label:"饲料系数"}},[a("el-input",{model:{value:t.editForm.feed_coefficient,callback:function(e){t.$set(t.editForm,"feed_coefficient",e)},expression:"editForm.feed_coefficient"}})],1),a("el-form-item",{attrs:{label:"日饵率"}},[a("el-input",{model:{value:t.editForm.daily_feed_rate,callback:function(e){t.$set(t.editForm,"daily_feed_rate",e)},expression:"editForm.daily_feed_rate"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.EditSettings()}}},[t._v("保存")]),a("el-button",{on:{click:function(e){t.editDialogVisible=!1}}},[t._v("取消")])],1)],1)],1),a("div",{staticClass:"text-right mt-4 pr-4"},[a("el-pagination",{attrs:{"current-page":t.currentPage,"page-sizes":[10,20,50,100],"page-size":t.pageSize,total:t.total,layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.onSizeChange,"current-change":t.onCurrentChange}})],1)],1),a("div",{staticClass:"bottom-form",staticStyle:{"margin-top":"150px"}},[a("el-select",{attrs:{placeholder:"选择塘口号"},on:{change:function(e){return t.filterByPondNumber()}},model:{value:t.selectedPondNumber,callback:function(e){t.selectedPondNumber=e},expression:"selectedPondNumber"}},t._l(t.pondNumbers,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1),a("el-table",{staticClass:"w-100 custom",attrs:{size:"large",data:t.DisplayData}},[a("el-table-column",{attrs:{prop:"pond_number",label:"塘口号",align:"center"}}),a("el-table-column",{attrs:{prop:"feeder_number",label:"投饲机号",align:"center"}}),a("el-table-column",{attrs:{prop:"plan_date",label:"计划日期",align:"center"}}),a("el-table-column",{attrs:{prop:"daily_feed_amount",label:"每日计划投饲量/g",align:"center"}}),a("el-table-column",{attrs:{prop:"total_feed_amount",label:"计划投喂总量/g",align:"center"}}),a("el-table-column",{attrs:{prop:"total_fish_weight",label:"计划鱼总重/g",align:"center"}}),a("el-table-column",{attrs:{prop:"fish_quantity",label:"鱼数量/条",align:"center"}}),a("el-table-column",{attrs:{prop:"fish_specification",label:"鱼规格/g",align:"center"}}),a("el-table-column",{attrs:{prop:"feed_coefficient",label:"饲料系数",align:"center"}}),a("el-table-column",{attrs:{prop:"daily_feed_rate",label:"日饵率",align:"center"}})],1)],1),t._v("> "),a("el-dialog",{attrs:{visible:t.addDialogVisible,title:"新增设置信息",width:"30%"},on:{"update:visible":function(e){t.addDialogVisible=e}}},[a("el-form",{attrs:{model:t.newSettingsForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"塘口号",prop:"pond_number"}},[a("el-input",{model:{value:t.newSettingsForm.pond_number,callback:function(e){t.$set(t.newSettingsForm,"pond_number",e)},expression:"newSettingsForm.pond_number"}})],1),a("el-form-item",{attrs:{label:"投饲机号",prop:"feeder_number"}},[a("el-input",{model:{value:t.newSettingsForm.feeder_number,callback:function(e){t.$set(t.newSettingsForm,"feeder_number",e)},expression:"newSettingsForm.feeder_number"}})],1),a("el-form-item",{attrs:{label:"鱼数量",prop:"fish_quantity"}},[a("el-input",{model:{value:t.newSettingsForm.fish_quantity,callback:function(e){t.$set(t.newSettingsForm,"fish_quantity",e)},expression:"newSettingsForm.fish_quantity"}})],1),a("el-form-item",{attrs:{label:"鱼规格",prop:"fish_specification"}},[a("el-input",{model:{value:t.newSettingsForm.fish_specification,callback:function(e){t.$set(t.newSettingsForm,"fish_specification",e)},expression:"newSettingsForm.fish_specification"}})],1),a("el-form-item",{attrs:{label:"饲料系数",prop:"feed_coefficient"}},[a("el-input",{model:{value:t.newSettingsForm.feed_coefficient,callback:function(e){t.$set(t.newSettingsForm,"feed_coefficient",e)},expression:"newSettingsForm.feed_coefficient"}})],1),a("el-form-item",{attrs:{label:"日饵率",prop:"daily_feed_rate"}},[a("el-input",{model:{value:t.newSettingsForm.daily_feed_rate,callback:function(e){t.$set(t.newSettingsForm,"daily_feed_rate",e)},expression:"newSettingsForm.daily_feed_rate"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){return t.closeAddDialog()}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addNewSettings()}}},[t._v("确定")])],1)],1),a("div",{staticClass:"text-right mt-4 pr-4"},[a("el-pagination",{staticClass:"custom",attrs:{background:"","current-page":t.currentPage,"page-sizes":[20,40,80,160],"page-size":t.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:t.total},on:{"size-change":t.onSizeChange,"current-change":t.onCurrentChange}})],1)],1)])},aa=[],ia={name:"tousiplan",data:function(){return{addDialogVisible:!1,editDialogVisible:!1,newSettingsForm:{pond_number:"",feeder_number:"",fish_quantity:"",fish_specification:"",feed_coefficient:"",daily_feed_rate:"",set_date:""},editForm:{id:"",pond_number:"",feeder_number:"",fish_quantity:"",fish_specification:"",feed_coefficient:"",daily_feed_rate:"",set_date:""},SettingsData:[],DisplayData:[],allData:[],currentPage:1,pageSize:20,total:0,selectedPondNumber:null,pondNumbers:["b2-1","b2-2","b2-3","b2-4"]}},computed:{selectClass:function(){return this.selectedPondNumber?"custom-select-active":"custom-select"}},mounted:function(){this.loadData()},methods:{loadData:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getSettingsData();case 2:return e.next=4,t.getDailyData();case 4:case"end":return e.stop()}}),e)})))()},getSettingsData:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:Object(d["h"])().then((function(e){e.settings&&(console.log(e.settings),t.SettingsData=e.settings)})).catch((function(t){console.error("An error occurred while fetching settings data:",t)}));case 1:case"end":return e.stop()}}),e)})))()},getDailyData:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:Object(d["f"])().then((function(e){e.FishFeedingPlan&&(console.log(e.FishFeedingPlan),t.allData=e.FishFeedingPlan,t.DisplayData=t.allData)})).catch((function(t){console.error("An error occurred while fetching settings data:",t)}));case 1:case"end":return e.stop()}}),e)})))()},addNewSettings:function(){var t={pond_number:this.newSettingsForm.pond_number,feeder_number:this.newSettingsForm.feeder_number,fish_quantity:this.newSettingsForm.fish_quantity,fish_specification:this.newSettingsForm.fish_specification,feed_coefficient:this.newSettingsForm.feed_coefficient,daily_feed_rate:this.newSettingsForm.daily_feed_rate};console.log("New Settings Data:",t),this.closeAddDialog(),this.addSettingsToBackend(t)},addSettingsToBackend:function(t){var e=this;Object(d["b"])(t).then((function(t){console.log("Settings added successfully"),e.getSettingsData()})).catch((function(t){console.error("An error occurred:",t)}))},filterByPondNumber:function(){var t=this;if(!this.selectedPondNumber)return this.DisplayData=this.allData,console.log("Selected Pond Number:",this.selectedPondNumber),void console.log("All Data:",this.allData);this.DisplayData=this.allData.filter((function(e){return e.pond_number===t.selectedPondNumber}))},showAddDialog:function(){this.addDialogVisible=!0},closeAddDialog:function(){this.addDialogVisible=!1},showEditDialog:function(){this.editDialogVisible=!0},closeEditDialog:function(){this.editDialogVisible=!1},show_message:function(t,e){this.$message({message:t,type:e})},onSizeChange:function(t){this.pageSize=t,this.getList(),console.log("nnnnnnnnnnnnn"),console.log(this.getList)},onCurrentChange:function(t){this.currentPage=t,this.getList()},editData:function(t){var e=t.id;this.editForm.id=e,this.editForm=Object(F["a"])({},t),this.showEditDialog()},EditSettings:function(){var t={id:this.editForm.id,pond_number:this.editForm.pond_number,feeder_number:this.editForm.feeder_number,fish_quantity:this.editForm.fish_quantity,fish_specification:this.editForm.fish_specification,feed_coefficient:this.editForm.feed_coefficient,daily_feed_rate:this.editForm.daily_feed_rate,set_date:this.editForm.set_date};console.log("Edit Pond Settings Data:",t),this.closeEditDialog(),this.editPondSettingsToBackend(t)},editPondSettingsToBackend:function(t){var e=this;Object(d["e"])(t).then((function(t){e.loadData(),e.show_message("塘口信息修改成功","success"),console.log("123")})).catch((function(t){console.error("发生错误:",t),e.show_message("塘口信息修改失败","error")}))},DeleteData:function(t){var e=this;this.$confirm("确认删除","提示框").then((function(){e.SettingsData.findIndex((function(e){if(e.id==t.id)return!0}));Object(d["d"])({ID:t.id}).then((function(t){e.show_message("删除成功","success"),e.getSettingsData()})).catch((function(t){e.show_message("删除失败"+t,"warning")}))})).catch((function(t){e.show_message("取消删除","warning")}))}}},na=ia,sa=(a("dc9d"),Object(f["a"])(na,ea,aa,!1,null,"5a6ae1aa",null)),oa=sa.exports,ra=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"home"},[a("van-nav-bar",{staticClass:"home-nav",attrs:{title:"上海海洋大学智慧养殖平台"}}),a("div",{staticClass:"content"},[a("div",{staticClass:"card"},[a("company-introduction")],1),a("div",{staticClass:"card"},[a("base-list",{attrs:{baselist:t.baseList}})],1)])],1)},la=[],ca=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"company-container"},[a("section-title",{staticClass:"white-title",attrs:{title:"企业介绍"}}),t._m(0)],1)},da=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"company-intro"},[a("p",[t._v("上海城市电力发展有限公司是一家集水产养殖、新能源、农业种植、农业休闲旅游观光的综合性开发公司。")]),a("p",[t._v("公司以发展绿色能源、生态养殖为使命，严格按照绿色、环保、生态的总体定位和产学研科研创新发展战略，现有主要养殖品种有加州鲈鱼、河豚、南美白对虾等，养殖占地面积3390.6亩，亩产效益达15000元以上。")]),a("p",[t._v('近年来，公司致力于实现从低端养殖向高端养殖转型，以智慧农业信息技术、现代农业种养殖技术、农产品深加工技术、农业生物技术、农业生产自动化智能化技术等全力推进MAP战略，建立多方共赢的现代农业服务"生态圈"。')])])}],ua=a("a5ca"),ma={name:"CompanyIntroduction",components:{SectionTitle:ua["a"]}},fa=ma,ha=(a("e73a"),Object(f["a"])(fa,ca,da,!1,null,"86274806",null)),pa=ha.exports,ga=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100"},[a("section-title",{staticClass:"white-title",attrs:{title:"基地列表"}}),a("div",{staticClass:"list-container"},[a("ul",{staticClass:"area-list"},t._l(t.localBaselist,(function(e,i){return a("li",{key:e.uniqueKey,class:["area-item flex-item cursor-pointer",{"odd-item":i%2===0}],on:{click:function(a){return t.linkshebei(e)}}},[t._m(0,!0),a("div",{staticClass:"flex-content information-title d-flex align-items-center"},[t._v(t._s(e.BASE_NAME))]),a("div",{staticClass:"d-flex align-items-center"},[t._v("运行良好")])])})),0)]),a("section-title",{staticClass:"white-title mt-3",attrs:{title:"设备描述"}}),t._m(1)],1)},Aa=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"d-flex h-100 align-items-center justify-content-center"},[i("img",{attrs:{src:a("fbe7"),alt:""}})])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"device-container"},[i("div",{staticClass:"device-row"},[i("div",{staticClass:"device-info"},[i("div",{staticClass:"device-icon"},[i("img",{attrs:{src:a("6a0b"),alt:""}})]),i("div",{staticClass:"device-text"},[i("div",{staticClass:"device-name"},[t._v("投饲机")]),i("div",{staticClass:"device-number"},[i("span",[t._v("6")]),i("span",[t._v("台")])]),i("div",{staticClass:"device-line"})])]),i("div",{staticClass:"device-info"},[i("div",{staticClass:"device-icon"},[i("img",{attrs:{src:a("1aa5"),alt:""}})]),i("div",{staticClass:"device-text"},[i("div",{staticClass:"device-name"},[t._v("增氧机")]),i("div",{staticClass:"device-number"},[i("span",[t._v("6")]),i("span",[t._v("台")])]),i("div",{staticClass:"device-line"})])])]),i("div",{staticClass:"device-row"},[i("div",{staticClass:"device-info"},[i("div",{staticClass:"device-icon"},[i("img",{attrs:{src:a("c8bc"),alt:""}})]),i("div",{staticClass:"device-text"},[i("div",{staticClass:"device-name"},[t._v("水质监测")]),i("div",{staticClass:"device-number"},[i("span",[t._v("8")]),i("span",[t._v("套")])]),i("div",{staticClass:"device-line"})])]),i("div",{staticClass:"device-info"},[i("div",{staticClass:"device-icon"},[i("img",{attrs:{src:a("6a0b"),alt:""}})]),i("div",{staticClass:"device-text"},[i("div",{staticClass:"device-name"},[t._v("气象监测站")]),i("div",{staticClass:"device-number"},[i("span",[t._v("8")]),i("span",[t._v("套")])]),i("div",{staticClass:"device-line"})])])])])}],ba={name:"BaseList",components:{SectionTitle:ua["a"]},props:{baselist:{type:Array,required:!0}},data:function(){return{localBaselist:[],shebeilist:{water:[],meteorological:[],monitoring:[]},formattedData:[{label:"设备名称",value:""},{label:"设备描述",value:""},{label:"运行状态",value:""},{label:"电压",value:""},{label:"光照",value:""},{label:"风速",value:""},{label:"风向",value:""},{label:"辐射",value:""},{label:"ORP",value:""},{label:"CON",value:""}],miaoshu:{EQUIPMENT_NAME:"",INTRODUCE:"",Online_Voltage:"",state:"",LIGHT:"",WIND_SPEED:"",WIND_DIRECTION:"",RADIATION:"",ORP:"",CON:"",type:1},lineData:[{name:"温度",data:[],xAxisData:[],data1:[],data2:[]},{name:"溶氧值",data:[],xAxisData:[],data1:[],data2:[]},{name:"PH",data:[],xAxisData:[],data1:[],data2:[]},{name:"雨量",data:[],xAxisData:[],data1:[],data2:[]}],computed:{backgroundImage:function(){return"水质检测"===this.miaoshu.EQUIPMENT_TYPE?a("f928"):a("bacf")},labelcolor:function(){return"水质检测"==this.miaoshu.EQUIPMENT_TYPE?"#5CA0B1":"#99BB6D"},valuecolor:function(){return"水质检测"==this.miaoshu.EQUIPMENT_TYPE?"#c6e1e7":"#e9f6d8"},miaoshuFormatted:function(){if("水质检测"==this.miaoshu.EQUIPMENT_TYPE){var t=[{label:"设备名称",value:this.miaoshu.EQUIPMENT_NAME},{label:"采集时间",value:this.miaoshu.ACQUISITION_TIME},{label:"设备描述",value:this.miaoshu.INTRODUCE},{label:"电压",value:this.miaoshu.VOLTAGE},{label:"状态",value:this.miaoshu.state},{label:"ORP",value:this.miaoshu.ORP},{label:"电导",value:this.miaoshu.CON},{label:"温度",value:this.miaoshu.TEM},{label:"PH",value:this.miaoshu.PH},{label:"含氧量",value:this.miaoshu.O2},{label:"盐度",value:this.miaoshu.SALT}];return t}if("气象检测"==this.miaoshu.EQUIPMENT_TYPE){var e=[{label:"设备名称",value:this.miaoshu.EQUIPMENT_NAME},{label:"采集时间",value:this.miaoshu.ACQUISITION_TIME},{label:"设备描述",value:this.miaoshu.INTRODUCE},{label:"电压",value:this.miaoshu.VOLTAGE},{label:"状态",value:this.miaoshu.state},{label:"光照",value:this.miaoshu.LIGHT},{label:"风速",value:this.miaoshu.WIND_SPEED},{label:"风向",value:this.miaoshu.WIND_DIRECTION},{label:"辐射",value:this.miaoshu.RADIATION},{label:"温度",value:this.miaoshu.TEM},{label:"湿度",value:this.miaoshu.HUMIDITY}];return e}return[]}},mounted:function(){this.getlist()},methods:{startPolling:function(){this.intervalId=setInterval((function(){}),6e4)},addAlarm:function(t,e,a,i){this.alertYuanList.push({message:t,value:e,type:a,time:i})},getCurrentTime:function(){var t=new Date;return"".concat(t.getFullYear(),"-").concat(t.getMonth()+1,"-").concat(t.getDate()," ").concat(t.getHours(),":").concat(t.getMinutes(),":").concat(t.getSeconds())},handleLinkShebei:function(t,e,a){if("water"==t?this.selectid=1:"meteorological"==t?this.selectid=2:"monitoring"==t&&(this.selectid=3),3!=this.selectid){e.UPDATE_TIME&&(this.currentTime=e.UPDATE_TIME.replace("T"," ")),this.selectEquipment(e,1);var i=this.zuobiaolist.find((function(t){return t.toString()===e.EQUIPMENT_LOCATION}));i&&(this.map.setCenter(i),this.map.setZoom(18),this.map.panTo(i)),this.miaoshu=e,this.miaoshu.state=0==e.STATE?"停止":"正常",this.miaoshu.type="water"==t?1:2,this.getshebeidata(e)}else a%2==0?this.initPlayer(e.video_address,e.serial_number):this.initPlayer1(e.video_address,e.serial_number);if(3!=this.selectid);else if(a%2==0?this.initPlayer(e.video_address,e.serial_number):this.initPlayer1(e.video_address,e.serial_number),e.camera_location){var n=e.camera_location.split(",");n&&(this.map.setCenter(n),this.map.setZoom(18),this.map.panTo(n))}},getshebeidata:function(t){var e=this;Object(d["y"])({base_id:this.$route.query.base_id,equipmentID:t.ID}).then((function(t){var a=t.data.shuju;e.lineData=[],e.lineData=[{name:"温度",id:1,data:a.templist[0].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[0].y[1].data,data2:a.templist[0].y[2].data,max:a.templist[0].max,min:a.templist[0].min}],1==e.selectid?e.lineData.push({name:"溶氧值",id:2,data:a.templist[1].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[1].y[1].data,data2:a.templist[1].y[2].data,max:a.templist[1].max,min:a.templist[1].min},{name:"PH",id:3,data:a.templist[2].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[2].y[1].data,data2:a.templist[2].y[2].data,max:a.templist[2].max,min:a.templist[2].min}):2==e.selectid&&e.lineData.push({name:"溶氧值",id:2,data:a.templist[3].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[3].y[1].data,data2:a.templist[3].y[2].data,max:a.templist[3].max,min:a.templist[3].min},{name:"PH",id:3,data:a.templist[4].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[4].y[1].data,data2:a.templist[4].y[2].data,max:a.templist[4].max,min:a.templist[4].min},{name:"雨量",id:4,data:a.templist[5].y[0].data,xAxisData:a.templist[0].x,data1:a.templist[5].y[1].data,data2:a.templist[5].y[2].data,max:a.templist[5].max,min:a.templist[5].min})}))},getlist:function(){var t=this;this.base_id=this.$route.query.base_id,Object(d["z"])({base_id:this.base_id}).then((function(e){console.log("数据接口:",e),t.getshebeidata(e.data.shebeilist[0]);var a=e.data.tianqi;Object.keys(a).forEach((function(e){Object.prototype.hasOwnProperty.call(t.wetherinfo,e)&&(t.wetherinfo[e].value=parseFloat(parseFloat(a[e]).toFixed(1)))})),t.shelist=e.data.shebeilist,t.shelist.map((function(e){e.UANDL=JSON.parse(e.UANDL),"水质检测"==e.EQUIPMENT_TYPE?t.shebeilist.water.push(e):"气象检测"==e.EQUIPMENT_TYPE?t.shebeilist.meteorological.push(e):"监控检测"==e.EQUIPMENT_TYPE&&t.shebeilist.monitoring.push(e)}));for(var i=1;i<t.shelist.length;i++){var n=t.shelist[i],s=n.Online_Voltage;if("水质检测"===n.EQUIPMENT_TYPE){var o=n.O2,r=n.TEM,l=n.PH,c=n.UANDL,d=n.UPDATE_TIME.replace("T"," "),u=c;if("string"===typeof c)try{u=JSON.parse(c)}catch(C){console.error("解析 UANDL 失败:",C)}else if("object"!==Object(Ut["a"])(c)||null===c)return void console.error("UANDL 不是有效的对象或 JSON 字符串");if(o<u.o2.th[0]||o>u.o2.th[1]){var m="".concat(n.EQUIPMENT_NAME,"的溶氧值异常"),f=o;t.addAlarm(m,f,1,d)}if(r<u.te.th[0]||r>u.te.th[1]){var h="".concat(n.EQUIPMENT_NAME,"的温度异常"),p=r;t.addAlarm(h,p,1,d)}if(l<u.ph.th[0]||l>u.ph.th[1]){var g="".concat(n.EQUIPMENT_NAME,"的PH值异常"),A=l;t.addAlarm(g,A,1,d)}if(0==s||s<10){var b="".concat(n.EQUIPMENT_NAME,"的电压值异常"),v=s;t.addAlarm(b,v,2,d)}}}t.zuobiaolist=e.data.zuobiaoilist,t.inforlist=e.data.inforlist,t.alertlist=e.data.alertlist,t.tianqi=e.data.tianqi,t.chuangan2=e.data.chuangan2,t.chuangan=e.data.chuangan,t.shuju2=e.data.shuju2,t.aa=t.shuju2.templist[0],t.renderMap(),t.handleLinkShebei("water",{ID:1}),t.selectEquipment(t.shelist[0],1)}))},getEquipmentType:function(t,e){return Object(i["a"])(regeneratorRuntime.mark((function a(){var i;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return i="水质检测",a.next=3,Object(d["m"])({zuobiao:t,base_id:e}).then((function(t){t&&t.data&&(i=t.data)}));case 3:return a.abrupt("return",i);case 4:case"end":return a.stop()}}),a)})))()},onClickLeft:function(){this.$router.back()},emitLinkShebei:function(t,e,a){this.$emit("link-shebei",t,e,a)},linkshebei:function(t){sessionStorage.setItem("base_id",t.ID),console.log("已将基地ID存储到sessionStorage:",t.ID),this.$router.push({path:"/detail",query:{base_id:t.ID,base_name:t.BASE_NAME}})}}}},watch:{baselist:{immediate:!0,handler:function(t){this.localBaselist=t.map((function(t,e){return Object(F["a"])(Object(F["a"])({},t),{},{uniqueKey:"".concat(t.ID,"-").concat(e)})}))}}},methods:{linkshebei:function(t){sessionStorage.setItem("base_id",t.ID),console.log("已将基地ID存储到sessionStorage:",t.ID),this.$router.push({path:"/detail",query:{base_id:t.ID,base_name:t.BASE_NAME}})}}},va=ba,Ca=(a("ad90"),Object(f["a"])(va,ga,Aa,!1,null,"4a27b3d8",null)),wa=Ca.exports,xa={name:"Home",components:{CompanyIntroduction:pa,BaseList:wa},data:function(){return{baseList:[],wetherinfo:{wendu:{img:"top-info-wd.png",value:"0"},shidu:{img:"top-info-xy.png",value:"0"},fengxiang:{img:"top-info-ql.png",value:"0"},fengsu:{img:"top-info-fs.png",value:"0"},guangzhao:{img:"top-info-yg.png",value:"0"},jiangyuliang:{img:"top-info-sd.png",value:"0"},qiya:{img:"top-info-qy.png",value:"0"}},alertlist:{tem:0,ph:0,o2:0}}},methods:{startPolling:function(){var t=this;this.intervalId=setInterval((function(){t.getlist()}),6e4)},getlist:function(){var t=this;Object(d["w"])({}).then((function(e){t.baseList=e.data.baselist,console.log("object",t.baseList);var a=e.data.tianqi;if(Object.keys(a).forEach((function(e){Object.prototype.hasOwnProperty.call(t.wetherinfo,e)&&(t.wetherinfo[e].value=parseFloat(parseFloat(a[e]).toFixed(1)))})),e.data.alertlist.length>0){var i=e.data.alertlist[0],n=i.describe,s=/TEM:(\d+\.\d+) PH:(\d+\.\d+) O2:(\d+\.\d+)/,o=n.match(s);o?(t.alertlist.tem=parseFloat(parseFloat(o[1]).toFixed(1)),t.alertlist.ph=parseFloat(parseFloat(o[2]).toFixed(1)),t.alertlist.o2=parseFloat(parseFloat(o[3]).toFixed(1))):console.error("无法解析 describe 字符串")}else console.error("alertlist 为空")}))}},mounted:function(){this.getlist(),this.startPolling()},beforeDestroy:function(){this.intervalId&&clearInterval(this.intervalId)}},ya=xa,Ea=(a("c588"),Object(f["a"])(ya,ra,la,!1,null,"7ce65c3a",null)),Ia=Ea.exports,Da=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"login-page w-100 h-100 position-relative"},[t._m(0),a("div",{staticClass:"login-box position-absolute flex-item"},[a("div",{staticClass:"flex-label"}),a("div",{staticClass:"flex-content d-flex flex-column justify-content-center"},[a("h2",{staticClass:"mb-4 color-black text-max"},[t._v("Welcome")]),a("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules}},[a("el-form-item",{attrs:{prop:"account"}},[a("el-input",{attrs:{placeholder:"请输入账号"},model:{value:t.form.username,callback:function(e){t.$set(t.form,"username",e)},expression:"form.username"}},[a("svg",{attrs:{slot:"prefix",t:"*************",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"2537",width:"1.3rem",height:"1.3rem"},slot:"prefix"},[a("path",{attrs:{d:"M954.181818 953.746618 512 953.746618 69.818182 953.746618 69.818182 932.126255C78.405818 911.646255 95.604364 892.422982 121.018182 874.945164 146.455273 857.444073 179.618909 841.897891 219.601455 828.772073 286.580364 806.802618 354.141091 798.075345 373.224727 795.934255 395.008 793.490618 414.510545 781.761164 426.612364 763.934255L434.385455 752.297891 437.341091 739.474618C445.789091 702.680436 425.239273 666.002618 412.229818 647.430982 408.040727 641.449891 402.990545 636.120436 397.288727 631.605527 364.776727 605.958982 336.546909 568.280436 315.624727 522.665891 304.872727 499.206982 283.205818 483.125527 258.490182 479.797527 254.557091 478.470982 241.245091 466.718255 235.403636 442.468073 228.794182 415.076073 235.938909 397.412073 238.545455 394.8288 261.213091 381.074618 275.013818 356.056436 274.548364 329.502255 273.128727 245.976436 293.050182 181.301527 333.730909 137.246255 385.186909 81.508073 459.077818 69.825164 512 69.825164 564.922182 69.825164 638.813091 81.508073 690.292364 137.246255 730.973091 181.301527 750.871273 245.976436 749.451636 329.502255 748.986182 356.056436 762.786909 381.074618 784.849455 394.409891 788.061091 397.412073 795.205818 415.076073 788.596364 442.491345 782.754909 466.718255 769.442909 478.470982 766.301091 479.634618 740.770909 483.125527 719.127273 499.206982 708.398545 522.642618 687.453091 568.280436 659.223273 605.958982 626.757818 631.582255 621.009455 636.120436 615.959273 641.449891 611.793455 647.430982 598.760727 666.002618 578.210909 702.680436 586.705455 739.614255L589.986909 753.019345 597.294545 763.771345C609.489455 781.761164 628.992 793.490618 650.752 795.934255 669.858909 798.075345 737.419636 806.802618 804.398545 828.772073 844.381091 841.897891 877.544727 857.444073 902.981818 874.945164 928.628364 892.585891 945.92 911.995345 954.181818 931.986618L954.181818 953.746618ZM1021.975273 913.973527 1021.882182 913.7408C1009.058909 877.225891 982.365091 844.830255 942.568727 817.414982 911.639273 796.143709 872.471273 777.641891 826.181818 762.4448 754.269091 738.846255 683.589818 729.374255 658.571636 726.558255 657.175273 726.395345 655.848727 725.720436 655.057455 724.580073 654.894545 724.324073 654.778182 724.068073 654.708364 723.835345 652.962909 716.225164 660.596364 699.4688 668.974545 687.483345 669.253818 687.064436 669.602909 686.715345 669.998545 686.4128 711.214545 653.854255 746.286545 607.448436 771.863273 551.7568 772.584727 550.174255 774.050909 549.033891 775.773091 548.801164 811.613091 543.890618 844.730182 507.561891 856.482909 458.8288 869.352727 405.441164 854.760727 355.241891 821.666909 335.157527 820.130909 334.203345 819.223273 332.504436 819.246545 330.689164 820.992 228.591709 794.88 147.579345 741.585455 89.886255 672.977455 15.599709 578.862545 0.006982 512 0.006982 445.137455 0.006982 351.022545 15.599709 282.414545 89.886255 229.12 147.579345 203.008 228.591709 204.753455 330.689164 204.776727 332.504436 203.869091 334.203345 202.333091 335.157527 169.239273 355.241891 154.647273 405.441164 167.517091 458.8288 179.269818 507.561891 212.386909 543.890618 248.250182 548.801164 249.949091 549.033891 251.415273 550.174255 252.136727 551.7568 277.713455 607.448436 312.785455 653.854255 354.001455 686.4128 354.397091 686.715345 354.746182 687.064436 355.025455 687.483345 363.403636 699.4688 371.037091 716.225164 369.291636 723.835345 369.221818 724.068073 369.105455 724.324073 368.942545 724.580073 368.151273 725.720436 366.824727 726.395345 365.451636 726.558255 340.410182 729.374255 269.730909 738.846255 197.818182 762.4448 151.528727 777.641891 112.360727 796.143709 81.454545 817.414982 41.634909 844.830255 14.941091 877.225891 2.117818 913.7408L2.024727 913.973527C0.674909 918.022982 0 922.142255 0 926.261527L0 980.370618C0 1004.225164 19.339636 1023.5648 43.194182 1023.5648L512 1023.5648 980.805818 1023.5648C1004.660364 1023.5648 1024 1004.225164 1024 980.370618L1024 926.261527C1024 922.142255 1023.325091 918.022982 1021.975273 913.973527L1021.975273 913.973527Z","p-id":"2538",fill:"#cdcdcd"}})])])],1),a("el-form-item",{attrs:{prop:"password"}},[a("el-input",{attrs:{placeholder:"请输入密码",type:"password"},nativeOn:{keydown:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.comlogin()}},model:{value:t.form.password,callback:function(e){t.$set(t.form,"password",e)},expression:"form.password"}},[a("svg",{attrs:{slot:"prefix",t:"1634354595512",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"3461",width:"1.5rem",height:"1.5rem"},slot:"prefix"},[a("path",{attrs:{d:"M511.970836 499.271094c-67.470671 0-124.724653 53.072753-130.051985 119.469975-0.358157 1.096984-0.775666 2.648316-0.775666 4.466732l0 31.441083c0 7.708566 6.224772 13.96199 13.876032 13.96199l31.203676 0c7.65126 0 13.931291-6.252401 13.931291-13.96199l-0.116657-9.052167c-0.535189-2.594081-0.35918-5.598508-0.11768-8.724709 0.11768-1.935072 0.234337-3.840468 0.234337-5.808286 0-39.924291 32.185027-72.467475 71.816652-72.467475 39.627533 0 71.870888 32.544207 71.870888 72.467475 0 31.771611-20.661575 59.697637-50.166563 69.136613l0-45.552475c0-7.68503-6.251378-13.96506-13.902638-13.96506l-31.174 0c-7.65433 0-13.906731 6.281053-13.906731 13.96506l0 94.259804c0 7.68503 6.252401 13.96506 13.906731 13.96506l31.174 0c1.279133 0 2.52859-0.208754 3.839445-0.596588 66.899666-5.985318 119.213126-63.475683 119.241778-131.214461C642.853745 558.38647 584.141552 499.271094 511.970836 499.271094","p-id":"3462",fill:"#cdcdcd"}}),a("path",{attrs:{d:"M806.553061 336.037383l-76.131935-0.090051 0-55.245232c0-104.908328-83.48439-190.268438-186.147584-190.268438L508.27977 90.433662c-101.113909 0-184.302563 83.84357-185.969529 187.18624l-0.177032 1.533936c0 1.057075 0.118704 1.696642 0 1.548262l0.357134 6.104021 0.952698 0c3.393283 12.400425 14.736633 21.497617 27.868721 21.497617 13.219069 0 24.473391-9.097192 27.836999-21.497617l0.983397 0 0.23843-5.819542c3.333932-74.213235 59.962673-132.331911 128.97956-132.331911l33.853016 0c71.311139 0 129.308041 62.390979 129.308041 139.030473l0 48.352241L375.188566 336.037383l0.089028-0.090051-157.800979 0.090051c-48.680722 0-88.307232 40.46255-88.307232 90.124624l0 417.338037c0 49.665143 39.627533 90.066295 88.307232 90.066295L806.553061 933.566338c48.708352 0 88.277556-40.400129 88.277556-90.066295L894.830617 426.162006C894.829594 376.499933 855.260389 336.037383 806.553061 336.037383M839.364351 430.584736l0 408.480298c0 21.019733-16.645099 38.081317-37.128619 38.081317L221.764268 877.146351c-20.484543 0-37.157271-17.061584-37.157271-38.081317L184.606996 430.584736c0-20.976754 16.672728-38.081317 37.157271-38.081317l580.471464 0C822.719252 392.503419 839.364351 409.609005 839.364351 430.584736","p-id":"3463",fill:"#cdcdcd"}})])])],1),a("el-form-item",{staticClass:"form-button"},[a("el-button",{attrs:{round:""},on:{click:function(e){return t.comlogin()}}},[t._v("LOGIN")])],1)],1)],1)])])},Sa=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"bg-box w-100 h-100 position-absolute d-flex"},[a("div",{staticClass:"left position-relative"}),a("div",{staticClass:"right"})])}],_a=a("d399"),ka={name:"Login",data:function(){return{form:{username:"",password:""},rules:{username:[{required:!0,message:"账号不能为空",blur:!0}],password:[{required:!0,message:"密码不能为空",blur:!0}]}}},methods:{comlogin:function(){var t=this;this.$refs.form.validate((function(e){e&&Object(d["x"])({username:t.form.username,password:t.form.password}).then((function(e){200==e.code?(t.$store.commit("set_token",e.data.token),localStorage.setItem("token",e.data.token),localStorage.setItem("userInfo",JSON.stringify({username:t.form.username})),t.$router.push("/home")):Object(_a["a"])({message:e.message||"账号或密码错误",position:"middle",duration:2e3,className:"custom-toast"})})).catch((function(t){Object(_a["a"])({message:"网络错误，请稍后重试",position:"middle",duration:2e3,className:"custom-toast"}),console.error("登录失败:",t)}))}))}}},Na=ka,Ba=(a("eea7"),Object(f["a"])(Na,Da,Sa,!1,null,"563e5c2a",null)),Ma=Ba.exports,Ta=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"w-100 h-100 p-4 border-box bg-deep color-white font3"},[a("div",{staticClass:"form-box border-box bg-cover round-md d-flex justify-content-center"},[a("el-form",{staticClass:"w-60",attrs:{model:t.form,rules:t.rules,"label-width":"auto"}},[a("h2",{staticClass:"text-center mb-4"},[t._v("用户注册")]),a("el-form-item",{attrs:{label:"姓名",prop:"nickname"}},[a("el-input",{attrs:{placeholder:"请输入姓名"},model:{value:t.form.nickname,callback:function(e){t.$set(t.form,"nickname",e)},expression:"form.nickname"}})],1),a("el-form-item",{attrs:{label:"账号",prop:"username"}},[a("el-input",{attrs:{placeholder:"请输入账号"},model:{value:t.form.username,callback:function(e){t.$set(t.form,"username",e)},expression:"form.username"}})],1),a("el-form-item",{attrs:{label:"密码",prop:"password"}},[a("el-input",{attrs:{type:"password",placeholder:"请输入密码"},model:{value:t.form.password,callback:function(e){t.$set(t.form,"password",e)},expression:"form.password"}})],1),a("el-form-item",[a("div",{staticClass:"text-right"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.register()}}},[t._v("注 册")]),a("el-button",{on:{click:t.toLogin}},[t._v("返回登录")])],1)])],1)],1)])},La=[],Oa={name:"Register",data:function(){return{form:{nickname:"",username:"",password:""},rules:{nickname:[{required:!0,message:"请输入姓名",trigger:"blur"}],username:[{required:!0,message:"请输入登录账号",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}]}}},methods:{register:function(){var t=this;Object(d["l"])(this.form).then((function(e){console.log(e),200==e.code?(t.$notify({message:"注册成功"}),t.$router.push("/login")):t.$notify({type:"error",message:e.message||"注册失败，请稍后再试"})})).catch((function(e){console.error(e),t.$notify({type:"error",message:"注册失败，请稍后再试"})}))},toLogin:function(){this.$router.push("/login")}}},Pa=Oa,Qa=(a("5a4d"),Object(f["a"])(Pa,Ta,La,!1,null,"7b3be85e",null)),Ua=Qa.exports,Fa=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"base-list"},[a("section-title",{staticClass:"white-title",attrs:{title:"企业介绍"}}),a("van-nav-bar",{staticClass:"base-nav",attrs:{title:t.baseName,"left-arrow":""},on:{"click-left":t.onClickLeft}}),a("div",{staticClass:"device-list-wrapper"},[a("device-list",{attrs:{shebeilist:t.deviceList},on:{"link-shebei":t.handleLinkShebei}})],1)],1)},za=[],ja=a("6099"),Ra={name:"BaseListView",components:{DeviceList:ja["default"],SectionTitle:ua["a"]},data:function(){return{baseId:null,baseName:"",deviceList:{water:[{ID:1,BACILITIES_NAME:"1号养殖池",EQUIPMENT_NAME:"水质监测仪",STATE:1},{ID:2,BACILITIES_NAME:"2号养殖池",EQUIPMENT_NAME:"溶解氧监测仪",STATE:1},{ID:3,BACILITIES_NAME:"3号养殖池",EQUIPMENT_NAME:"PH值监测仪",STATE:1}],meteorological:[{ID:4,BACILITIES_NAME:"养殖基地",EQUIPMENT_NAME:"温度监测仪",STATE:1},{ID:5,BACILITIES_NAME:"养殖基地",EQUIPMENT_NAME:"湿度监测仪",STATE:1}],monitoring:[{ID:6,camera_name:"1号池塘监控",state:1},{ID:7,camera_name:"2号池塘监控",state:0},{ID:8,camera_name:"基地大门监控",state:1}]}}},created:function(){this.$route.query.base_id&&(this.baseId=this.$route.query.base_id),this.$route.query.base_name&&(this.baseName=this.$route.query.base_name)},methods:{onClickLeft:function(){this.$router.back()},handleLinkShebei:function(t,e,a){console.log("设备类型:",t),console.log("设备信息:",e),this.$store.dispatch("device/updateDeviceInfo",{id:e.ID||e.id||"",name:"monitoring"===t?e.camera_name||"未命名摄像头":e.EQUIPMENT_NAME||"未命名设备",type:t,itemData:e}),"water"===t||"meteorological"===t?this.$router.push("/mobile/device-detail"):"monitoring"===t&&this.$router.push("/mobile/section-title")}}},Ya=Ra,qa=(a("e3b0"),Object(f["a"])(Ya,Fa,za,!1,null,"a0c8dee0",null)),Wa=qa.exports,Ha=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"section-title-page"},[a("van-nav-bar",{staticClass:"device-nav",attrs:{title:t.deviceName,"left-arrow":""},on:{"click-left":t.onClickLeft}}),a("div",{staticClass:"monitor-content"},[a("div",{ref:"videoContainer",staticClass:"monitor-placeholder",attrs:{id:"video-container"}},[t.player?t._e():a("p",[t._v(t._s(t.loadingText))])])])],1)},Ga=[],Xa=a("2f62"),Ja={name:"SectionTitlePage",data:function(){return{jktokens:{value:""},player:null,currentVideoSerial:"",loadingText:"准备加载监控..."}},computed:Object(F["a"])(Object(F["a"])({},Object(Xa["c"])("device",["deviceInfo"])),{},{deviceName:function(){return this.deviceInfo.name||"监控详情"},cameraId:function(){return this.deviceInfo.id||""}}),watch:{deviceInfo:{handler:function(t){var e=this;t&&t.fullDeviceData&&(console.log("设备信息变化，准备更新播放器:",t),this.destroyCurrentPlayer(),this.$nextTick((function(){e.processItemDataAndInitPlayer(encodeURIComponent(JSON.stringify(t.fullDeviceData)))})))},immediate:!0,deep:!0}},mounted:function(){this.jktokens.value||this.getToken()},beforeDestroy:function(){console.log("组件即将卸载，销毁播放器..."),this.destroyCurrentPlayer()},methods:{destroyCurrentPlayer:function(){if(this.player){console.log("销毁播放器实例:",this.player);try{this.player.stop(),this.player.destroy(),console.log("播放器实例销毁成功")}catch(t){console.error("销毁播放器实例时发生错误:",t)}finally{this.player=null,this.currentVideoSerial=""}this.loadingText="准备加载监控..."}},getToken:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,i,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,a="3d0be5dc16b846e58ba2e4efb80d6d7f",i="1d040ec6b1a4d12061fa97ef21987942",e.next=5,_t.a.post("https://open.ys7.com/api/lapp/token/get","appKey=".concat(a,"&appSecret=").concat(i),{headers:{"Content-Type":"application/x-www-form-urlencoded"}});case 5:n=e.sent,t.jktokens.value=n.data.data.accessToken,console.log("Token获取成功:",t.jktokens.value),t.deviceInfo&&t.deviceInfo.fullDeviceData?(console.log("Token获取成功，处理设备信息:",t.deviceInfo),t.$nextTick((function(){t.processItemDataAndInitPlayer(encodeURIComponent(JSON.stringify(t.deviceInfo.fullDeviceData)))}))):(console.log("Token获取成功，但当前无设备信息。"),t.loadingText="等待设备信息..."),e.next=15;break;case 11:e.prev=11,e.t0=e["catch"](0),console.error("获取Token失败:",e.t0),t.loadingText="获取播放授权失败，请稍后重试。";case 15:case"end":return e.stop()}}),e,null,[[0,11]])})))()},processItemDataAndInitPlayer:function(t){var e=this;if(!t)return console.warn("processItemDataAndInitPlayer 调用时 itemDataString 为空。"),void(this.loadingText="无法获取摄像头信息，请返回重试");try{var a=JSON.parse(decodeURIComponent(t));if(console.log("解析设备数据成功:",a),a&&a.video_address&&a.serial_number){if(this.currentVideoSerial===a.serial_number&&this.player)return void console.log("已经在播放该序列号的视频，无需重新创建播放器");this.currentVideoSerial=a.serial_number,this.destroyCurrentPlayer(),this.$nextTick((function(){e.initPlayer(a.video_address,a.serial_number)}))}else console.error("设备数据中缺少 video_address 或 serial_number。无法初始化播放器。"),this.loadingText="设备参数不完整，无法加载监控。"}catch(i){console.error("解析设备数据或初始化播放器时发生错误:",i),this.loadingText="加载监控信息失败。"}},initPlayer:function(t,e){var a=this,i=this.$refs.videoContainer;if(!i)return console.error("播放器容器 #video-container 未找到! 无法初始化播放器。"),void(this.loadingText="播放器容器未就绪，请刷新页面重试。");if(console.log("准备初始化播放器: URL=".concat(t,", SerialNumber=").concat(e,", Token已获取=").concat(!!this.jktokens.value)),!e||!this.jktokens.value||!t)return console.error("初始化播放器参数不足: 序列号、AccessToken或URL缺失。"),void(this.loadingText="播放器初始化失败：缺少必要信息。");console.log("尝试创建新的EZUIKitPlayer实例...");try{this.player=new Nt.a.EZUIKitPlayer({id:"video-container",accessToken:this.jktokens.value,url:t,height:220,width:450,template:"simple",autoplay:!0,handleError:function(t){console.error("EZUIKitPlayer 运行时错误:",t),a.loadingText="播放失败: ".concat(t.msg||t.message||"未知错误"),a.player=null,a.currentVideoSerial=""}}),this.player&&"function"===typeof this.player.play&&"function"===typeof this.player.destroy?(console.log("新的EZUIKitPlayer实例已成功创建:",this.player),this.loadingText=""):(console.error("EZUIKit.EZUIKitPlayer(...) 返回的不是一个有效的播放器实例，或者实例上缺少关键方法。",this.player),this.player=null,this.currentVideoSerial="",this.loadingText="播放器创建失败 (返回无效实例)。")}catch(n){console.error("创建EZUIKitPlayer实例期间发生错误:",n),this.player=null,this.currentVideoSerial="",this.loadingText="创建播放器时出错: ".concat(n.message||"未知错误")}},onClickLeft:function(){this.$router.back()}}},Ka=Ja,Va=(a("0f5a"),Object(f["a"])(Ka,Ha,Ga,!1,null,"36838b1e",null)),Za=Va.exports;n["default"].use(s["a"]);var $a=[{path:"/pc/login",name:"PCLogin",component:p,meta:{withMenu:!1,platform:"pc"}},{path:"/pc/home",name:"PCHome",component:Lt,meta:{withMenu:!1,platform:"pc"}},{path:"/pc/detail",name:"PCDetail",component:ee,meta:{withMenu:!1,platform:"pc"}},{path:"/pc/cultiver",name:"PCCultiver",component:xe,meta:{withMenu:!1,platform:"pc"}},{path:"/data/history",name:"historyData",component:ke,meta:{withMenu:!0,pathName:"数据中心 / 历史数据",platform:"pc"}},{path:"/data/report",name:"reportData",component:Pe,meta:{withMenu:!0,pathName:"数据中心 / 统计报表",platform:"pc"}},{path:"/data/tousi",name:"tousiplan",component:oa,meta:{withMenu:!0,pathName:"数据中心 / 投饲计划",platform:"pc"}},{path:"/device/manage",name:"deviceManage",component:Re,meta:{withMenu:!0,pathName:"设备中心 / 设备管理",platform:"pc"}},{path:"/device/camera",name:"deviceCamera",component:Xe,meta:{withMenu:!0,pathName:"设备中心 / 摄像头管理",platform:"pc"}},{path:"/user/add",name:"userAdd",component:ta,meta:{withMenu:!0,pathName:"用户管理 / 新增用户",platform:"pc"}},{path:"/register",name:"register",component:ta,meta:{withMenu:!1,platform:"pc"}}],ti=[{path:"/mobile/login",name:"MobileLogin",component:Ma,meta:{platform:"mobile"}},{path:"/mobile/home",name:"MobileHome",component:Ia,meta:{requiresAuth:!0,platform:"mobile"}},{path:"/mobile/register",name:"MobileRegister",component:Ua,meta:{platform:"mobile"}},{path:"/mobile/list",name:"MobileBaseList",component:Wa,meta:{requiresAuth:!0,platform:"mobile"}},{path:"/mobile/detail",name:"MobileDeviceList",component:function(){return Promise.resolve().then(a.bind(null,"6099"))},meta:{requiresAuth:!0,platform:"mobile"}},{path:"/mobile/device-detail",name:"MobileDeviceDetail",component:function(){return a.e("chunk-55a3b784").then(a.bind(null,"e5c9"))},meta:{platform:"mobile"}},{path:"/mobile/section-title",name:"MobileSectionTitlePage",component:Za,meta:{platform:"mobile"}}],ei=new s["a"]({mode:"history",routes:[{path:"/",redirect:function(){var t=Object(o["a"])();return Object(r["b"])(t)}},{path:"/login",redirect:function(){var t=Object(o["a"])();return Object(r["b"])(t)}},{path:"/home",redirect:function(){var t=Object(o["a"])();return Object(r["a"])(t)}},{path:"/detail",redirect:function(){var t=Object(o["a"])();return t?"/mobile/detail":"/pc/detail"}},{path:"/cultiver",redirect:function(){var t=Object(o["a"])();return t?"/mobile/home":"/pc/cultiver"}}].concat($a,ti,[{path:"*",redirect:function(){var t=Object(o["a"])();return Object(r["a"])(t)}}])});ei.beforeEach(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(e,a,i){var n,s,l,c,d;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n=Object(o["a"])(),s=e.path,!Object(r["d"])(s,n)){t.next=12;break}return t.prev=3,t.next=6,Object(r["c"])(ei,s,n,e.query);case 6:return t.abrupt("return");case 9:t.prev=9,t.t0=t["catch"](3),console.error("设备跳转失败:",t.t0);case 12:if(!e.matched.some((function(t){return t.meta.requiresAuth}))){t.next=19;break}if(l=localStorage.getItem("token"),c=localStorage.getItem("userInfo"),l&&c){t.next=19;break}return d=Object(r["b"])(n),i({path:d,query:{redirect:e.fullPath}}),t.abrupt("return");case 19:i();case 20:case"end":return t.stop()}}),t,null,[[3,9]])})));return function(e,a,i){return t.apply(this,arguments)}}()),ei.afterEach((function(t,e){console.log("路由跳转: ".concat(e.path," -> ").concat(t.path)),t.meta&&t.meta.pathName&&(document.title=t.meta.pathName)}));e["a"]=ei},a355:function(t,e,a){t.exports=a.p+"static/img/device-icon-o2.10969ada.png"},a5ca:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"section-title"},[a("h3",{staticClass:"title-text"},[t._v(t._s(t.title))])])},n=[],s={name:"SectionTitle",props:{title:{type:String,required:!0}}},o=s,r=(a("3ce9"),a("2877")),l=Object(r["a"])(o,i,n,!1,null,"6cf49a12",null);e["a"]=l.exports},ad90:function(t,e,a){"use strict";a("ba69")},b052:function(t,e,a){},b603:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAeCAYAAADD0FVVAAAAAXNSR0IArs4c6QAABVZJREFUSEuNlntQVHUUx79nF5ZdQOWlCEqG+GowM6sZfKYzTEhmiK0S+YrJVKxBbSg1HDR8DEgTJcmopYUTvghYC0snShwboayISTAUIhEBBTFey+6ye0/ee/euu4Ta7697Xp/fOed37/ldwn3W9da+2QysBygcgIrA9SDKCQlwK7xfjKKngRzqb1mywCJwoEVFoYHuCxVLUxN7drpDrcge3ej7D/Rqi+U1YnzqhKtjSI4TnHTZY4I0SaJc22wpATDLyXbEBfpnKw8is+U2AHfJiWn1+BDNAfGx5ropGkRi6VpRVjOPHROira1pNNcCCLsHpRIXaFWDOQZggwzkvPBRuqXODahuMKcweIdsx7rwUdo9lS3sJfTC3dsKoVcDta8KJhdo5bXe5cTIlYOEWMttz1Pu/qYUUVSR6rhKbbpps2nESsCM954I1W2r/rsnyMwqDxWBBAaz1drjAv2tzhhPwBExiEAzVW7mKptV0y53glZOGa07WFFnZNmOTZPDPDMq6owtAAKdKrrgAr14xRgPUqCqKCs8ytXo7bCXq68fqzOE1vZa7ZtufnqsLv3iVWMRgOkABLEgACdcoD9dNsYzsZQpmKPgZi6HTWuHkr5hgs7wSI1Rgt6FpEZM8Nru3HPl2QV6/lJ3PCmZCohSa83lVouHBCVm/Y1wb0NwdY8dSikzwr12/XipewUThxJUAkNQgdSVLtDSS93xYDlTAkV5aM3lJpNGggoEfVu1t2HYY912KL8z+/HBmaV/dIllO3NqXKDfVXTMI6JiGSpEmU22co3WXYIykf5OjbfBb1yXDGV6I/LJQTnf/961B+ApLO0LlQA67QI98+s/qwDaL71CwFxjr+WCVqfplBms76wdYhgS1qn09MOop4ZseGBPT/18JwGgHOWLYeIYo6fPaa+eDrMdGmOs9znl9WiHAgURdj7/jM+W/mApU0NZ+3wCfaUYmWBYEOEbe+LsLW8PrXuXXf9SzFTfwpNld7IBvHnPlzcviPBLdwbT1780eVrM2h6HE9NW/QzftIKy9ukkIJWB5+y2CgJ2L5zud6zwwu1FzHTiXgxP1M/wr3K8UsfPte0F0VpJQdgfN9N/zfFzbVtANOA7COCTuFn+q46db19LzHvlQ0N53LP+Ux3QvLNt0mcHwLZkToBb3g9tC0EoGOgAnHQZS+YEbMorbasBY5zMpZClc/wbpdxyS1olKANpr0YO3Zpb0ioehGPo3g9u8gnQeHS0TiWmc3af1BWRQ6Xq6NCZWzKUaITaKugENYnz8aGLGNEJc4edVuIJnJcQFSiNSjrw7U0JarV5+Lmp+iaBhNKHEuX+J66aG7hPiScg//XowMWSKae4RYK622i4TUWDBRKu/B8oEeYnzhterMQTcDTxheGvSNA9J5vlgyJsSnoxKMMhP4SsNqsH9WmEcBVxuf1Mtq+LCUqVUFlFTfLQJZjWLwjWfWC4kUBMhx7MpOwNsUFJWUVNFXe/ncmirxrqMUmxgXUSK7OgKReM5ZLAtDt5UdDGzC+bPgIg3Zb9F4GLk/Uj5r9f0Kxn5ny5SFQl64MnKr6UWdgyzGa13VQUzJy4OW7kvvT8G9PAeBvgSQCJE/0vYsreGBdsSM9vjgYL3ygxAnjau4tHljmg4sOuo43LGDjsyIpxWOfGa95aHNLbP9OdRxp3gCBdhlIviXdveTlko7OfY/SlfdG4nomzXCCESma6LF6tBIwGENFvk71bl4Q4hotLpoqwLfdapKDCxwCNf+BBMdpBSE5b9shnA/e9v5aZUj5vWAqilfKfBw+WXUi8mxuIcMxq6juYsTpMvhAHWP8Cc1IuH1pZm7MAAAAASUVORK5CYII="},b669:function(t,e,a){},b6a0:function(t,e,a){t.exports=a.p+"static/img/top-info-sd.8fc88cab.png"},b7a1:function(t,e,a){"use strict";a("3ecc")},ba69:function(t,e,a){},bacf:function(t,e){t.exports="data:image/png;base64,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"},bbe0:function(t,e,a){t.exports=a.p+"static/img/map-icon3.dc17f549.png"},be27:function(t,e,a){},be38:function(t,e,a){"use strict";a("efa6")},bedf:function(t,e,a){},c021:function(t,e,a){},c460:function(t,e,a){t.exports=a.p+"static/img/daqiy.1b14da96.png"},c588:function(t,e,a){"use strict";a("5a8e")},c8bc:function(t,e,a){t.exports=a.p+"static/img/device-icon-ph.016ea6f2.png"},ca38:function(t,e,a){t.exports=a.p+"static/img/top-info-yg.55532633.png"},ca67:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAAAjCAYAAADR20XfAAAAAXNSR0IArs4c6QAAAVRJREFUeF7t3cFNw2AQROH4lA5og04QF1qgjlQRKakC0VBKCSdyQTGRdi4Zf75au/K88ZNvv5eP4+W6W7m+D+/ntfvuIfDMBN4OX59rz78Q5Jnr9exTAg8L4ksxRW2+icCvOLcvCEGa6pVlSoAgU4LmqwkQpLpe4aYECDIlaL6aAEGq6xVuSuAmyP7l9TRdZh6BVgILQVqrlStBgCAJinbUEiBIbbWCJQgQJEHRjloCBKmtVrAEAYIkKNpRS4AgtdUKliBAkARFO2oJEKS2WsESBAiSoGhHLQGC1FYrWIIAQRIU7aglQJDaagVLECBIgqIdtQQIUlutYAkCBElQtKOWAEFqqxUsQYAgCYp21BJwLlZttYJNCDi0YULPbD0BgtRXLOCEAEEm9MzWEyBIfcUCTggQZELPbD2BP4LcS+zU9/p3YdMBH/4/CEE2/Z5sNvx/gvwAI4J9szg3tEsAAAAASUVORK5CYII="},cc0d:function(t,e,a){"use strict";a("2523")},ce4c:function(t,e,a){},ce6c:function(t,e){t.exports="data:image/png;base64,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************************************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"},d697:function(t,e,a){},d797:function(t,e,a){t.exports=a.p+"static/img/shidu.bd6b7f47.png"},d8e3:function(t,e,a){"use strict";a("53df")},d8e4:function(t,e,a){},d962:function(t,e,a){"use strict";a("01d7")},da54:function(t,e,a){"use strict";a("ecb2")},daf1:function(t,e,a){},dc9d:function(t,e,a){"use strict";a("9098")},df74:function(t,e){t.exports="data:image/png;base64,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"},e0d0:function(t,e,a){},e34b:function(t,e,a){},e3b0:function(t,e,a){"use strict";a("bedf")},e42c:function(t,e,a){"use strict";a("3302")},e73a:function(t,e,a){"use strict";a("f643")},e7e4:function(t,e,a){"use strict";a("46a0")},e864:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAOCAYAAAD9lDaoAAAAAXNSR0IArs4c6QAAASZJREFUKFON0b9OAkEQBvD55kAN/4KRszwBQUmIkTPESKE+gxa+mPElrLTTd1DADmNAC5XCAgELBG7G7AkXUAu32p39ZbPzDejHajTe4oWC3Z8twxzOVa0TwDP7WrObXxh8tovF1Y8p9FHtsZMspZNdAHrX6m1CrOzWeuQagJh7H1Uf+raEOVROR9sGiVCFlDpuPnEZoJv7XirMlhNaGj+NhkipcgWgA2U6dTOxevBSCOwI64p4+gzoLsCHROoMZHg0j8hziDmmItYEVUB0+wuBOC4kPEWmr3lkaQqevghpeYLWlMfHPpr9+HgE23QH8D4EZ9v5aDV4iRY5vONEX78j4D0i6bq5xEUQgQnTzSy/m0K92dtQsnKlbORqLsx/jWV2mH8N+AsvL5PnW5zPjAAAAABJRU5ErkJggg=="},e973:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIcAAAAjCAYAAAC+ahnyAAAAAXNSR0IArs4c6QAAATtJREFUeF7tnF0KAWEARWfIo214YAMelaUoyYKEshRlEyzBLvyUMiUZ6rsvTsfrNLe5556mefnUu/P4VrX8FpPjtu261/6XwPowmrc9fa0c/ztu6ZP/LIdviFLUnPuf0jRvDuXgjFvaRDlKCYLvVw7wuKXVXuS4Xm6X5fS0Kw31fgaB1X4463Tr7uObQzkYo6ZaNHL0Bv1NKtQcFoFaOViDJtsoR5ImLEs5YIMm6yhHkiYsSzlggybrKEeSJixLOWCDJusoR5ImLEs5YIMm6yhHkiYsSzlggybrKEeSJixLOWCDJusoR5ImLEs5YIMm6yhHkiYsSzlggybrKEeSJizLcyuwQRN1PJqQoAjNUA7osIlaypGgCM1QDuiwiVrKkaAIzXiT41NPT99DDaiq6uf/51AOrgSfmn2T4w5iLYNHQL29AQAAAABJRU5ErkJggg=="},eb59:function(t,e,a){t.exports=a.p+"static/media/1.5fccdb5c.mp4"},eba0:function(t,e,a){"use strict";a("f9f7")},eca2:function(t,e,a){"use strict";a("b669")},ecb2:function(t,e,a){},edcb:function(t,e){t.exports="data:image/png;base64,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"},eea7:function(t,e,a){"use strict";a("1be6")},ef50:function(t,e,a){"use strict";a("d697")},efa6:function(t,e,a){},f227:function(t,e,a){"use strict";a("d8e4")},f2bf:function(t,e,a){"use strict";a("6993")},f2f5:function(t,e){t.exports="data:image/png;base64,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"},f643:function(t,e,a){},f928:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAYAAAAD+CAYAAAAzmNK6AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAVxSURBVHhe7d3Pi51nGYDh7zuZQWFiorgrCBo1Xboxm0BpF6EVI02hYsSNO3dC/wPJfyC4c+dGkSql2BQbtU0aCC667aJV40JwqWaSyQzOmfnMOfnyA6xQ0CDMfV2L4XmfGQ7DLN575pwDMx5753AaWPnV5sb4yt4z4835DHCkCcBjxuX+dOLGlTsnr76+s5rnNcCRc/P6T54SgI8w3dn+++FrP/v18MarH84rgCPj1NOf+96/BeDg4rlL8wjAEfUgAIv5DECMAABECQBAlAAARAkAQJQAAEQJAECUAABECQBAlAAARAkAQJQAAEQJAECUAABECQBAlAAARAkAQJQAAEQJAECUAABECQBAlAAARAkAQJQAAEQJAECUAABECQBAlAAARAkAQJQAAEQJAECUAABECQBAlAAARAkAQJQAAEQJAECUAABECQBAlAAARAkAQNQqAJfvjwCULDYX4/fH5XKazwBELPaeHf984sZbO/MZgIj1awAnr75+Z9rZ/sd6A0DCOgDjcn86/OVP31xvAEh49C6gy7/4wzwBEOBtoABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAUHL+m1+eJwEAKFm8/J2vTxub43pebwBIGLdOfPrWcxe2VrMAAMRsn33h+CevTV8QAICYaWNj3D+cfiQAAE3nBQAgSgAAogQAIEoAAKIEACBKAACiBAAgSgAAogQAIEoAAKIEACBKAACiBAAgSgAAogQAIEoAAKIEACBKAACiBAAgalx9OPXMd/9684O//Hi9AeBjO/bz3/5gHoeDi+cuzeP9/cHBcjhYLg/e/c3bi6+ePTMtl/8c93b3Dq9feW/4ypkvDXfv7g67O3vD76/9afj86c8Od27tDbdv7w1/fP9vw9anNoed2/vD9vb+sLu7nB/2v/b49+svAID/h2la3cDrX8LXxkfjQ4cH03A43vvCJ0MAAJ6c+VY/uHe/r2741a3/Hzz41PhYFFYW00eU4X9j/cCeAgJo8BQQAAIAUCUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQJQAAUQIAECUAAFECABAlAABRAgAQ5V9CAkfbi98+vXjpW18bt058Zt4wexiA9QngCJo2Nsdbz104vn32+a3VPK/z/CCAjE/8bvricph+OCyGb8yrsGH4FzsVs6zGLnzhAAAAAElFTkSuQmCC"},f9f7:function(t,e,a){},fbe7:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAOCAYAAAD9lDaoAAAAAXNSR0IArs4c6QAAASZJREFUKFON0b9OAkEQBvD55kAN/4KRszwBQUmIkTPESKE+gxa+mPElrLTTd1DADmNAC5XCAgELBG7G7AkXUAu32p39ZbPzDejHajTe4oWC3Z8twxzOVa0TwDP7WrObXxh8tovF1Y8p9FHtsZMspZNdAHrX6m1CrOzWeuQagJh7H1Uf+raEOVROR9sGiVCFlDpuPnEZoJv7XirMlhNaGj+NhkipcgWgA2U6dTOxevBSCOwI64p4+gzoLsCHROoMZHg0j8hziDmmItYEVUB0+wuBOC4kPEWmr3lkaQqevghpeYLWlMfHPpr9+HgE23QH8D4EZ9v5aDV4iRY5vONEX78j4D0i6bq5xEUQgQnTzSy/m0K92dtQsnKlbORqLsx/jWV2mH8N+AsvL5PnW5zPjAAAAABJRU5ErkJggg=="},fd03:function(t,e,a){"use strict";a.d(e,"x",(function(){return f})),a.d(e,"y",(function(){return h})),a.d(e,"w",(function(){return p})),a.d(e,"z",(function(){return g})),a.d(e,"n",(function(){return A})),a.d(e,"k",(function(){return b})),a.d(e,"j",(function(){return v})),a.d(e,"l",(function(){return C})),a.d(e,"m",(function(){return w})),a.d(e,"b",(function(){return x})),a.d(e,"h",(function(){return y})),a.d(e,"f",(function(){return E})),a.d(e,"e",(function(){return I})),a.d(e,"d",(function(){return D})),a.d(e,"s",(function(){return S})),a.d(e,"q",(function(){return _})),a.d(e,"o",(function(){return k})),a.d(e,"p",(function(){return N})),a.d(e,"r",(function(){return B})),a.d(e,"a",(function(){return M})),a.d(e,"c",(function(){return T})),a.d(e,"A",(function(){return L})),a.d(e,"g",(function(){return O})),a.d(e,"i",(function(){return P})),a.d(e,"t",(function(){return Q})),a.d(e,"v",(function(){return U})),a.d(e,"u",(function(){return F}));a("99af"),a("d3b7"),a("ac1f"),a("5319");var i=a("bc3a"),n=a.n(i),s=(a("4328"),a("a18c")),o=a("d399"),r=a("4360"),l={80:"http://150.158.85.33:9000","":"http://150.158.85.33:9000",83:"http://150.158.85.33:9001"},c=window.location.port||"80";function d(t,e){return new Promise((function(a,i){n.a.get(t,{params:e}).then((function(t){a(t.data)})).catch((function(t){i(t.data)}))}))}function u(t,e){return new Promise((function(a,i){n.a.post(t,e).then((function(t){a(t.data)})).catch((function(t){i(t.data)}))}))}function m(t,e){return new Promise((function(a,i){n.a.put(t,e).then((function(t){a(t.data)})).catch((function(t){i(t.data)}))}))}n.a.defaults.baseURL=l[c]||"http://150.158.85.33:9000",console.log("Current frontend port: ".concat(c,", API endpoint: ").concat(n.a.defaults.baseURL)),n.a.defaults.timeout=18e4,n.a.defaults.headers.post["Content-Type"]="application/x-www-form-urlencoded;charset=UTF-8",n.a.interceptors.request.use((function(t){var e=localStorage.getItem("token");return e&&(t.headers.xtoken=e),t.headers["Cache-Control"]="no-cache",t}),(function(t){return Promise.error(t)})),n.a.interceptors.response.use((function(t){return 200===t.status?Promise.resolve(t):Promise.reject(t)}),(function(t){if(t.response.status){switch(t.response.status){case 401:s["a"].replace({path:"/login",query:{redirect:s["a"].currentRoute.fullPath}});break;case 403:Object(o["a"])({message:"登录过期，请重新登录",duration:1e3,forbidClick:!0}),localStorage.removeItem("token"),r["a"].commit("loginSuccess",null),setTimeout((function(){s["a"].replace({path:"/login",query:{redirect:s["a"].currentRoute.fullPath}})}),1e3);break;case 404:Object(o["a"])({message:"网络请求不存在",duration:1500,forbidClick:!0});break;default:Object(o["a"])({message:t.response.data.message,duration:1500,forbidClick:!0})}return Promise.reject(t.response)}}));var f=function(t){return u("/user/login/",t)},h=function(t){return u("/learning/select_equipment/?v=2.0",t)},p=function(t){return d("/learning/getbreading_base/",t)},g=function(t){return d("/learning/getequipment/?v=2.0",t)},A=function(t){return u("/learning/cultiver/?v=2.0",t)},b=function(t){return m("/learning/cultiver/",t)},v=function(t){return d("/learning/getpeoplefeed/",t)},C=function(t){return u("/user/user/",t)},w=function(t){return u("/learning/caicon/",t)},x=function(t){return u("/report/TouSi/AddSettings/",t)},y=function(t){return d("/report/TouSi/GetSettings/",t)},E=function(t){return d("/report/TouSi/GetDailyData/",t)},I=function(t){return u("/report/TouSi/EditDailyData/",t)},D=function(t){return u("/report/TouSi/DeleteSetting/",t)},S=function(t){return u("/report/getHistoryEquipmentByBasename/",t)},_=function(t){return u("/report/getHistoryData/",t)},k=function(t){return u("/report/getBaseEquipment/",t)},N=function(t){return u("/report/getBaseEquipment3/",t)},B=function(t){return u("/report/getReportData/",t)},M=function(t){return u("/report/AddEquipment/",t)},T=function(t){return u("/report/DeleteEquipment/",t)},L=function(t){return u("/report/uniqueEQUIPMENT_ID/",t)},O=function(t){return d("/report/GetProductionRecords/",t)},P=function(t){return d("/report/Getbasic_data_fish_pond/",t)},Q=function(t){return d("/learning/getcamera/",t)},U=function(t){return d("/learning/getweather/",t)},F=function(t){return d("/learning/getfeedersensing/",t)}},fddf:function(t,e,a){"use strict";a("8e6c")},fdea:function(t,e,a){t.exports=a.p+"static/img/top-info-qy.8eefc0cc.png"},fea9c:function(t,e,a){t.exports=a.p+"static/img/jiankong(2).e435ad74.png"}});