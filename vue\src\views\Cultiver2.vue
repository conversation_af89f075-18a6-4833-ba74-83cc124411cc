<!-- 首页 -->
<script>
import SubtitledSection from '@/components/layout/SubtitledSection.vue'
import SubtitledSectionLong from '@/components/layout/SubtitledSectionLong.vue'
import Head2 from '@/components/layout/Head2.vue'
import { cultiverlistv2, add_cultiver, caicon, shebeilistv2, Getpeoplefeed, GetDailyData, AddSettings, getfeedersensing } from '@/request/api';
import DeviceList2 from '@/components/layout/DeviceList2.vue'
import LogList from '@/components/layout/LogList.vue'
import Line2 from '@/components/charts/Line2/Line2.vue'
export default {
  components: {
    SubtitledSection, //短标题框
    SubtitledSectionLong,//长标题框
    Head2,//头部
    DeviceList2,
    LogList,
    Line2
  },
  data() {
    return {
      recordlist: [],
      totalFeed: 0,
      planlist: [],
      dailyfeedlist: [],
      allData: [],
      addDialogVisible: false,
      // 背景图片路径
      bgImage: require('@/assets/ui/bg.png'),
      touwei1Image: require('@/assets/ui/touwei-icon1.png'),
      touwei2Image: require('@/assets/ui/touwei-icon2.png'),
      newSettingsForm: {
        pond_number: '',
        feeder_number: '',
        fish_quantity: '',
        fish_specification: '',
        feed_coefficient: '',
        daily_feed_rate: '',
        set_date: '',
      },
      box: 0,
      isdisabled: true,//按钮不可点击
      tianqi: {},
      temp: {},
      rizhishuru: 0,
      tianjiarizhi: '添加日志',
      map: '',
      shelist: [],
      zuobiaolist: [],
      base_name: "",
      miaoshu: [],
      param: 'b2-1',//底下两个图表默认显示设备
      equipList: [],
      actionOptions: [
        { name: '投饲料', value: '投饲料' },
        { name: '投药物', value: '投药物' },
        { name: '捕捞水产品', value: '捕捞水产品' },
        { name: '投放水产品', value: '投放水产品' },
        // 捕捞水产品和投放水产品
      ],
      typeOptions: [ // 随actionOption选择而变化
        { name: '饲料1', value: 1 },
        { name: '饲料2', value: 2 }
      ],
      addForm: {
        time: '',
        name: '',
        action: '',
        // type: '',
        num: '',
        remark: '',
        zhonglei: '',
        sheshiid: '',
        base_id: '',
      },
      addFromName: {
        sheshiid: '点击左侧设施选择，设施号',
        time: '时间',
        name: '操作人姓名',
        action: '动作',
        // type: '种类',
        zhonglei: '种类',
        num: '数量',
        // remark: '备注',
      },

      addLoading: false,
      loglist: [],
      total_quantity: '',
      latestDailyFeedAmount: '',
      errMsg: '',
      dis: true,
      wetherinfo: {
        'wendu': {
          img: require('@/assets/ui/top-info-wd.png'),
          value: '0'
        },
        'shidu': {
          img: require('@/assets/ui/top-info-xy.png'),
          value: '0'
        },
        'fengxiang': {
          img: require('@/assets/ui/top-info-ql.png'),
          value: '0'
        },
        'fengsu': {
          img: require('@/assets/ui/top-info-fs.png'),
          value: '0'
        },
        'guangzhao': {
          img: require('@/assets/ui/top-info-yg.png'),
          value: '0'
        },
        'jiangyuliang': {
          img: require('@/assets/ui/top-info-sd.png'),
          value: '0'
        },
        'qiya': {
          img: require('@/assets/ui/top-info-qy.png'),
          value: '0'
        }
      },
      markers: [], // 存储所有地图标记
    }
  },
  computed: {
  },
  mounted() {
    this.getEquipList();
    this.getlist();
    // this.renderMap();
    this.GetFeedplan();
    // this.startPolling(); // 开始定时请求数据
    this.getfeeder();
    // this.getpeoplefeed();
  },
  methods: {

    startPolling() {
      this.intervalId = setInterval(() => {
        // this.getlist();
      }, 60000); // 每60秒（1分钟）调用一次getlist
    },
    getpeoplefeed() {
      Getpeoplefeed({
        base_id: this.$route.query.base_id,
      }).then(res => {
        this.total_quantity = res.total_quantity + this.totalFeed;
        console.log('12344', this.total_quantity);
      }).catch(function (error) {
        alert('获取失败' + error)
      })
    },
    addLog() {
      this.addLoading = false;
      if (this.addForm.sheshiid == '') {
        this.$message.warning('请选择设施');
        return;
      }
      if (this.addForm.time == '') {
        this.$message.warning('请选择时间');
        return;
      }
      if (this.addForm.name == '') {
        this.$message.warning('请填写操作人姓名');
        return;
      }
      if (this.addForm.action == '') {
        this.$message.warning('请选择动作');
        return;
      }
      if (this.addForm.zhonglei == '') {
        this.$message.warning('请填写种类');
        return;
      }
      if (this.addForm.num == '') {
        this.$message.warning('请填写数量');
        return;
      }

      this.addForm.base_id = this.$route.query.base_id
      add_cultiver(
        this.addForm
      ).then(res => {
        this.addLoading = false;
        this.loglist = res.data.loglist;
        this.alertlist = res.data.alertlist;
        alert('提交成功')
      }).catch(function (error) {
        alert('提交失败' + error)
      })

      this.rizhishuru = 0;
      // 发送请求添加日志
    },
    convertBacilitiesName(name) {
      // 移除所有空格
      let converted = name.trim();
      // 转换为小写
      converted = converted.toLowerCase();
      // 移除"塘"字
      converted = converted.replace(/塘$/, '');
      // 将类似 "a1-01" 转换为 "a1-1" (移除前导零)
      converted = converted.replace(/-0+(\d+)/, '-$1');
      return converted;
    },
    handleLinkShebei(item) {
      // 将坐标字符串转换为数组
      this.param = this.convertBacilitiesName(item.BACILITIES_NAME)
      this.rizhishuru = 1;
      this.addForm.sheshiid = item.ID;
      this.tianjiarizhi = "添加日志 " + item.BACILITIES_NAME;

      const coordinates = item.BACILITIES_LOCATION.split(',').map(coord => parseFloat(coord));

      if (coordinates && coordinates.length === 2) {
        // 设置地图中心点
        this.map.setCenter(coordinates);

        // 调整缩放级别
        this.map.setZoom(18);

        // 平滑移动到目标位置
        this.map.panTo(coordinates);

        // 重置所有标记的zIndex
        this.markers.forEach(marker => {
          marker.setzIndex(100);
        });
        
        // 找到对应标记并设置高zIndex
        const coordStr = coordinates.toString();
        let foundMarker = false;
        
        this.markers.forEach(marker => {
          const markerPos = marker.getPosition();
          if (markerPos && markerPos.toString() === coordStr) {
            marker.setzIndex(1000);
            foundMarker = true;
          }
        });
      }
      
      // 更新地图详情弹窗内容
      this.miaoshu = [
        {
          label: "设施名",
          value: item.BACILITIES_NAME
        },
        {
          label: "设施类型",
          value: item.BACILITIES_TYPE
        },
        {
          label: "设施描述",
          value: item.INTRODUCE
        }
      ];
      
      // 显示详情弹窗
      this.box = 1;
      
      this.GetFeedplan()
    },
    addNewSettings() {
      // 将设置信息保存到数据中
      const newSettings = {
        // 填入设置信息字段
        pond_number: this.newSettingsForm.pond_number,
        feeder_number: this.newSettingsForm.feeder_number,
        fish_quantity: this.newSettingsForm.fish_quantity,
        fish_specification: this.newSettingsForm.fish_specification,
        feed_coefficient: this.newSettingsForm.feed_coefficient,
        daily_feed_rate: this.newSettingsForm.daily_feed_rate,
        // 其他设置信息字段
      };

      // 添加完成后关闭对话框
      console.log('New Settings Data:', newSettings);
      this.addDialogVisible = false
      // // 调用后端接口添加设置信息
      this.addSettingsToBackend(newSettings);
    },
    addSettingsToBackend(newSettings) {
      // console.log('newSettings',newSettings);
      AddSettings(newSettings) // 假设 AddSettings 是你用于将设置信息添加到后端的函数
        .then(response => {
          this.$message({
            message: '设置添加成功',
            type: 'success',
            duration: 2000,  // 显示时长，单位毫秒
            showClose: true  // 显示关闭按钮
          });

          console.log('Settings added successfully');
          // 在成功添加设置信息后，可以调用方法刷新数据
        })
        .catch(error => {
          this.$message({
            message: '设置添加失败：' + error.message,
            type: 'error',
            duration: 3000,
            showClose: true
          });

          // 如果发生错误，可以在这里进行错误处理
          console.error('An error occurred:', error);
        });
    },
    GetFeedplan() {
      GetDailyData()
        .then(response => {
          if (response.FishFeedingPlan) {
            this.allData = Array.isArray(response.FishFeedingPlan) ? response.FishFeedingPlan : [];
            if (this.param !== undefined) {
              const today = new Date();
              const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0, 0);
              const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1, 0, 0, 0, -1); // 下一天的0点前1毫秒  
              //获取全部日期匹配的数据
              // const filteredData = this.allData.filter(item =>
              //   item.pond_number.toLowerCase() === this.param.toLowerCase()
              // );
              
              // 此处是筛选出近三个月匹配的数据
              const currentDate = new Date();
              const threeMonthsAgo = new Date();
              threeMonthsAgo.setMonth(currentDate.getMonth() - 3);
              const filteredData = this.allData.filter(item => {
                const itemDate = new Date(item.plan_date);
                return item.pond_number.toLowerCase() === this.param.toLowerCase() 
                  && itemDate >= threeMonthsAgo 
                  && itemDate <= currentDate;
              });
              // 按日期排序（从新到旧）

              if (filteredData.length > 0) {
                this.planlist = filteredData.map(item => ({//fish_specification
                  plan_date: item.plan_date,
                  daily_feed_amount: item.fish_specification,
                  id: 2
                }));

                this.dailyfeedlist = filteredData.map(item => ({
                  plan_date: item.plan_date,
                  daily_feed_amount: item.daily_feed_amount,
                  id: 1
                }));
              }
              else{
                this.planlist = [
                  {
                    plan_date:[1],
                    fish_specification: ['1'],
                    id: 2
                  }
                ];
                this.dailyfeedlist = [{
                  plan_date: [0],
                  daily_feed_amount: ['1'],
                  id: 1
                }];
              }
              // 提取 plan_date, daily_feed_amount 和 fish_quantity 并保存到 planlist 和 dailyfeedlist

              const todayFeed = this.dailyfeedlist.filter(feed => {
                const feedDate = new Date(feed.plan_date);
                return feedDate >= startOfDay && feedDate <= endOfDay; // 确保是当天的记录
              });

              // 提取当天的 daily_feed_amount，假设只取最新的
              const latestDailyFeedAmount = todayFeed.length > 0 ? todayFeed[todayFeed.length - 1].daily_feed_amount : 0; // 默认值为 0
              // console.log('当天最新的 daily_feed_amount:', latestDailyFeedAmount);

              // 如果需要在模板中使用，设置一个组件属性
              this.latestDailyFeedAmount = latestDailyFeedAmount;


              // 输出结果以确认
              //console.log('planlist:', this.planlist);
              // console.log('dailyfeedlist:', this.dailyfeedlist);
            } else {
              console.error('active 未定义');
            }
          } else {
            console.error('FishFeedingPlan 数据未定义');
          }
        })
        .catch(error => {
          console.error('获取数据时发生错误:', error);
        });
    },
    getCurrentTime() {
      const now = new Date();
      return `${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()} ${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}`;
    },
    getEquipList() {
      this.base_name = this.$route.query.base_name;
      cultiverlistv2({
        base_id: this.$route.query.base_id
      }).then(res => {
        // this.shelist = res.data.shebeilist;
        this.zuobiaolist = res.data.zuobiaoilist;
        this.loglist = res.data.loglist;
        this.tianqi = res.data.tianqi;
        // this.alertlist = res.data.alertlist;
        this.renderMap();
      })
    },
    getlist() {
      this.base_name = this.$route.query.base_name;
      shebeilistv2({
        base_id: this.$route.query.base_id
      }).then(res => {
        let tianqi = res.data.tianqi;
        Object.keys(tianqi).forEach(key => {
          if (Object.prototype.hasOwnProperty.call(this.wetherinfo, key)) {
            this.wetherinfo[key].value = parseFloat(parseFloat(tianqi[key]).toFixed(1));
          }
        });
        this.shelist = res.data.sheshilist;
        this.renderMap();
      })
    },
    showAddDialog() {
      this.addDialogVisible = true;
    },
    renderMap() {
      this.$AMapLoader.load({
        key: 'bb41d02b6376f70646e2490b6bf5f80b',
        version: '1.4.15',
        plugins: [],
        AMapUI: {
          version: '1.1',
          plugins: []
        },
        Loca: {
          version: '1.3.2'
        }
      }).then((AMap) => {
        this.map = new AMap.Map('map-container', {
          mapStyle: 'amap://styles/blue',
          layers: [new AMap.TileLayer.Satellite(), new AMap.TileLayer.RoadNet()],

          zoom: 18,
          // center: this.zuobiaolist[0]
          center: this.shelist[0].BACILITIES_LOCATION.split(',')
        });
        // const list = this.zuobiaolist;

        async function ca_icon(local) {
          let icon = '';
          icon = new AMap.Icon({
            size: new AMap.Size(40, 40), // 图标尺寸
            image: require('@/assets/ui/map-icon4.png'), // Icon的图像
            // image: require('@/assets/img/shexiangtou.svg'), // Icon的图像
            imageSize: new AMap.Size(40, 40) // 根据所设置的大小拉伸或压缩图片
          });
          return icon;
        }

        this.markers = []; // 存储所有标记

        this.shelist.forEach(async (item) => {
          let icon = await ca_icon(item)//等待await后面的函数完后才能之后再返回icon
          const marker = new AMap.Marker({
            position: item.BACILITIES_LOCATION.split(','),
            offset: new AMap.Pixel(-20, -25), // 偏移值
            icon,
            zIndex: 100, // 默认zIndex
            label: {
              content: item.BACILITIES_NAME,
              //offset: new AMap.Pixel(27, 25)

            },
          })
          marker.on('click', () => {
            // 重置所有标记的zIndex
            this.markers.forEach(m => {
              m.setzIndex(100);
            });
            
            // 设置当前点击标记的高zIndex
            marker.setzIndex(1000);
            
            this.box = 1;
            this.map.setCenter(item.BACILITIES_LOCATION.split(','));
            const zuobiaopinStr = item.BACILITIES_LOCATION
            this.miaoshu = [
              {
                label: "设施名",
                value: item.BACILITIES_NAME
              },
              {
                label: "设施类型",
                value: item.BACILITIES_TYPE
              },
              {
                label: "设施描述",
                value: item.INTRODUCE
              }
            ]
            
            // 调用与设施列表相同的处理逻辑
            this.handleLinkShebei(item);
          })
          this.markers.push(marker); // 将标记添加到数组中
          this.map.add(marker);
        })
      })
    },
    async getEquipmentType(local, base_id) {
      let type = '水质检测' // 默认类型
      await caicon({ zuobiao: local, base_id: base_id }).then(res => {
        if (res && res.data) {
          type = res.data
        }
      })
      return type
    },


    onCloseCover() {
      this.box = 0;
    },
    getfeeder() {
      // 获取当天的开始和结束时间（假设时间为00:00:00到23:59:59）  
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0, 0);
      const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1, 0, 0, 0, -1); // 下一天的0点前1毫秒  
      getfeedersensing({}).then(res => {
        this.recordlist = res.res3;
        console.log("this.recordlist", this.recordlist);

        // 筛选当天时间范围内的记录  
        const todayRecords = this.recordlist.filter(record => {
          const recordTime = new Date(record.update_time); // 假设 record.timestamp 是时间戳字符串  
          return recordTime >= startOfDay && recordTime < endOfDay;
        });

        console.log('111111', todayRecords);
        // 计算 TOUSIJI1_FEED 到 TOUSIJI4_FEED 的总和 
        // console.log('TOUERJI1_FEED value:', todayRecords[0].TOUERJI1_FEED)
        let totalFeed = 0;
        if (todayRecords.length === 0) {
          console.log('没有当天记录，总饲料量设置为 0');
          totalFeed = 0; // 没有记录时，默认 totalFeed 为 0
        } else {
          todayRecords.forEach((record) => {
            const feed1 = parseFloat(record.TOUERJI1_FEED) || 0;
            const feed2 = parseFloat(record.TOUERJI2_FEED) || 0;
            const feed3 = parseFloat(record.TOUERJI3_FEED) || 0;
            const feed4 = parseFloat(record.TOUERJI4_FEED) || 0;

            // 累加饲料量
            totalFeed += feed1 + feed2 + feed3 + feed4;
          });
        }
        this.totalFeed = totalFeed;
        // 更新页面上的显示（假设有一个元素用于显示总和）  
        console.log('总量', totalFeed);
        this.getpeoplefeed();
      }).catch(error => {
        console.error('获取数据失败:', error);
      });
    },
  }

}
</script>
<template>

  <div class="w-100 h-100 d-flex flex-column  ">

    <div class="w-100 h-100 map-container position-absolute " id="map-container"></div>
    <img :src="bgImage" alt="" class="w-100 h-100 position-absolute top-0 left-0"
      style="pointer-events: none;">
    <div v-if="box === 1" class="equip-box">
      <!-- <img src="@/assets/ui/prompt.png" alt=""> -->
      <div class="text">
        <div class="info-table">
          <div v-for="(item, index) in miaoshu" :key="index" :class="['info-row', { 'full-width': index < 3 }]">
            <div class="info-label"><span>{{ item.label }}</span></div>
            <div class="info-value">{{ item.value }}</div>
          </div>
        </div>
      </div>
      <div class="close-icon d-flex align-items-center justify-content-center" @click="onCloseCover">
        <svg t="1639825855503" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
          p-id="26567" width="1rem" height="1rem">
          <path
            d="M846.005097 957.24155c-28.587082 0-57.174164-10.911514-78.996169-32.733519L96.632851 254.131955c-43.644009-43.644009-43.644009-114.348328 0-157.992337s114.348328-43.644009 157.992337 0L925.001265 766.515694c43.644009 43.644009 43.644009 114.348328 0 157.992337C903.17926 946.330036 874.592179 957.24155 846.005097 957.24155z"
            p-id="26568" fill="#ffffff"></path>
          <path
            d="M175.62902 957.24155c-28.587082 0-57.174164-10.911514-78.996169-32.733519-43.644009-43.644009-43.644009-114.348328 0-157.992337L767.008928 96.139617c43.644009-43.644009 114.348328-43.644009 157.992337 0s43.644009 114.348328 0 157.992337L254.625188 924.508032C232.803183 946.330036 204.216101 957.24155 175.62902 957.24155z"
            p-id="26569" fill="#ffffff"></path>
        </svg>
      </div>
    </div>
    <div class="header">
      <Head2 :isShow="true"></Head2>
    </div>
    <div class="main d-flex flex-row  ">
      <div class="home-sidebar  ">
        <div style="height: 64.8%;">
          <SubtitledSection title="设施列表">
            <template>
              <DeviceList2 :shebeilist="shelist" @link-shebei="handleLinkShebei"></DeviceList2>
            </template>
          </SubtitledSection>
        </div>
        <div style="height: 32.6%; ">
          <SubtitledSection title="控制面板">
            <template>
              <div class="control-panel">
                <div class="row img-box row-text" style="height: 40%;">
                  <div class="d-flex justify-content-between" style="width: 45%;">
                    <span style="line-height: 33px;color: #FFFFFF;">投饲机数量</span>
                    <div class="row-text1">
                      1
                    </div>
                  </div>
                </div>
                <div class="row img-box row-text" style="height: 40%;filter: hue-rotate(180deg);">
                  <div class="d-flex justify-content-between" style="width: 45%;">
                    <span style="line-height: 33px;color: #FFFFFF;">塘口数量</span>
                    <div class="row-text1">
                      1
                    </div>
                  </div>
                </div>
                <div class="row split " style="height: 20%;margin-top: 5px;cursor: pointer;">
                  <div class="half img-box3 row-text" style="color: #EE6E2A;" @click="showAddDialog()">
                    投饲设置
                  </div>
                  <div class="half img-box4 row-text" style="color:#FDEE21;">
                    读取参数
                  </div>
                </div>
              </div>
            </template>
          </SubtitledSection>
        </div>

      </div>
      <div class="home-center">
        <div class="info-box">
          <div v-for="(item, i) in wetherinfo" :key="i" class="info-item"
            :style="{ backgroundImage: 'url(' + item.img + ')' }">
            <div style=" margin-left: 3.4rem;">
              {{ item.value }}
            </div>
          </div>
        </div>
        <div class="bottom-box ">
          <div style="width:49.5% ;">
            <SubtitledSectionLong title=" ">
              <template>
                <div class="video-wrapper">
                  <Line2 :Data="dailyfeedlist"></Line2>
                </div>
              </template>
            </SubtitledSectionLong>
          </div>
          <div style="width: 49.5%;margin-left: 0.6rem;">
            <SubtitledSectionLong title=" ">
              <template>
                <div class="video-wrapper">
                  <Line2 :Data="planlist"></Line2>
                </div>
              </template>
            </SubtitledSectionLong>
          </div>
        </div>
      </div>
      <div class="home-sidebar ">
        <div style="height: 51.5%;">
          <SubtitledSection :title="tianjiarizhi" style="pointer-events: auto;">
            <template>
              <div class="h-100 w-100 log-form-container">
                <el-form :model="addForm" label-position="top" class="log-form">
                  <el-form-item label="时间">
                    <el-date-picker v-model="addForm.time" value-format="yyyy-MM-dd%20HH:mm" placeholder="选择日期时间">
                    </el-date-picker>
                  </el-form-item>

                  <div class="form-row">
                    <el-form-item label="姓名" class="half-width">
                      <el-input v-model="addForm.name"></el-input>
                    </el-form-item>
                    <el-form-item label="动作" class="half-width">
                      <el-select v-model="addForm.action" placeholder="请选择动作">
                        <el-option v-for="item in actionOptions" :key="item.value" :label="item.label"
                          :value="item.value">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </div>

                  <div class="form-row">
                    <el-form-item label="种类" class="half-width">
                      <el-input v-model="addForm.zhonglei"></el-input>
                    </el-form-item>
                    <el-form-item label="数量" class="half-width">
                      <el-input v-model="addForm.num"></el-input>
                    </el-form-item>
                  </div>

                  <el-form-item label="备注">
                    <el-input v-model="addForm.remark" :rows="2"></el-input>
                  </el-form-item>

                  <el-form-item>
                    <el-button type="primary" @click="addLog" class="submit-btn">提交</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </template>
          </SubtitledSection>
        </div>
        <div class="touwei">
          <div class="touwei-container">
            <div class="touwei-box">
              <img :src="touwei1Image" alt="" class="touwei-img">
              <div class="touwei-text">
                <div class="text-top">当天投喂量</div>
                <div class="text-bottom">{{ total_quantity }}kg</div>
              </div>
            </div>
            <div class="divider"></div>
            <div class="touwei-box">
              <img :src="touwei2Image" alt="" class="touwei-img">
              <div class="touwei-text">
                <div class="text-top">计划投喂量</div>
                <div class="text-bottom">{{ latestDailyFeedAmount }}kg</div>
              </div>
            </div>
          </div>
        </div>
        <div style="height: 32.6%; ">
          <SubtitledSection title="日志记录">
            <template>
              <LogList :baselist="loglist"></LogList>
            </template>
          </SubtitledSection>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="addDialogVisible" :modal="false" title="投饲设置" width="500px" :append-to-body="false"
      custom-class="custom-dialog">
      <el-form :model="newSettingsForm" label-width="80px" class="settings-form">
        <el-form-item label="塘 口 号：" prop="pond_number">
          <el-input v-model="newSettingsForm.pond_number" placeholder="请输入塘口号">
          </el-input>
        </el-form-item>

        <el-form-item label="投饲机号：" prop="feeder_number">
          <el-input v-model="newSettingsForm.feeder_number" placeholder="请输入投饲机号">
          </el-input>
        </el-form-item>

        <el-form-item label="鱼 数 量：" prop="fish_quantity">
          <el-input v-model="newSettingsForm.fish_quantity" placeholder="请输入鱼数量">
            <template slot="append" class="unit-box">条</template>
          </el-input>
        </el-form-item>

        <el-form-item label="鱼 规 格：" prop="fish_specification">
          <el-input v-model="newSettingsForm.fish_specification" placeholder="请输入鱼规格">
            <template slot="append" class="unit-box">g</template>
          </el-input>
        </el-form-item>

        <el-form-item label="饲料系数：" prop="feed_coefficient">
          <el-input v-model="newSettingsForm.feed_coefficient" placeholder="请输入饲料系数">
          </el-input>
        </el-form-item>

        <el-form-item label="日 饵 率：" prop="daily_feed_rate">
          <el-input v-model="newSettingsForm.daily_feed_rate" placeholder="请输入日饵率">
            <template slot="append" class="unit-box">%</template>
          </el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addNewSettings">确 定</el-button>
      </div>
    </el-dialog>
  </div>

</template>


<style lang="less" scoped>
.touwei {
  background: linear-gradient(0deg, rgba(16, 37, 76, 0.9) 0%, rgba(16, 37, 76, 0.6) 100%);
  padding: 20px;
  height: calc(11.5% - 40px);
  width: calc(100% - 40px - 16px) !important;
  margin: 0 8px;
  display: flex;

  .touwei-container {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
  }

  .touwei-box {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 0 15px;
  }

  .touwei-img {
    width: 55px;
    height: 55px;
    object-fit: contain;
  }

  .touwei-text {
    display: flex;
    flex-direction: column;
    gap: 5px;

    .text-top {
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 16px;
      color: #BDDBF1;
    }

    .text-bottom {

      font-family: PingFang SC;
      font-weight: 500;
      font-size: 22px;
      color: #FFFFFF;
      text-shadow: 0px 2px 5px rgba(158, 184, 218, 0.9);
    }
  }

  .divider {
    width: 2px;
    height: 60%;
    background: rgba(85, 150, 223, 0.3);
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

/deep/ .custom-dialog {
  background: rgba(4, 12, 24, 0.9);
  border: 1px solid #5596DF;
  box-shadow: 0 0 20px rgba(85, 150, 223, 0.3);
  border-radius: 6px;

  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(85, 150, 223, 0.3);

    .el-dialog__title {
      color: #BDDBF1;
      font-size: 18px;
      font-weight: 600;
    }
  }

  .el-dialog__body {
    padding: 20px;
  }

  .settings-form {
    .el-form-item__label {
      color: #BDDBF1;
      font-size: 14px;
    }

    .el-input__inner {
      background: rgba(1, 10, 20, 0.6);
      border: 1px solid #376293;
      color: #BDDBF1;

      &:focus {
        border-color: #5596DF;
      }

      &::placeholder {
        color: #376293;
      }
    }

    .el-input-group__append {
      background: rgba(1, 10, 20, 0.6);
      border: 1px solid #376293;
      color: #BDDBF1;
      width: 10px;
    }
  }

  .dialog-footer {
    text-align: center;
    padding: 10px 0;

    .el-button {
      width: 120px;
      height: 36px;
      margin: 0 10px;

      &.el-button--default {
        background: rgba(1, 10, 20, 0.6);
        border: 1px solid #376293;
        color: #BDDBF1;

        &:hover {
          border-color: #5596DF;
          color: #5596DF;
        }
      }

      &.el-button--primary {
        background: linear-gradient(0deg, #5074BC 0%, #2C5199 100%);
        border: 1px solid #5596DF;

        &:hover {
          opacity: 0.9;
        }
      }
    }
  }
}


/deep/ .el-button--primary {
  background: linear-gradient(0deg, #5074BC 0%, #2C5199 100%);
  border-radius: 3px;
  border: 1px solid #5596DF;
}

.log-form-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  // padding: 10px;
}

.log-form {
  display: flex;
  flex-direction: column;
  height: 100%;

  .el-form-item {
    margin-bottom: 5px;
    height: calc(100% / 5);
  }

  .form-row {
    display: flex;
    margin-bottom: 5px;
    justify-content: space-between;
    height: calc(100% / 5);

    .half-width {
      width: 48%;
    }
  }

  .submit-btn {
    margin-top: 1rem;
    width: 100%;
  }
}

/deep/ .el-form-item__label {
  padding: 0;
  line-height: 20px;
  color: #BDDBF1;
  font-family: PingFang SC;
  font-weight: 600;
  font-size: 16px;

}

/deep/ .el-input__inner,
/deep/ .el-textarea__inner,
/deep/ .el-input input {
  background: rgba(1, 10, 20, 0.6);
  border-radius: 3px;
  border: none;
  color: #BDDBF1;
  // height: 30%;
  // line-height: 30px;

  &::placeholder {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #376293;
  }
}

/deep/ .el-select .el-input__inner {
  &::-webkit-input-placeholder {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #376293;
  }
}


// 为 date picker 的占位符添加样式
/deep/ .el-date-editor.el-input .el-input__inner {
  &::-webkit-input-placeholder {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #376293;
  }
}

// 为 textarea 的占位符添加样式
/deep/ .el-textarea__inner {
  &::placeholder {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #376293;
  }
}

/deep/ .el-textarea__inner {
  height: 40px;
  resize: none;
}

/deep/ .el-button {
  height: 40px;
  line-height: 40px;
  padding: 0;
}

/deep/ .el-date-editor.el-input,
/deep/ .el-date-editor.el-input__inner {
  width: 100%;
}

/deep/ .el-select {
  width: 100%;
}

.row-text1 {
  width: 60px !important;
  height: 30px;
  background: linear-gradient(0deg, rgba(20, 192, 239, 0.4) 0%, rgba(20, 193, 239, 0) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(20, 193, 239, 1);
}

.row-text {
  font-family: PingFang SC;
  font-weight: 600;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;

}

.img-box {
  background-image: url('~@/assets/ui/control-bg4.png');
}

.img-box2 {
  background-image: url('~@/assets/ui/control-bg3.png');
}

.img-box3 {
  background-image: url('~@/assets/ui/control-bg2.png');
}

.img-box4 {
  background-image: url('~@/assets/ui/control-bg1.png');
}

.control-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.row {
  background-size: 100% 100%;
  background-repeat: no-repeat;
  // flex: 1;
  margin-bottom: 5px; // 行之间的间距，可以根据需要调整
}

.split {
  display: flex;
  justify-content: space-between;
}

.half {
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 49% !important; // 使用 49% 而不是 50% 来为两个 div 之间留出一点间隔
}

/deep/ .amap-icon {
  cursor: pointer !important;
}

.video-wrapper {
  pointer-events: auto;
  position: absolute;
  top: -2rem;
  width: calc(100% - 2.6rem);
  height: calc(100% - 0rem);
  padding: 0.5rem 1.3rem 1.3rem 1.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.video-content {
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-width: 100%;
  max-height: 100%;
}

::v-deep .amap-marker-label {
  border: 0;
  background-color: transparent;
  background-image: url('~@/assets/ui/position-text-bg3.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  font-family: PingFang SC;
  font-weight: 600;
  font-size: 18px;
  color: #FFFFFF;
  padding: 10px;
  margin-top: 5px;
  margin-left: 50px;
  text-shadow: 1px 1px 0 #000000, -1px -1px 0 #000000, 1px -1px 0 #000000, -1px 1px 0 #000000;
  -webkit-text-stroke: 0px; /* 移除原有的粗描边效果 */
}



//  地图设备提示框样式
.equip-box {
  position: absolute;
  top: 30%;
  left: 50%;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #F4ECC4;
  line-height: 28px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 22.2rem;
  // height: 60px;
  height: 13rem;
  background-image: url('~@/assets/ui/map-bg3.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  // height: 18rem;


  .text {
    position: absolute;
    top: 0;
    left: 0;
    padding: 1rem 1rem;
  }


  .info-table {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }

  .info-row {
    display: flex;
    width: 50%;
    margin-bottom: 0.5rem;

    &.full-width {
      width: 100%;
    }
  }

  .info-label {
    width: 5rem; // 设置固定宽度，可以根据需要调整
    margin-right: 0.5rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    overflow: hidden;

    &::after {
      content: ':';
      margin-left: 0.2rem;
    }
  }

  .info-label span {
    display: inline-block;
    width: 100%;
    text-align: justify;
    text-align-last: justify;
    letter-spacing: 0.1em; // 调整字间距以实现等距效果

    &::after {
      content: '';
      display: inline-block;
      width: 100%;
    }
  }

  .info-value {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .close-icon {
    position: absolute;
    top: -2.5rem;
    right: 0rem;
    width: 2.4rem;
    height: 2.4rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 0.3rem;
  }

  /*item框的高度*/
  .h-35 {
    height: 35%;
  }

  /*左下角设备描述字体*/

}

.header {
  width: 100%;
  height: 6rem;
}

.main {
  width: calc(100% - 2.7rem);
  height: calc(100% - 8.7rem);
  padding: 1.35rem;
  color: black;
  position: relative;
  pointer-events: none;

}

.home-sidebar {
  pointer-events: auto;
  height: 100%;
  // height: calc(100vh-11.4rem);
  width: 22.5%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  div {
    width: 100%;
  }

}

.home-center {
  height: 100%;
  width: 55%;
  position: relative;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  pointer-events: none;
}

.info-box {
  margin-top: 1.6rem;
  height: 5%;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.info-item {
  width: 12%;
  height: 100%;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  // justify-content: flex-end;
  align-items: center;
  font-family: PingFang SC;
  font-weight: 600;
  font-size: 20px;
  color: #FFFFFF;
  line-height: 41px;
}


.bottom-box {
  height: 32.6%;
  width: calc(100% - 1rem);
  margin-top: 1.5rem;
  position: absolute;
  display: flex;
  flex-direction: row;
  bottom: 0;

}

.box {
  background-image: url("~@/assets/ui/bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>
