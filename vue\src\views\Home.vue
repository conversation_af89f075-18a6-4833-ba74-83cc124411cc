<!-- 首页 -->
<script>
import SubtitledSection from '@/components/layout/SubtitledSection.vue'
import SubtitledSectionLong from '@/components/layout/SubtitledSectionLong.vue'
import CompanyIntroduction from '@/components/layout/CompanyIntroduction.vue'
import BaseList from '@/components/layout/BaseList.vue'
import DeviceDescription from '@/components/layout/DeviceDescription.vue'
import Head2 from '@/components/layout/Head2.vue'
import BreedingTypes from '@/components/charts/BreedingTypes/BreedingTypes.vue'
import BaseSize from '@/components/charts/BaseSize/BaseSize.vue'
import WaterQualityModule from '@/components/layout/WaterQualityModule.vue'
import { homelist, caicon, GetProductionRecords, Getbasic_data_fish_pond,select_equipmentv2,getcamera,getweather } from '@/request/api';
import axios from 'axios';
import EZUIKit from 'ezuikit-js';
export default {
  components: {
    SubtitledSection, //短标题框
    SubtitledSectionLong,//长标题框
    CompanyIntroduction, // 企业介绍
    BaseList,//基地列表
    DeviceDescription,//设备描述
    Head2,
    BreedingTypes,//养殖类型
    BaseSize,//基地规模
    WaterQualityModule,//水质模块
  },
  data() {
    return {
      // videoSrc1: '',
      // videoSrc2: '',
      videoSrc: require('@/assets/1.mp4'),
      //videoSrc2: 'http://**************:9000/static/video/2.mp4',
      videoSrc2: require('@/assets/2.mp4'),
      // 背景图片路径
      bgImage: require('@/assets/ui/bg.png'),
      promptImage: require('@/assets/ui/prompt.png'),
      jiankong1Image: require('@/assets/img/jiankong(1).png'),
      jiankong2Image: require('@/assets/img/jiankong(2).png'),
      videoAddresses: [],
      SerialNumber: [],
      video_address: [],
      cameralist: [],
      base_id: "",
      shelist: [],
      jktokens: {
        value: ''
      },
      shebeilist: {
        "water": [],
        "meteorological": [],
        "monitoring": []
      },
      box: 0,
      intervalId: null,
      baselist: [
        {
          "ID": 1,
          "BASE_NAME": "上海海洋大学养殖基地 ",
          "BASE_LOCATION": "121.89691,30.882894",
          "INTRODUCE": "上海海洋大学将利用学科优势，整合学校资源，以崇明为示范基地，探索河蟹湖泊化生态养殖新模式以及以种植为主的立体化养殖新模式、稻渔综合种养新模式、陆基工业化对虾养殖新模式，形成绿色种养产业综合体，为上海市乡村振兴提供示范。",
          "abbreviation": "shou"
        },
        {
          "ID": 2,
          "BASE_NAME": "常州金坛数字化渔场",
          "BASE_LOCATION": "119.481091,31.600419",
          "INTRODUCE": "金坛区隶属于江苏省常州市，是久负盛名的江南鱼米之乡。拥有\"长荡湖大闸蟹\"养殖基地，连续多次获得\"中国十大名蟹\"的荣誉称号。",
          "abbreviation": ""
        },
        {
          "ID": 3,
          "BASE_NAME": "上海裕安养殖场 ",
          "BASE_LOCATION": "121.83524,31.57505",
          "INTRODUCE": "陈家镇裕安养殖场\"渔光互补\"光伏发电项目，占地3180亩，电站总装机容量110兆瓦，是崇明区第一个\"渔光互补\"光伏发电项目，也是上海最大的渔光互补示范工程",
          "abbreviation": "yuan"
        }
      ],
      // wetherinfo: {
      //   'wendu': {
      //     img: 'top-info-wd.png',
      //     value: '0'
      //   },
      //   'shidu': {
      //     img: 'top-info-xy.png',
      //     value: '0'
      //   },
      //   'fengxiang': {
      //     img: 'top-info-ql.png',
      //     value: '0'
      //   },
      //   'fengsu': {
      //     img: 'top-info-fs.png',
      //     value: '0'
      //   },
      //   'guangzhao': {
      //     img: 'top-info-yg.png',
      //     value: '0'
      //   },
      //   'jiangyuliang': {
      //     img: 'top-info-sd.png',
      //     value: '0'
      //   },
      //   'qiya': {
      //     img: 'top-info-qy.png',
      //     value: '0'
      //   }
      // },
      wetherinfo1: {
        'temperature': {
          img: require('@/assets/ui/top-info-wd.png'),
          value: '0'
        },
        'humidity': {
          img: require('@/assets/ui/top-info-xy.png'),
          value: '0'
        },
        'wind_direction': {
          img: require('@/assets/ui/top-info-ql.png'),
          value: '0'
        },
        'wind_power': {
          img: require('@/assets/ui/top-info-fs.png'),
          value: '0'
        },
        'guangzhao': {
          img: require('@/assets/ui/top-info-yg.png'),
          value: '-'
        },
        'precipitation': {
          img: require('@/assets/ui/top-info-sd.png'),
          value: '-'
        },
        'pressure': {
          img: require('@/assets/ui/top-info-qy.png'),
          value: '-'
        } ,
      },
      city: '上海',  // 直接设置城市为 "上海"
      livetianqi: null,  // 存储天气数据
      alertlist: {
        tem: 0,
        ph: 0,
        o2: 0
      },
      optionsData: [],
      volumeDetailData: [
        { name: "养殖规模(亩)", data: [0, 0, 0, 0, 0, 0] },
        { name: "预期产量(吨)", data: [0, 0, 0, 0, 0, 0] },
      ],
    }
  },
  mounted() {
    this.renderMap();
    // this.GetCamera();
    this.getlist();
    this.startPolling(); // 开始定时请求数据
    // this.getToken();
    this.GetWeather();
  },
  methods: {
    // onEnded(v) {
    //   if (v == 'video1')
    //     this.$refs.video1.play()
    //   else
    //     this.$refs.video2.play()
    //   // this.$emit('ended')
    // },
    onCloseCover() {
      this.box = 0;
    },
    // initPlayer(url, SerialNumber) {
    //   if (!SerialNumber || !this.jktokens.value) {
    //     console.error('设备序列号或访问令牌未设置');
    //     return;
    //   }
    //   this.player = new EZUIKit.EZUIKitPlayer({
    //     id: 'video-container',
    //     accessToken: this.jktokens.value,
    //     url: url,
    //     height: '220',
    //     width: '450',
    //     template: 'simple',
    //     autoplay: true,  // 自动播放
    //     handleError: (e) => {  // 添加错误处理
    //       console.error('播放器错误:', e);
    //     }
    //   });
    // },
    // initPlayer1(url, SerialNumber) {
    //   if (!SerialNumber || !this.jktokens.value) {
    //     console.error('设备序列号或访问令牌未设置');
    //     return;
    //   }
    //   this.player1 = new EZUIKit.EZUIKitPlayer({
    //     id: 'video-container1',
    //     accessToken: this.jktokens.value,
    //     url: url,
    //     height: '220',
    //     width: '450',
    //     template: 'simple',
    //     autoplay: true,  // 自动播放
    //     handleError: (e) => {  // 添加错误处理
    //       console.error('播放器错误:', e);
    //     }
    //     // header: ['capturePicture', 'save', 'zoom'],  
    //     // footer: ['hd', 'fullScreen']  
    //   });
    // },
    // GetCamera() {
    //   this.base_id = '3',
    //   getcamera({
    //     base_id: this.base_id,
    //   }).then(res => {
    //     this.cameralist = res.item_list;
    //     const videoAddresses = this.cameralist.map(camera => camera.video_address);
    //     const SerialNumber = this.cameralist.map(camera => camera.serial_number);
    //     this.videoAddresses = videoAddresses;
    //     this.SerialNumber = SerialNumber;
    //     console.log('视频地址数组:', res);
    //     if (this.cameralist.length > 0) {
    //       this.shebeilist.monitoring = this.cameralist;
    //     }

    //     this.initPlayer(this.cameralist[0].video_address, this.SerialNumber[0]);
    //     this.initPlayer1(this.cameralist[1].video_address, this.SerialNumber[1]);
    //   })

    // },
    // async getToken() {
    //   try {
    //     const appKey = '3d0be5dc16b846e58ba2e4efb80d6d7f'; // 替换为你的appKey  
    //     const appSecret = '1d040ec6b1a4d12061fa97ef21987942'; // 替换为你的appSecret  

    //     const response = await axios.post('https://open.ys7.com/api/lapp/token/get',
    //       `appKey=${appKey}&appSecret=${appSecret}`,
    //       {
    //         headers: {
    //           'Content-Type': 'application/x-www-form-urlencoded'
    //         }
    //       }
    //     );
    //     // 处理响应数据  
    //     this.jktokens.value = response.data.data.accessToken;
    //     console.log('shuju:', response.data.data.accessToken); // 这里你应该会看到返回的token数据（如果请求成功的话）  
    //     //  this.jktokens=response.data.accessToken;
    //   } catch (error) {
    //     // 错误处理  
    //     console.error('获取Token失败:', error);
    //   }
    // },
    startPolling() {
      this.intervalId = setInterval(() => {
        this.getlist();
      }, 60000); // 每60秒（1分钟）调用一次getlist
    },
    GetWeather () {
      // 获取 base_id 参数（如果有的话，可以根据需求传入）
      getweather({
        city: this.city,
      }).then(res => {
        this.livetianqi = {
        temperature: res.temperature,  // 温度
        humidity: res.humidity,        // 湿度
        wind_direction: res.wind_direction, // 风向
        wind_power: res.wind_power,     // 风力    
        guangzhao: res.guangzhao, //光照
        precipitation: res.precipitation,   // 降水量
        pressure: res.pressure ,       // 气压
         };
         Object.keys(this.livetianqi).forEach(key => {
          if (Object.prototype.hasOwnProperty.call(this.wetherinfo1, key)) {
            this.wetherinfo1[key].value = this.livetianqi[key];  // 更新value字段
          }
        });
        console.log(this.wetherinfo1);
       })
       .catch(error => {
          // 处理错误并显示错误消息
          console.error('获取天气数据失败:', error);
          this.errorMessage = '获取天气数据失败，请稍后再试。';  // 清除已有的天气数据
        });
    },
    getlist() {
      Getbasic_data_fish_pond({}).then(res => {
        const result = {};
        console.log(res);
        res.ponds.forEach(item => {
          const base = item.base;
          const area = parseFloat(item.area) || 0;
          const total = parseFloat(item.total) / 1000 || 0; // 转换为吨

          if (!result[base]) {
            result[base] = {
              totalArea: 0,
              totalYield: 0
            };
          }

          // 累加每个base的面积和总量
          result[base].totalArea += area;
          result[base].totalYield += total;
        });

        // 将统计结果转换为所需的数组格式
        this.volumeDetailData = [
          {
            name: "养殖规模(亩)", data: [
              result['常州金坛数字化渔场'] ? result['常州金坛数字化渔场'].totalArea : 1000,
              result['宣城水产养殖基地'] ? result['宣城水产养殖基地'].totalArea : 1500,
              result['上海裕安养殖场'] ? result['上海裕安养殖场'].totalArea : 1300,
              result['竖新养殖基地'] ? result['竖新养殖基地'].totalArea : 1400,
              result['上海金山水产养殖场'] ? result['上海金山水产养殖场'].totalArea : 800,
              result['奉贤欣兴鲈鱼养殖基地'] ? result['奉贤欣兴鲈鱼养殖基地'].totalArea : 900
            ]
          },
          {
            name: "预期产量(吨)", data: [
              result['常州金坛数字化渔场'] ? result['常州金坛数字化渔场'].totalYield : 2100,
              result['宣城水产养殖基地'] ? result['宣城水产养殖基地'].totalYield : 1900,
              result['上海裕安养殖场'] ? result['上海裕安养殖场'].totalYield : 3000,
              result['竖新养殖基地'] ? result['竖新养殖基地'].totalYield : 1600,
              result['上海金山水产养殖场'] ? result['上海金山水产养殖场'].totalYield : 2500,
              result['奉贤欣兴鲈鱼养殖基地'] ? result['奉贤欣兴鲈鱼养殖基地'].totalYield : 3000
            ]
          }
        ];
        console.log(this.volumeDetailData);

      })
      GetProductionRecords({}).then(res => {
        const colorMap = [
          'rgba(179,229,251,1)',  // 蓝色
          'rgba(250,210,95,1)',   // 黄色
          'rgba(249,170,114,1)',  // 橙色
          'rgba(79,191,227,1)'    // 深蓝色
        ];
        const speciesSum = res.productionReports.reduce((acc, report) => {
          const species = report.species;
          const value = parseInt(report.seedQuantity, 10) || 0;

          if (!acc[species]) {
            acc[species] = 0;
          }
          acc[species] += value;
          return acc;
        }, {});

        // 转换为所需的格式并分配颜色
        this.optionsData = Object.entries(speciesSum).map(([species, value], index) => {
          return {
            name: species,
            value: value,
            itemStyle: {
              color: colorMap[index % colorMap.length] // 使用取模运算循环使用颜色
            }
          };
        }).sort((a, b) => b.value - a.value);

      })
      homelist({}).then(res => {
        this.baselist = res.data.baselist;
        this.updateMapMarkers();
        // let tianqi = res.data.tianqi;
        // Object.keys(tianqi).forEach(key => {
        //   if (Object.prototype.hasOwnProperty.call(this.wetherinfo, key)) {
        //     this.wetherinfo[key].value = parseFloat(parseFloat(tianqi[key]).toFixed(1));
        //   }
        // });
        if (res.data.alertlist.length > 0) {
          const firstAlert = res.data.alertlist[0];
          const describe = firstAlert.describe;
          // 使用正则表达式提取 TEM, PH, O2 的值
          const regex = /TEM:(\d+\.\d+) PH:(\d+\.\d+) O2:(\d+\.\d+)/;
        } else {
          console.error("alertlist 为空");
        }
      });
      //此处更改首页"水质参数"的设备
      select_equipmentv2({
        base_id: '3',
        equipmentID: 11
      }).then(res => {
        const data = res.data.shuju
        this.lineData = []
        this.alertlist.tem =data.templist[0].y[0].data.at(-1)
        this.alertlist.ph =  data.templist[2].y[0].data.at(-1)
        this.alertlist.o2 = data.templist[1].y[0].data.at(-1)
      })

    },
    renderMap() {// 渲染地图
      this.$AMapLoader.load({
        key: 'bb41d02b6376f70646e2490b6bf5f80b',
        version: '1.4.15',
        plugins: [],
        AMapUI: {
          version: '1.1',
          plugins: []
        },
        Loca: {
          version: '1.3.2'
        }
      }).then((AMap) => {
        this.AMap = AMap; // 将 AMap 赋值给组件的属性
        this.map = new AMap.Map('map-container', {
          mapStyle: 'amap://styles/whitesmoke',
          zoom: 8,
          layers: [new AMap.TileLayer.Satellite()],
          center: [120.559745, 31.29811]
        });
        this.updateMapMarkers();
      });
    },
    updateMapMarkers() {// 更新地图
      if (!this.map) return;
      // 清除之前的标记
      this.map.clearMap();
      this.baselist.forEach((item, index) => {
        const isSecondMarker = index === 0 || index === 2; // 判断是否是第二个标签
        const marker = new this.AMap.Marker({ // 使用 this.AMap
          position: item.BASE_LOCATION.split(',').map(parseFloat),
          label: {
            content: item.BASE_NAME,
            direction: isSecondMarker ? 'right' : 'center', // 第二个标签设为靠右
            offset: isSecondMarker ? new this.AMap.Pixel(0, -20) : new this.AMap.Pixel(0, -50), // 调整偏移以适应右侧
            style: {
              fontSize: '20',
              color: '#000',
            }
          },
          icon: new this.AMap.Icon({
            size: new this.AMap.Size(60, 50),
            image: require('@/assets/ui/position.png'),
            imageSize: new this.AMap.Size(60, 50)
          }),
          offset: new this.AMap.Pixel(-25, -55)
        });

        marker.on('click', () => {
          this.box = 1;     
          this.miaoshu = item.INTRODUCE;  // 更新描述框内容
          this.map.setCenter(item.BASE_LOCATION.split(',').map(coord => parseFloat(coord)));  // 定位到该标记
          this.$forceUpdate();
          
          // 等待DOM更新后调整详情框大小
          this.$nextTick(() => {
            this.adjustEquipBoxSize();
          });
        });
        this.map.add(marker);
      });
    },
    adjustEquipBoxSize() {
      const textElement = document.querySelector('.equip-box .adaptive-text');
      const bgElement = document.querySelector('.equip-box .adaptive-bg');
      
      if (textElement && bgElement) {
        // 创建一个临时元素来测量文本高度
        const tempDiv = document.createElement('div');
        tempDiv.style.cssText = `
          position: absolute;
          visibility: hidden;
          white-space: pre-wrap;
          font-family: PingFang SC;
          font-size: 16px;
          line-height: 1.6;
          padding: 2rem 2rem;
          box-sizing: border-box;
          width: ${textElement.offsetWidth}px;
          word-wrap: break-word;
          overflow-wrap: break-word;
          text-align: center;
        `;
        tempDiv.textContent = this.miaoshu;
        document.body.appendChild(tempDiv);
        
        // 计算所需高度，至少为最小高度
        const textHeight = Math.max(tempDiv.offsetHeight, 200); // 200px 最小高度
        const maxHeight = 400; // 最大高度限制
        const finalHeight = Math.min(textHeight, maxHeight);
        
        // 设置背景图片高度
        bgElement.style.height = finalHeight + 'px';
        
        // 清理临时元素
        document.body.removeChild(tempDiv);
      }
    }
  },
  beforeDestroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId); // 组件销毁时清除定时器
    }
  }
};
</script>
<template>

  <div class="w-100 h-100 d-flex flex-column  ">

    <div class="w-100 h-100 map-container position-absolute " id="map-container"></div>
    <img :src="bgImage" alt="" class="w-100 h-100 position-absolute top-0 left-0"
      style="pointer-events: none;">
    <div v-if="box === 1" class="equip-box">
      <img :src="promptImage" alt="" class="adaptive-bg">
      <div class="text adaptive-text" style="white-space: pre-wrap;">{{ miaoshu }}</div>
      <div class="close-icon d-flex align-items-center justify-content-center" @click="onCloseCover">
        <svg t="1639825855503" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
          p-id="26567" width="1rem" height="1rem">
          <path
            d="M846.005097 957.24155c-28.587082 0-57.174164-10.911514-78.996169-32.733519L96.632851 254.131955c-43.644009-43.644009-43.644009-114.348328 0-157.992337s114.348328-43.**********.992337 0L925.001265 766.515694c43.644009 43.644009 43.**********.348328 0 157.992337C903.17926 946.330036 874.592179 957.24155 846.005097 957.24155z"
            p-id="26568" fill="#ffffff"></path>
          <path
            d="M175.62902 957.24155c-28.587082 0-57.174164-10.911514-78.996169-32.733519-43.644009-43.644009-43.644009-114.348328 0-157.992337L767.008928 96.139617c43.644009-43.**********.348328-43.**********.992337 0s43.**********.348328 0 157.992337L254.**********.508032C232.**********.**********.**********.24155 175.62902 957.24155z"
            p-id="26569" fill="#ffffff"></path>
        </svg>
      </div>
    </div>
    <div class="header">
      <Head2></Head2>
    </div>
    <div class="main d-flex flex-row  ">
      <div class="home-sidebar  ">
        <div>
          <SubtitledSection title="企业介绍">
            <template>
              <CompanyIntroduction></CompanyIntroduction>
            </template>
          </SubtitledSection>
        </div>
        <div>
          <SubtitledSection title="基地列表">
            <template>
              <BaseList :baselist="baselist"></BaseList>
            </template>
          </SubtitledSection>
        </div>
        <div>
          <SubtitledSection title="设备描述">
            <template>
              <DeviceDescription></DeviceDescription>
            </template>
          </SubtitledSection>
        </div>

      </div>
      <div class="home-center">
        <div class="info-box">
          <div v-for="(item, i) in wetherinfo1" :key="i" class="info-item"
            :style="{ backgroundImage: 'url(' + item.img + ')' }">
            <div style=" margin-left: 3.4rem;">
              {{ item.value }}
            </div>

          </div>
        </div>
        <div class="bottom-box ">
          <div style="width: 49.5%;">
            <SubtitledSectionLong title="设备监控">
              <template>
                <div class="video-wrapper">
                  <div id="video-container" class="video-content">
                    <img :src="jiankong2Image" alt="视频占位图">
                  </div>
                </div>
              </template>
            </SubtitledSectionLong>
          </div>
          <div style="width: 49.5%;margin-left: 0.6rem;">
            <SubtitledSectionLong title="设备监控">
              <template>
                <div class="video-wrapper">
                  <div id="video-container" class="video-content">
                    <img :src="jiankong1Image" alt="视频占位图">
                  </div>
                </div>
              </template>
            </SubtitledSectionLong>
          </div>
        </div>
      </div>
      <div class="home-sidebar ">
        <div>
          <SubtitledSection title="养殖种类" style="pointer-events: auto;">
            <template>
              <BreedingTypes :Data="optionsData"></BreedingTypes>
            </template>
          </SubtitledSection>
        </div>
        <div>
          <SubtitledSection title="基地规模">
            <template>
              <BaseSize :volumeDetailData="volumeDetailData"></BaseSize>
            </template>
          </SubtitledSection>
        </div>
        <div>
          <SubtitledSection title="水质参数">
            <template>
              <WaterQualityModule :alertlist="alertlist"></WaterQualityModule>
            </template>
          </SubtitledSection>
        </div>
      </div>
    </div>
  </div>

</template>


<style lang="less" scoped>
.video-wrapper {
  pointer-events: auto;
  width: calc(100% - 2.6rem);
  height: calc(100% - 2.6rem);
  padding: 0.5rem 1.3rem 1.3rem 1.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.video-content {
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-width: 100%;
  max-height: 100%;
}

/deep/.amap-marker-label {
  border: 0;
  background-color: rgba(54, 74, 112, 0.4);
  padding: 10px;
  background-image: url('~@/assets/ui/position-text-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  font-family: var(--primary-font);
  font-weight: 600;
  font-size: 18px;
  color: #FFFFFF;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-shadow: 1px 1px 0 #000000, -1px -1px 0 #000000, 1px -1px 0 #000000, -1px 1px 0 #000000;
}


//  地图设备提示框样式
.equip-box {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #F4ECC4;
  line-height: 28px;
  min-width: 20rem;
  max-width: 35rem;
  display: flex;
  flex-direction: column;

  .adaptive-bg {
    width: 100%;
    height: auto;
    min-height: 12rem;
    max-height: 25rem;
    object-fit: fill;
    display: block;
    transition: height 0.3s ease;
  }

  .text {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 2rem 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    box-sizing: border-box;
  }

  .adaptive-text {
    font-size: 16px;
    line-height: 1.6;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  .close-icon {
    position: absolute;
    top: -2.5rem;
    right: 0rem;
    width: 2.4rem;
    height: 2.4rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 0.3rem;
  }

  /*item框的高度*/
  .h-35 {
    height: 35%;
  }

  /*左下角设备描述字体*/

}

.header {
  width: 100%;
  height: 6rem;
}

.main {
  width: calc(100% - 2.7rem);
  height: calc(100% - 8.7rem);
  padding: 1.35rem;
  color: black;
  position: relative;
  pointer-events: none;

}

.home-sidebar {
  pointer-events: auto;
  height: 100%;
  // height: calc(100vh-11.4rem);
  width: 22.5%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  div:nth-child(1) {
    height: 31%;
    width: 100%;
  }

  div:nth-child(2) {
    height: 31.3%;
    width: 100%;
    // margin-top: 5.6%;
  }

  div:nth-child(3) {
    height: 32.6%;
    width: 100%;
    // margin-top: 5.6%;
  }
}

.home-center {
  height: 100%;
  width: 55%;
  position: relative;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  pointer-events: none;
}

.info-box {
  margin-top: 1.6rem;
  height: 5%;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.info-item {
  width: 12%;
  height: 100%;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  // justify-content: flex-end;
  align-items: center;
  font-family: PingFang SC;
  font-weight: 600;
  font-size: 20px;
  color: #FFFFFF;
  line-height: 41px;
}


.bottom-box {
  height: 32.6%;
  width: calc(100% - 1rem);
  margin-top: 1.5rem;
  position: absolute;
  display: flex;
  flex-direction: row;
  bottom: 0;

}

.box {
  background-image: url("~@/assets/ui/bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>