/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-01-16 18:32:39
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-01-17 23:38:59
 * @FilePath: \vue\src\router\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import VueRouter from 'vue-router'
import { _isMobile } from '../utils/deviceDetect'
import { shouldRedirect, performDeviceRedirect, getLoginRoute, getHomeRoute } from '../utils/routeManager'

// PC端页面
import Login from '../views/Login2.vue'
import Home from '../views/Home.vue'
import Detail from '../views/Detail2.vue'
import Cultiver from '../views/Cultiver2.vue'
import historyData from '../views/data/history.vue'
import reportData from '../views/data/report.vue'
import deviceManage from '../views/device/manage.vue'
import deviceCamera from '../views/device/camera.vue'
import userAdd from '../views/user/add.vue'
import tousiplan from '../views/data/tousi.vue'

// 移动端页面
import MobileHome from '../views/mobile/Home.vue'
import MobileLogin from '../views/mobile/Login.vue'
import MobileRegister from '../views/mobile/Register.vue'
import MobileBaseList from '../views/mobile/BaseList.vue'
import MobileSectionTitlePage from '../views/mobile/SectionTitlePage.vue'

Vue.use(VueRouter)

// PC端路由配置
const pcRoutes = [
  {
    path: '/pc/login',
    name: 'PCLogin',
    component: Login,
    meta: { withMenu: false, platform: 'pc' }
  },
  {
    path: '/pc/home',
    name: 'PCHome',
    component: Home,
    meta: { withMenu: false, platform: 'pc' }
  },
  {
    path: '/pc/detail',
    name: 'PCDetail',
    component: Detail,
    meta: { withMenu: false, platform: 'pc' }
  },
  {
    path: '/pc/cultiver',
    name: 'PCCultiver',
    component: Cultiver,
    meta: { withMenu: false, platform: 'pc' }
  },
  {
    path: '/data/history',
    name: 'historyData',
    component: historyData,
    meta: { withMenu: true, pathName: '数据中心 / 历史数据', platform: 'pc' }
  },
  {
    path: '/data/report',
    name: 'reportData',
    component: reportData,
    meta: { withMenu: true, pathName: '数据中心 / 统计报表', platform: 'pc' }
  },
  {
    path: '/data/tousi',
    name: 'tousiplan',
    component: tousiplan,
    meta: { withMenu: true, pathName: '数据中心 / 投饲计划', platform: 'pc' }
  },
  {
    path: '/device/manage',
    name: 'deviceManage',
    component: deviceManage,
    meta: { withMenu: true, pathName: '设备中心 / 设备管理', platform: 'pc' }
  },
  {
    path: '/device/camera',
    name: 'deviceCamera',
    component: deviceCamera,
    meta: { withMenu: true, pathName: '设备中心 / 摄像头管理', platform: 'pc' }
  },
  {
    path: '/user/add',
    name: 'userAdd',
    component: userAdd,
    meta: { withMenu: true, pathName: '用户管理 / 新增用户', platform: 'pc' }
  },
  {
    path: '/register',
    name: 'register',
    component: userAdd,
    meta: { withMenu: false, platform: 'pc' }
  }
];

// 移动端路由配置
const mobileRoutes = [
  {
    path: '/mobile/login',
    name: 'MobileLogin',
    component: MobileLogin,
    meta: { platform: 'mobile' }
  },
  {
    path: '/mobile/home',
    name: 'MobileHome',
    component: MobileHome,
    meta: { requiresAuth: true, platform: 'mobile' }
  },
  {
    path: '/mobile/register',
    name: 'MobileRegister',
    component: MobileRegister,
    meta: { platform: 'mobile' }
  },
  {
    path: '/mobile/list',
    name: 'MobileBaseList',
    component: MobileBaseList,
    meta: { requiresAuth: true, platform: 'mobile' }
  },
  {
    path: '/mobile/detail',
    name: 'MobileDeviceList',
    component: () => import('../components/mobile/DeviceList.vue'),
    meta: { requiresAuth: true, platform: 'mobile' }
  },
  {
    path: '/mobile/device-detail',
    name: 'MobileDeviceDetail',
    component: () => import('../components/mobile/DeviceDetail.vue'),
    meta: { platform: 'mobile' }
  },
  {
    path: '/mobile/section-title',
    name: 'MobileSectionTitlePage',
    component: MobileSectionTitlePage,
    meta: { platform: 'mobile' }
  }
];

const router = new VueRouter({
  mode: 'history',
  routes: [
    {
      path: '/',
      redirect: () => {
        const isMobile = _isMobile()
        return getLoginRoute(isMobile)
      }
    },
    // 兼容旧路由
    {
      path: '/login',
      redirect: () => {
        const isMobile = _isMobile()
        return getLoginRoute(isMobile)
      }
    },
    {
      path: '/home',
      redirect: () => {
        const isMobile = _isMobile()
        return getHomeRoute(isMobile)
      }
    },
    {
      path: '/detail',
      redirect: () => {
        const isMobile = _isMobile()
        return isMobile ? '/mobile/detail' : '/pc/detail'
      }
    },
    {
      path: '/cultiver',
      redirect: () => {
        const isMobile = _isMobile()
        return isMobile ? '/mobile/home' : '/pc/cultiver'
      }
    },
    ...pcRoutes,
    ...mobileRoutes,
    // 404页面处理
    {
      path: '*',
      redirect: () => {
        const isMobile = _isMobile()
        return getHomeRoute(isMobile)
      }
    }
  ]
});

// 防抖函数，避免频繁的路由跳转
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 添加全局导航守卫
router.beforeEach(async (to, from, next) => {
  const isMobile = _isMobile()
  const currentPath = to.path
  
  // 检查是否需要设备跳转
  if (shouldRedirect(currentPath, isMobile)) {
    try {
      // 执行设备跳转，保留查询参数
      await performDeviceRedirect(router, currentPath, isMobile, to.query)
      return // 跳转已执行，不需要继续
    } catch (error) {
      console.error('设备跳转失败:', error)
    }
  }
  
  // 检查认证
  if (to.matched.some(record => record.meta.requiresAuth)) {
    const token = localStorage.getItem('token')
    const userInfo = localStorage.getItem('userInfo')

    if (!token || !userInfo) {
      // 未登录，重定向到对应平台的登录页面
      const loginPath = getLoginRoute(isMobile)
      next({
        path: loginPath,
        query: { redirect: to.fullPath }
      })
      return
    }
  }
  
  next()
})

// 路由跳转完成后的处理
router.afterEach((to, from) => {
  // 记录页面访问日志
  console.log(`路由跳转: ${from.path} -> ${to.path}`)
  
  // 更新页面标题
  if (to.meta && to.meta.pathName) {
    document.title = to.meta.pathName
  }
})

export default router
